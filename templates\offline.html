{% extends 'base.html' %}
{% load static %}

{% block title %}Offline - ZBet{% endblock %}

{% block extra_css %}
<style>
.offline-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    color: white;
}

.offline-content {
    max-width: 400px;
    width: 100%;
}

.offline-icon {
    font-size: 4rem;
    color: #ffd700;
    margin-bottom: 24px;
    animation: pulse 2s infinite;
}

.offline-title {
    font-size: 2rem;
    color: #ffd700;
    margin-bottom: 16px;
    font-weight: bold;
}

.offline-message {
    font-size: 1.1rem;
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 32px;
}

.offline-features {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
    text-align: left;
}

.offline-features h3 {
    color: #ffd700;
    font-size: 1.2rem;
    margin-bottom: 16px;
    text-align: center;
}

.offline-feature-list {
    list-style: none;
    padding: 0;
}

.offline-feature-list li {
    padding: 8px 0;
    color: #ccc;
    display: flex;
    align-items: center;
}

.offline-feature-list li i {
    color: #00a651;
    margin-right: 12px;
    width: 20px;
}

.offline-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.offline-btn {
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.offline-btn-primary {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1a1a2e;
}

.offline-btn-primary:hover {
    background: linear-gradient(135deg, #ffed4e 0%, #fff59d 100%);
    color: #1a1a2e;
    transform: translateY(-2px);
}

.offline-btn-secondary {
    background: transparent;
    color: #ffd700;
    border: 2px solid #ffd700;
}

.offline-btn-secondary:hover {
    background: #ffd700;
    color: #1a1a2e;
    transform: translateY(-2px);
}

.connection-status {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
}

.connection-status.online {
    background: #28a745;
}

.offline-tips {
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-top: 24px;
}

.offline-tips h4 {
    color: #ffd700;
    font-size: 1rem;
    margin-bottom: 12px;
}

.offline-tips ul {
    list-style: none;
    padding: 0;
    color: #ccc;
    font-size: 0.9rem;
}

.offline-tips li {
    padding: 4px 0;
    display: flex;
    align-items: flex-start;
}

.offline-tips li::before {
    content: "💡";
    margin-right: 8px;
    margin-top: 2px;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@media (max-width: 767px) {
    .offline-container {
        padding: 16px;
    }
    
    .offline-title {
        font-size: 1.5rem;
    }
    
    .offline-message {
        font-size: 1rem;
    }
    
    .offline-actions {
        gap: 12px;
    }
    
    .offline-btn {
        padding: 14px 20px;
        font-size: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Connection Status Indicator -->
<div class="connection-status" id="connection-status">
    <i class="fas fa-wifi-slash"></i>
    <span>You're offline</span>
</div>

<div class="offline-container">
    <div class="offline-content">
        <!-- Offline Icon -->
        <div class="offline-icon">
            <i class="fas fa-cloud-slash"></i>
        </div>
        
        <!-- Offline Title -->
        <h1 class="offline-title">You're Offline</h1>
        
        <!-- Offline Message -->
        <p class="offline-message">
            No internet connection detected. Don't worry, you can still access some features of ZBet while offline.
        </p>
        
        <!-- Available Offline Features -->
        <div class="offline-features">
            <h3><i class="fas fa-check-circle"></i> Available Offline</h3>
            <ul class="offline-feature-list">
                <li>
                    <i class="fas fa-eye"></i>
                    View cached sports matches and odds
                </li>
                <li>
                    <i class="fas fa-history"></i>
                    Check your betting history
                </li>
                <li>
                    <i class="fas fa-user"></i>
                    View your account information
                </li>
                <li>
                    <i class="fas fa-gift"></i>
                    Browse promotions and bonuses
                </li>
                <li>
                    <i class="fas fa-gamepad"></i>
                    Play offline casino games
                </li>
                <li>
                    <i class="fas fa-save"></i>
                    Queue bets for when you're back online
                </li>
            </ul>
        </div>
        
        <!-- Action Buttons -->
        <div class="offline-actions">
            <button class="offline-btn offline-btn-primary" onclick="checkConnection()">
                <i class="fas fa-sync-alt"></i>
                Check Connection
            </button>
            
            <a href="/" class="offline-btn offline-btn-secondary">
                <i class="fas fa-home"></i>
                Go to Homepage
            </a>
            
            <button class="offline-btn offline-btn-secondary" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
                Go Back
            </button>
        </div>
        
        <!-- Offline Tips -->
        <div class="offline-tips">
            <h4>Tips for Better Offline Experience</h4>
            <ul>
                <li>Install the ZBet app for better offline functionality</li>
                <li>Your queued bets will be placed automatically when connection returns</li>
                <li>Cached content is updated every time you're online</li>
                <li>Enable notifications to know when you're back online</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Connection status monitoring
function updateConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    const isOnline = navigator.onLine;
    
    if (isOnline) {
        statusElement.innerHTML = '<i class="fas fa-wifi"></i><span>Back online!</span>';
        statusElement.classList.add('online');
        
        // Redirect to homepage after a short delay
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
    } else {
        statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i><span>You\'re offline</span>';
        statusElement.classList.remove('online');
    }
}

// Check connection manually
function checkConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
    button.disabled = true;
    
    // Try to fetch a small resource to test connection
    fetch('/static/manifest.json', { 
        method: 'HEAD',
        cache: 'no-cache'
    })
    .then(response => {
        if (response.ok) {
            button.innerHTML = '<i class="fas fa-check"></i> Connected!';
            button.classList.remove('offline-btn-primary');
            button.classList.add('offline-btn-secondary');
            
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        } else {
            throw new Error('No connection');
        }
    })
    .catch(() => {
        button.innerHTML = '<i class="fas fa-times"></i> Still offline';
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);
    });
}

// Go back to previous page
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '/';
    }
}

// Listen for connection changes
window.addEventListener('online', updateConnectionStatus);
window.addEventListener('offline', updateConnectionStatus);

// Initial status check
document.addEventListener('DOMContentLoaded', updateConnectionStatus);

// Periodic connection check
setInterval(() => {
    if (!navigator.onLine) {
        // Try to detect if we're actually online but navigator.onLine is wrong
        fetch('/static/manifest.json', { 
            method: 'HEAD',
            cache: 'no-cache'
        })
        .then(response => {
            if (response.ok && !navigator.onLine) {
                // We're actually online, update status
                updateConnectionStatus();
            }
        })
        .catch(() => {
            // Still offline
        });
    }
}, 10000); // Check every 10 seconds

// Service Worker message handling
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'OFFLINE_READY') {
            console.log('Offline functionality ready');
        }
    });
}

// Show install prompt if available
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    
    // Show install suggestion
    const installTip = document.createElement('div');
    installTip.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        background: #ffd700;
        color: #1a1a2e;
        padding: 16px;
        border-radius: 12px;
        font-weight: bold;
        text-align: center;
        z-index: 1001;
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    `;
    installTip.innerHTML = `
        <i class="fas fa-download"></i> 
        Install ZBet app for better offline experience
        <button onclick="this.parentElement.remove()" style="background: none; border: none; color: #1a1a2e; font-size: 1.2rem; float: right; cursor: pointer;">×</button>
    `;
    
    installTip.addEventListener('click', () => {
        e.prompt();
        e.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                installTip.remove();
            }
        });
    });
    
    document.body.appendChild(installTip);
    
    // Remove after 10 seconds
    setTimeout(() => {
        if (installTip.parentElement) {
            installTip.remove();
        }
    }, 10000);
});
</script>
{% endblock %}
