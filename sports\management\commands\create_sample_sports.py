from django.core.management.base import BaseCommand
from django.utils.text import slugify
from sports.models import Sport, Country
from decimal import Decimal


class Command(BaseCommand):
    help = 'Create sample sports data for the ZBet platform'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample sports data...'))
        
        # Create sample countries first
        countries_data = [
            {'name': 'United Kingdom', 'code': 'GBR', 'flag_emoji': '🇬🇧'},
            {'name': 'United States', 'code': 'USA', 'flag_emoji': '🇺🇸'},
            {'name': 'Spain', 'code': 'ESP', 'flag_emoji': '🇪🇸'},
            {'name': 'Germany', 'code': 'DEU', 'flag_emoji': '🇩🇪'},
            {'name': 'France', 'code': 'FRA', 'flag_emoji': '🇫🇷'},
            {'name': 'Italy', 'code': 'ITA', 'flag_emoji': '🇮🇹'},
            {'name': 'Brazil', 'code': 'BRA', 'flag_emoji': '🇧🇷'},
            {'name': 'Argentina', 'code': 'ARG', 'flag_emoji': '🇦🇷'},
        ]
        
        for country_data in countries_data:
            country, created = Country.objects.get_or_create(
                name=country_data['name'],
                defaults={
                    'code': country_data['code'],
                    'flag_emoji': country_data['flag_emoji'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created country: {country.name}')
        
        # Create sample sports
        sports_data = [
            {
                'name': 'Football',
                'description': 'The world\'s most popular sport. Bet on leagues from around the globe.',
                'icon': 'futbol',
                'is_featured': True,
                'display_order': 1,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('50000.00'),
            },
            {
                'name': 'Basketball',
                'description': 'Fast-paced action from NBA, EuroLeague, and more.',
                'icon': 'basketball-ball',
                'is_featured': True,
                'display_order': 2,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('25000.00'),
            },
            {
                'name': 'Tennis',
                'description': 'Grand Slams, ATP, WTA tournaments and more.',
                'icon': 'table-tennis',
                'is_featured': True,
                'display_order': 3,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('20000.00'),
            },
            {
                'name': 'American Football',
                'description': 'NFL, college football, and championship games.',
                'icon': 'football-ball',
                'is_featured': True,
                'display_order': 4,
                'min_bet_amount': Decimal('10.00'),
                'max_bet_amount': Decimal('30000.00'),
            },
            {
                'name': 'Baseball',
                'description': 'MLB, World Series, and international baseball.',
                'icon': 'baseball-ball',
                'is_featured': True,
                'display_order': 5,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('15000.00'),
            },
            {
                'name': 'Ice Hockey',
                'description': 'NHL, international tournaments, and league play.',
                'icon': 'hockey-puck',
                'is_featured': True,
                'display_order': 6,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('20000.00'),
            },
            {
                'name': 'Boxing',
                'description': 'Championship fights, title bouts, and major events.',
                'icon': 'fist-raised',
                'is_featured': False,
                'display_order': 7,
                'min_bet_amount': Decimal('10.00'),
                'max_bet_amount': Decimal('25000.00'),
            },
            {
                'name': 'Mixed Martial Arts',
                'description': 'UFC, Bellator, and other MMA promotions.',
                'icon': 'fist-raised',
                'is_featured': False,
                'display_order': 8,
                'min_bet_amount': Decimal('10.00'),
                'max_bet_amount': Decimal('25000.00'),
            },
            {
                'name': 'Golf',
                'description': 'PGA Tour, majors, and international tournaments.',
                'icon': 'golf-ball',
                'is_featured': False,
                'display_order': 9,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('15000.00'),
            },
            {
                'name': 'Cricket',
                'description': 'Test matches, ODIs, T20s, and domestic leagues.',
                'icon': 'baseball-ball',
                'is_featured': False,
                'display_order': 10,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('20000.00'),
            },
            {
                'name': 'Rugby',
                'description': 'Rugby Union, Rugby League, and international competitions.',
                'icon': 'football-ball',
                'is_featured': False,
                'display_order': 11,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('15000.00'),
            },
            {
                'name': 'Volleyball',
                'description': 'Indoor and beach volleyball competitions.',
                'icon': 'volleyball-ball',
                'is_featured': False,
                'display_order': 12,
                'min_bet_amount': Decimal('5.00'),
                'max_bet_amount': Decimal('10000.00'),
            },
        ]
        
        created_count = 0
        updated_count = 0
        
        for sport_data in sports_data:
            slug = slugify(sport_data['name'])
            sport, created = Sport.objects.get_or_create(
                slug=slug,
                defaults={
                    'name': sport_data['name'],
                    'description': sport_data['description'],
                    'icon': sport_data['icon'],
                    'is_active': True,
                    'is_featured': sport_data['is_featured'],
                    'display_order': sport_data['display_order'],
                    'min_bet_amount': sport_data['min_bet_amount'],
                    'max_bet_amount': sport_data['max_bet_amount'],
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'Created sport: {sport.name}')
            else:
                # Update existing sport
                sport.name = sport_data['name']
                sport.description = sport_data['description']
                sport.icon = sport_data['icon']
                sport.is_featured = sport_data['is_featured']
                sport.display_order = sport_data['display_order']
                sport.min_bet_amount = sport_data['min_bet_amount']
                sport.max_bet_amount = sport_data['max_bet_amount']
                sport.save()
                updated_count += 1
                self.stdout.write(f'Updated sport: {sport.name}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} sports and updated {updated_count} sports.'
            )
        )
        
        # Display summary
        total_sports = Sport.objects.count()
        featured_sports = Sport.objects.filter(is_featured=True).count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Total sports in database: {total_sports} ({featured_sports} featured)'
            )
        )
