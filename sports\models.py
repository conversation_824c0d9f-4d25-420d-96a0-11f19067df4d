from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal


class Sport(models.Model):
    """
    Sports categories (Football, Basketball, Tennis, etc.)
    """
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="Font Awesome icon class")

    # Display settings
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    display_order = models.PositiveIntegerField(default=0)

    # Betting settings
    min_bet_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('10.00'))
    max_bet_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('100000.00'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sports_sport'
        ordering = ['display_order', 'name']
        verbose_name = 'Sport'
        verbose_name_plural = 'Sports'

    def __str__(self):
        return self.name

    @property
    def active_leagues_count(self):
        """Get count of active leagues for this sport"""
        return self.leagues.filter(is_active=True).count()

    @property
    def upcoming_matches_count(self):
        """Get count of upcoming matches for this sport"""
        return Match.objects.filter(
            league__sport=self,
            status='scheduled',
            start_time__gte=timezone.now()
        ).count()


class Country(models.Model):
    """
    Countries for leagues and teams
    """
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=3, unique=True, help_text="ISO 3166-1 alpha-3 code")
    flag_emoji = models.CharField(max_length=10, blank=True)

    # Display settings
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'sports_country'
        ordering = ['name']
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name


class League(models.Model):
    """
    Sports leagues/competitions (Premier League, NBA, etc.)
    """
    LEAGUE_TYPE_CHOICES = [
        ('domestic', 'Domestic League'),
        ('international', 'International Competition'),
        ('cup', 'Cup Competition'),
        ('friendly', 'Friendly'),
    ]

    name = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    short_name = models.CharField(max_length=50, blank=True)

    # Relationships
    sport = models.ForeignKey(Sport, on_delete=models.CASCADE, related_name='leagues')
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='leagues')

    # League details
    league_type = models.CharField(max_length=20, choices=LEAGUE_TYPE_CHOICES, default='domestic')
    season = models.CharField(max_length=20, help_text="e.g., 2024/25")
    description = models.TextField(blank=True)

    # Display settings
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    display_order = models.PositiveIntegerField(default=0)

    # External data
    external_id = models.CharField(max_length=100, blank=True, help_text="External API ID")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sports_league'
        ordering = ['sport', 'display_order', 'name']
        verbose_name = 'League'
        verbose_name_plural = 'Leagues'
        unique_together = ['sport', 'name', 'season']

    def __str__(self):
        return f"{self.name} ({self.season})"

    @property
    def active_teams_count(self):
        """Get count of active teams in this league"""
        return self.teams.filter(is_active=True).count()

    @property
    def upcoming_matches_count(self):
        """Get count of upcoming matches in this league"""
        return self.matches.filter(
            status='scheduled',
            start_time__gte=timezone.now()
        ).count()


class Team(models.Model):
    """
    Sports teams
    """
    name = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    short_name = models.CharField(max_length=50, blank=True)

    # Relationships
    sport = models.ForeignKey(Sport, on_delete=models.CASCADE, related_name='teams')
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='teams')
    leagues = models.ManyToManyField(League, related_name='teams', blank=True)

    # Team details
    founded_year = models.PositiveIntegerField(null=True, blank=True)
    stadium = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100, blank=True)

    # Media
    logo = models.ImageField(upload_to='teams/logos/', blank=True, null=True)

    # Display settings
    is_active = models.BooleanField(default=True)

    # External data
    external_id = models.CharField(max_length=100, blank=True, help_text="External API ID")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sports_team'
        ordering = ['name']
        verbose_name = 'Team'
        verbose_name_plural = 'Teams'
        unique_together = ['sport', 'name']

    def __str__(self):
        return self.name

    @property
    def recent_matches(self):
        """Get recent matches for this team"""
        return Match.objects.filter(
            models.Q(home_team=self) | models.Q(away_team=self),
            status='finished'
        ).order_by('-start_time')[:5]

    @property
    def upcoming_matches(self):
        """Get upcoming matches for this team"""
        return Match.objects.filter(
            models.Q(home_team=self) | models.Q(away_team=self),
            status='scheduled',
            start_time__gte=timezone.now()
        ).order_by('start_time')[:5]


class Match(models.Model):
    """
    Sports matches/games
    """
    MATCH_STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('live', 'Live'),
        ('halftime', 'Half Time'),
        ('finished', 'Finished'),
        ('postponed', 'Postponed'),
        ('cancelled', 'Cancelled'),
        ('suspended', 'Suspended'),
    ]

    # Basic match information
    home_team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='home_matches')
    away_team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='away_matches')
    league = models.ForeignKey(League, on_delete=models.CASCADE, related_name='matches')

    # Match details
    start_time = models.DateTimeField()
    status = models.CharField(max_length=20, choices=MATCH_STATUS_CHOICES, default='scheduled')
    round_number = models.PositiveIntegerField(null=True, blank=True)
    venue = models.CharField(max_length=200, blank=True)

    # Score information
    home_score = models.PositiveIntegerField(null=True, blank=True)
    away_score = models.PositiveIntegerField(null=True, blank=True)

    # Additional score details (for sports with periods/sets)
    score_details = models.JSONField(default=dict, blank=True, help_text="Detailed score breakdown")

    # Match statistics (basic JSON data)
    stats_data = models.JSONField(default=dict, blank=True, help_text="Basic match statistics data")

    # Live match data
    minute = models.PositiveIntegerField(null=True, blank=True, help_text="Current minute")
    period = models.CharField(max_length=20, blank=True, help_text="Current period/half")

    # External data
    external_id = models.CharField(max_length=100, blank=True, help_text="External API ID")

    # Betting settings
    betting_enabled = models.BooleanField(default=True)
    live_betting_enabled = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sports_match'
        ordering = ['start_time']
        verbose_name = 'Match'
        verbose_name_plural = 'Matches'
        unique_together = ['home_team', 'away_team', 'start_time']

    def __str__(self):
        return f"{self.home_team} vs {self.away_team} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"

    @property
    def is_live(self):
        """Check if match is currently live"""
        return self.status in ['live', 'halftime']

    @property
    def is_finished(self):
        """Check if match is finished"""
        return self.status == 'finished'

    @property
    def is_upcoming(self):
        """Check if match is upcoming"""
        return self.status == 'scheduled' and self.start_time > timezone.now()

    @property
    def result_display(self):
        """Get formatted result display"""
        if self.home_score is not None and self.away_score is not None:
            return f"{self.home_score} - {self.away_score}"
        return "vs"

    @property
    def winner(self):
        """Get match winner"""
        if not self.is_finished or self.home_score is None or self.away_score is None:
            return None

        if self.home_score > self.away_score:
            return self.home_team
        elif self.away_score > self.home_score:
            return self.away_team
        else:
            return "draw"

    def clean(self):
        """Validate match data"""
        from django.core.exceptions import ValidationError

        if self.home_team == self.away_team:
            raise ValidationError("Home team and away team cannot be the same.")

        if self.home_team.sport != self.away_team.sport:
            raise ValidationError("Both teams must be from the same sport.")

        if self.league.sport != self.home_team.sport:
            raise ValidationError("League sport must match team sports.")


class MatchEvent(models.Model):
    """
    Events that occur during a match (goals, cards, substitutions, etc.)
    """
    EVENT_TYPE_CHOICES = [
        ('goal', 'Goal'),
        ('own_goal', 'Own Goal'),
        ('penalty_goal', 'Penalty Goal'),
        ('yellow_card', 'Yellow Card'),
        ('red_card', 'Red Card'),
        ('substitution', 'Substitution'),
        ('corner', 'Corner'),
        ('offside', 'Offside'),
        ('foul', 'Foul'),
        ('penalty_miss', 'Penalty Miss'),
        ('var_decision', 'VAR Decision'),
        ('injury', 'Injury'),
        ('timeout', 'Timeout'),
    ]

    match = models.ForeignKey(Match, on_delete=models.CASCADE, related_name='events')
    team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='match_events')

    # Event details
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES)
    minute = models.PositiveIntegerField()
    period = models.CharField(max_length=20, default='1st Half')

    # Player information (if applicable)
    player_name = models.CharField(max_length=200, blank=True)
    player_2_name = models.CharField(max_length=200, blank=True, help_text="For substitutions")

    # Event description
    description = models.TextField(blank=True)

    # Additional data
    metadata = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'sports_match_event'
        ordering = ['minute', 'created_at']
        verbose_name = 'Match Event'
        verbose_name_plural = 'Match Events'

    def __str__(self):
        return f"{self.match} - {self.get_event_type_display()} ({self.minute}')"


class MatchStatistics(models.Model):
    """
    Comprehensive match statistics
    """
    match = models.OneToOneField(Match, on_delete=models.CASCADE, related_name='statistics')

    # General statistics
    total_goals = models.PositiveIntegerField(default=0)
    total_corners = models.PositiveIntegerField(default=0)
    total_cards = models.PositiveIntegerField(default=0)
    total_fouls = models.PositiveIntegerField(default=0)
    total_offsides = models.PositiveIntegerField(default=0)

    # Home team statistics
    home_possession = models.PositiveIntegerField(default=0, help_text="Possession percentage")
    home_shots = models.PositiveIntegerField(default=0)
    home_shots_on_target = models.PositiveIntegerField(default=0)
    home_corners = models.PositiveIntegerField(default=0)
    home_fouls = models.PositiveIntegerField(default=0)
    home_yellow_cards = models.PositiveIntegerField(default=0)
    home_red_cards = models.PositiveIntegerField(default=0)
    home_offsides = models.PositiveIntegerField(default=0)
    home_passes = models.PositiveIntegerField(default=0)
    home_pass_accuracy = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Away team statistics
    away_possession = models.PositiveIntegerField(default=0, help_text="Possession percentage")
    away_shots = models.PositiveIntegerField(default=0)
    away_shots_on_target = models.PositiveIntegerField(default=0)
    away_corners = models.PositiveIntegerField(default=0)
    away_fouls = models.PositiveIntegerField(default=0)
    away_yellow_cards = models.PositiveIntegerField(default=0)
    away_red_cards = models.PositiveIntegerField(default=0)
    away_offsides = models.PositiveIntegerField(default=0)
    away_passes = models.PositiveIntegerField(default=0)
    away_pass_accuracy = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Advanced statistics (JSON for flexibility)
    advanced_stats = models.JSONField(default=dict, blank=True, help_text="Additional sport-specific statistics")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sports_match_statistics'
        verbose_name = 'Match Statistics'
        verbose_name_plural = 'Match Statistics'

    def __str__(self):
        return f"Statistics for {self.match}"

    @property
    def total_shots(self):
        """Get total shots in match"""
        return self.home_shots + self.away_shots

    @property
    def total_shots_on_target(self):
        """Get total shots on target in match"""
        return self.home_shots_on_target + self.away_shots_on_target

    def update_from_events(self):
        """Update statistics from match events"""
        events = self.match.events.all()

        # Reset counters
        self.home_corners = self.away_corners = 0
        self.home_yellow_cards = self.away_yellow_cards = 0
        self.home_red_cards = self.away_red_cards = 0
        self.home_fouls = self.away_fouls = 0
        self.home_offsides = self.away_offsides = 0

        for event in events:
            if event.team == self.match.home_team:
                if event.event_type == 'corner':
                    self.home_corners += 1
                elif event.event_type == 'yellow_card':
                    self.home_yellow_cards += 1
                elif event.event_type == 'red_card':
                    self.home_red_cards += 1
                elif event.event_type == 'foul':
                    self.home_fouls += 1
                elif event.event_type == 'offside':
                    self.home_offsides += 1
            else:
                if event.event_type == 'corner':
                    self.away_corners += 1
                elif event.event_type == 'yellow_card':
                    self.away_yellow_cards += 1
                elif event.event_type == 'red_card':
                    self.away_red_cards += 1
                elif event.event_type == 'foul':
                    self.away_fouls += 1
                elif event.event_type == 'offside':
                    self.away_offsides += 1

        # Update totals
        self.total_corners = self.home_corners + self.away_corners
        self.total_cards = (self.home_yellow_cards + self.away_yellow_cards +
                           self.home_red_cards + self.away_red_cards)
        self.total_fouls = self.home_fouls + self.away_fouls
        self.total_offsides = self.home_offsides + self.away_offsides

        self.save()


class PlayerStatistics(models.Model):
    """
    Player statistics for a specific match
    """
    match = models.ForeignKey(Match, on_delete=models.CASCADE, related_name='player_stats')
    team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='player_stats')

    # Player details
    player_name = models.CharField(max_length=200)
    jersey_number = models.PositiveIntegerField(null=True, blank=True)
    position = models.CharField(max_length=50, blank=True)

    # Playing time
    minutes_played = models.PositiveIntegerField(default=0)
    is_starter = models.BooleanField(default=False)
    substituted_in = models.PositiveIntegerField(null=True, blank=True)
    substituted_out = models.PositiveIntegerField(null=True, blank=True)

    # Performance statistics
    goals = models.PositiveIntegerField(default=0)
    assists = models.PositiveIntegerField(default=0)
    shots = models.PositiveIntegerField(default=0)
    shots_on_target = models.PositiveIntegerField(default=0)
    passes = models.PositiveIntegerField(default=0)
    pass_accuracy = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    tackles = models.PositiveIntegerField(default=0)
    interceptions = models.PositiveIntegerField(default=0)
    fouls_committed = models.PositiveIntegerField(default=0)
    fouls_suffered = models.PositiveIntegerField(default=0)
    yellow_cards = models.PositiveIntegerField(default=0)
    red_cards = models.PositiveIntegerField(default=0)

    # Advanced statistics (sport-specific)
    advanced_stats = models.JSONField(default=dict, blank=True)

    # Rating
    rating = models.DecimalField(max_digits=3, decimal_places=1, null=True, blank=True)

    class Meta:
        db_table = 'sports_player_statistics'
        ordering = ['team', 'jersey_number', 'player_name']
        verbose_name = 'Player Statistics'
        verbose_name_plural = 'Player Statistics'
        unique_together = ['match', 'team', 'player_name']

    def __str__(self):
        return f"{self.player_name} - {self.match}"


class TeamSeasonStatistics(models.Model):
    """
    Team statistics for a season
    """
    team = models.ForeignKey(Team, on_delete=models.CASCADE, related_name='season_stats')
    league = models.ForeignKey(League, on_delete=models.CASCADE, related_name='team_stats')
    season = models.CharField(max_length=20)

    # Match statistics
    matches_played = models.PositiveIntegerField(default=0)
    wins = models.PositiveIntegerField(default=0)
    draws = models.PositiveIntegerField(default=0)
    losses = models.PositiveIntegerField(default=0)

    # Goal statistics
    goals_for = models.PositiveIntegerField(default=0)
    goals_against = models.PositiveIntegerField(default=0)

    # Points and position
    points = models.PositiveIntegerField(default=0)
    position = models.PositiveIntegerField(null=True, blank=True)

    # Form (last 5 matches)
    form = models.CharField(max_length=5, blank=True, help_text="W/D/L for last 5 matches")

    # Advanced statistics
    clean_sheets = models.PositiveIntegerField(default=0)
    failed_to_score = models.PositiveIntegerField(default=0)
    average_possession = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sports_team_season_statistics'
        ordering = ['league', '-points', '-goals_for']
        verbose_name = 'Team Season Statistics'
        verbose_name_plural = 'Team Season Statistics'
        unique_together = ['team', 'league', 'season']

    def __str__(self):
        return f"{self.team} - {self.league} {self.season}"

    @property
    def goal_difference(self):
        """Calculate goal difference"""
        return self.goals_for - self.goals_against

    @property
    def win_percentage(self):
        """Calculate win percentage"""
        if self.matches_played == 0:
            return 0
        return round((self.wins / self.matches_played) * 100, 1)

    def update_from_match(self, match, is_home=True):
        """Update statistics from a completed match"""
        if match.status != 'finished':
            return

        self.matches_played += 1

        if is_home:
            team_score = match.home_score or 0
            opponent_score = match.away_score or 0
        else:
            team_score = match.away_score or 0
            opponent_score = match.home_score or 0

        self.goals_for += team_score
        self.goals_against += opponent_score

        # Determine result
        if team_score > opponent_score:
            self.wins += 1
            self.points += 3
            result = 'W'
        elif team_score == opponent_score:
            self.draws += 1
            self.points += 1
            result = 'D'
        else:
            self.losses += 1
            result = 'L'

        # Update form (keep last 5)
        if len(self.form) >= 5:
            self.form = self.form[1:] + result
        else:
            self.form += result

        # Update clean sheets
        if opponent_score == 0:
            self.clean_sheets += 1

        # Update failed to score
        if team_score == 0:
            self.failed_to_score += 1

        self.save()
