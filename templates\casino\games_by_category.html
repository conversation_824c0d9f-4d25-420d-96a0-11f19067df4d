{% extends 'base.html' %}
{% load static %}

{% block title %}{{ category.name }} Games - ZBet Casino{% endblock %}

{% block extra_css %}
<style>
.category-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 40px 0;
    text-align: center;
    color: white;
}

.category-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

.category-description {
    font-size: 1.1rem;
    color: #aaa;
    max-width: 600px;
    margin: 0 auto;
}

.filters-section {
    background: #1e1e2e;
    padding: 20px 0;
    border-bottom: 1px solid #333;
}

.filters-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-label {
    color: #aaa;
    font-weight: bold;
}

.filter-select {
    background: #333;
    color: white;
    border: 1px solid #555;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.search-box {
    background: #333;
    color: white;
    border: 1px solid #555;
    padding: 8px 12px;
    border-radius: 6px;
    width: 250px;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    padding: 30px 0;
}

.game-card {
    background: #1e1e2e;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #333;
    position: relative;
}

.game-card:hover {
    transform: translateY(-8px);
    border-color: #ffd700;
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.2);
}

.game-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.game-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-card:hover .game-image img {
    transform: scale(1.1);
}

.game-placeholder {
    height: 180px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffd700;
    font-size: 3rem;
}

.game-badges {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.badge-featured {
    background: #ffd700;
    color: #1a1a2e;
}

.badge-new {
    background: #00ff88;
    color: #1a1a2e;
}

.badge-popular {
    background: #ff6b6b;
    color: white;
}

.badge-jackpot {
    background: #ff9500;
    color: white;
}

.game-info {
    padding: 20px;
}

.game-title {
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 8px;
    line-height: 1.3;
}

.game-provider {
    color: #888;
    font-size: 0.9rem;
    margin-bottom: 12px;
}

.game-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 15px;
    font-size: 0.8rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    color: #aaa;
}

.stat-value {
    color: #ffd700;
    font-weight: bold;
}

.game-description {
    color: #bbb;
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.play-buttons {
    display: flex;
    gap: 10px;
}

.btn-play {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-play-real {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
}

.btn-play-real:hover {
    background: linear-gradient(45deg, #ffed4e, #fff59d);
    color: #1a1a2e;
    transform: translateY(-2px);
}

.btn-play-demo {
    background: transparent;
    color: #ffd700;
    border: 2px solid #ffd700;
}

.btn-play-demo:hover {
    background: #ffd700;
    color: #1a1a2e;
    transform: translateY(-2px);
}

.no-games {
    text-align: center;
    padding: 60px 20px;
    color: #aaa;
}

.no-games-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #555;
}

.breadcrumb {
    background: #1e1e2e;
    padding: 15px 0;
    border-bottom: 1px solid #333;
}

.breadcrumb-nav {
    color: #aaa;
    font-size: 0.9rem;
}

.breadcrumb-nav a {
    color: #ffd700;
    text-decoration: none;
}

.breadcrumb-nav a:hover {
    text-decoration: underline;
}

.results-count {
    color: #aaa;
    font-size: 0.9rem;
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .search-box {
        width: 100%;
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .category-title {
        font-size: 2rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<div class="breadcrumb">
    <div class="container">
        <nav class="breadcrumb-nav">
            <a href="{% url 'casino:home' %}">Casino</a> 
            <span class="mx-2">›</span> 
            <span>{{ category.name }}</span>
        </nav>
    </div>
</div>

<!-- Category Header -->
<div class="category-header">
    <div class="container">
        <h1 class="category-title">{{ category.icon }} {{ category.name }}</h1>
        {% if category.description %}
        <p class="category-description">{{ category.description }}</p>
        {% endif %}
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section">
    <div class="container">
        <form method="get" class="filters-container">
            <div class="filter-group">
                <label class="filter-label">Provider:</label>
                <select name="provider" class="filter-select" onchange="this.form.submit()">
                    <option value="">All Providers</option>
                    {% for provider in providers %}
                    <option value="{{ provider.id }}" {% if current_provider == provider.id|stringformat:"s" %}selected{% endif %}>
                        {{ provider.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <input type="text" name="search" class="search-box" 
                       placeholder="Search games..." 
                       value="{{ search_query|default:'' }}"
                       onkeypress="if(event.key==='Enter') this.form.submit()">
            </div>
        </form>
    </div>
</div>

<!-- Games Grid -->
<div class="container">
    {% if games %}
    <div class="results-count">
        Showing {{ games|length }} game{{ games|length|pluralize }} in {{ category.name }}
    </div>
    
    <div class="games-grid">
        {% for game in games %}
        <div class="game-card">
            <div class="game-image">
                {% if game.thumbnail %}
                <img src="{{ game.thumbnail.url }}" alt="{{ game.name }}">
                {% else %}
                <div class="game-placeholder">
                    {% if game.game_type == 'SLOT' %}🎰
                    {% elif game.game_type == 'BLACKJACK' %}🃏
                    {% elif game.game_type == 'ROULETTE' %}🎯
                    {% elif game.game_type == 'AVIATOR' %}✈️
                    {% else %}🎮
                    {% endif %}
                </div>
                {% endif %}
                
                <div class="game-badges">
                    {% if game.is_featured %}
                    <span class="badge badge-featured">Featured</span>
                    {% endif %}
                    {% if game.is_new %}
                    <span class="badge badge-new">New</span>
                    {% endif %}
                    {% if game.is_popular %}
                    <span class="badge badge-popular">Popular</span>
                    {% endif %}
                    {% if game.has_progressive_jackpot %}
                    <span class="badge badge-jackpot">Jackpot</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="game-info">
                <h3 class="game-title">{{ game.name }}</h3>
                <div class="game-provider">{{ game.provider.name }}</div>
                
                <div class="game-stats">
                    <div class="stat-item">
                        <span>RTP:</span>
                        <span class="stat-value">{{ game.rtp_percentage }}%</span>
                    </div>
                    <div class="stat-item">
                        <span>Volatility:</span>
                        <span class="stat-value">{{ game.volatility }}</span>
                    </div>
                    <div class="stat-item">
                        <span>Min Bet:</span>
                        <span class="stat-value">KES {{ game.min_bet }}</span>
                    </div>
                    <div class="stat-item">
                        <span>Plays:</span>
                        <span class="stat-value">{{ game.play_count }}</span>
                    </div>
                </div>
                
                {% if game.description %}
                <div class="game-description">{{ game.description }}</div>
                {% endif %}
                
                <div class="play-buttons">
                    <a href="{% url 'casino:launch_game' game.slug %}" class="btn-play btn-play-real">
                        Play Real
                    </a>
                    {% if game.has_demo_mode %}
                    <a href="{% url 'casino:launch_game' game.slug %}?demo=true" class="btn-play btn-play-demo">
                        Demo
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="no-games">
        <div class="no-games-icon">🎮</div>
        <h3>No games found</h3>
        <p>
            {% if search_query %}
            No games match your search "{{ search_query }}" in {{ category.name }}.
            {% elif current_provider %}
            No games found for the selected provider in {{ category.name }}.
            {% else %}
            No games available in {{ category.name }} at the moment.
            {% endif %}
        </p>
        <a href="{% url 'casino:home' %}" class="btn-play btn-play-real" style="display: inline-block; margin-top: 20px;">
            Browse All Games
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
