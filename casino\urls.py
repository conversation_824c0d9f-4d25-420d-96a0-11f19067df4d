from django.urls import path
from . import views

app_name = 'casino'

urlpatterns = [
    # Main casino pages
    path('', views.casino_home, name='home'),
    path('category/<slug:category_slug>/', views.games_by_category, name='games_by_category'),
    path('game/<slug:game_slug>/', views.game_detail, name='game_detail'),
    path('play/<slug:game_slug>/', views.launch_game, name='launch_game'),
    
    # Game API endpoints
    path('api/place-bet/', views.place_bet, name='place_bet'),
    path('api/end-session/<str:session_token>/', views.end_game_session, name='end_session'),
    
    # Jackpot endpoints
    path('api/jackpots/', views.jackpot_display, name='jackpot_display'),
    path('jackpot-winners/', views.jackpot_winners, name='jackpot_winners'),
]
