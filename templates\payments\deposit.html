{% extends 'base.html' %}
{% load static %}

{% block title %}Deposit Funds - ZBet{% endblock %}

{% block extra_css %}
<style>
    .payment-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .payment-method-card {
        border: 2px solid #2d3748;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #1a1a2e;
    }
    
    .payment-method-card:hover {
        border-color: #4299e1;
        background: #2d3748;
    }
    
    .payment-method-card.selected {
        border-color: #4299e1;
        background: #2d3748;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
    
    .mpesa-logo {
        width: 40px;
        height: 40px;
        background: #00a651;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
    }
    
    .amount-input {
        background: #2d3748;
        border: 2px solid #4a5568;
        border-radius: 8px;
        color: white;
        padding: 12px 16px;
        font-size: 18px;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .amount-input:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
    
    .phone-input {
        background: #2d3748;
        border: 2px solid #4a5568;
        border-radius: 8px;
        color: white;
        padding: 12px 16px;
        width: 100%;
        margin-bottom: 15px;
    }
    
    .phone-input:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
    
    .deposit-btn {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 15px 30px;
        font-size: 16px;
        font-weight: 600;
        width: 100%;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .deposit-btn:hover {
        background: linear-gradient(135deg, #3182ce, #2c5282);
        transform: translateY(-2px);
    }
    
    .deposit-btn:disabled {
        background: #4a5568;
        cursor: not-allowed;
        transform: none;
    }
    
    .wallet-balance {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .balance-amount {
        font-size: 32px;
        font-weight: bold;
        color: #4299e1;
        margin-bottom: 5px;
    }
    
    .quick-amounts {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        margin-bottom: 20px;
    }
    
    .quick-amount-btn {
        background: #2d3748;
        border: 1px solid #4a5568;
        border-radius: 8px;
        color: white;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .quick-amount-btn:hover {
        background: #4a5568;
        border-color: #4299e1;
    }
    
    .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 10px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background: rgba(72, 187, 120, 0.1);
        border: 1px solid #48bb78;
        color: #48bb78;
    }
    
    .alert-error {
        background: rgba(245, 101, 101, 0.1);
        border: 1px solid #f56565;
        color: #f56565;
    }
    
    .alert-info {
        background: rgba(66, 153, 225, 0.1);
        border: 1px solid #4299e1;
        color: #4299e1;
    }
</style>
{% endblock %}

{% block content %}
<div class="payment-container">
    <h1 class="text-3xl font-bold text-white mb-6">Deposit Funds</h1>
    
    <!-- Wallet Balance -->
    <div class="wallet-balance">
        <div class="text-gray-300 mb-2">Current Balance</div>
        <div class="balance-amount" id="current-balance">KES {{ wallet.balance|floatformat:2 }}</div>
        <div class="text-xs text-gray-400 mt-1" id="balance-updated" style="display: none;">
            Balance updated! 🎉
        </div>
    </div>
    
    <!-- Alert Messages -->
    <div id="alert-container"></div>
    
    <!-- Deposit Form -->
    <form id="deposit-form">
        {% csrf_token %}
        
        <!-- Payment Method Selection -->
        <div class="mb-6">
            <label class="block text-white text-lg font-semibold mb-4">Select Payment Method</label>
            {% for method in payment_methods %}
            <div class="payment-method-card" data-method="{{ method.code }}">
                <div class="flex items-center">
                    <div class="mpesa-logo mr-4">
                        {% if method.code == 'MPESA' %}
                            M-P
                        {% elif method.code == 'AIRTEL' %}
                            AM
                        {% else %}
                            {{ method.code|slice:":2" }}
                        {% endif %}
                    </div>
                    <div>
                        <div class="text-white font-semibold">{{ method.name }}</div>
                        <div class="text-gray-400 text-sm">{{ method.description }}</div>
                    </div>
                    <div class="ml-auto">
                        <input type="radio" name="payment_method" value="{{ method.code }}" class="hidden">
                        <div class="w-6 h-6 border-2 border-gray-400 rounded-full flex items-center justify-center">
                            <div class="w-3 h-3 bg-blue-500 rounded-full hidden"></div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Amount Input -->
        <div class="mb-6">
            <label class="block text-white text-lg font-semibold mb-4">Enter Amount (KES)</label>
            
            <!-- Quick Amount Buttons -->
            <div class="quick-amounts">
                <button type="button" class="quick-amount-btn" data-amount="100">100</button>
                <button type="button" class="quick-amount-btn" data-amount="500">500</button>
                <button type="button" class="quick-amount-btn" data-amount="1000">1,000</button>
                <button type="button" class="quick-amount-btn" data-amount="2000">2,000</button>
                <button type="button" class="quick-amount-btn" data-amount="5000">5,000</button>
                <button type="button" class="quick-amount-btn" data-amount="10000">10,000</button>
            </div>
            
            <input type="number" name="amount" id="amount" class="amount-input" 
                   placeholder="Enter amount" min="10" max="100000" required>
            <div class="text-gray-400 text-sm">Minimum: KES 10 | Maximum: KES 100,000</div>
        </div>
        
        <!-- Phone Number Input (shown when M-Pesa is selected) -->
        <div id="phone-section" class="mb-6" style="display: none;">
            <label class="block text-white text-lg font-semibold mb-2">M-Pesa Phone Number</label>
            <input type="tel" name="phone_number" id="phone_number" class="phone-input" 
                   placeholder="e.g., 0712345678" pattern="[0-9]{10}">
            <div class="text-gray-400 text-sm">Enter your M-Pesa registered phone number</div>
        </div>
        
        <!-- Submit Button -->
        <button type="submit" id="deposit-btn" class="deposit-btn" disabled>
            <div class="loading-spinner"></div>
            <span id="btn-text">Select Payment Method</span>
        </button>
    </form>
    
    <!-- Transaction Status Modal -->
    <div id="status-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div id="status-content">
                <!-- Status content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('deposit-form');
    const paymentCards = document.querySelectorAll('.payment-method-card');
    const phoneSection = document.getElementById('phone-section');
    const depositBtn = document.getElementById('deposit-btn');
    const btnText = document.getElementById('btn-text');
    const loadingSpinner = document.querySelector('.loading-spinner');
    const alertContainer = document.getElementById('alert-container');
    const statusModal = document.getElementById('status-modal');
    
    let selectedMethod = null;
    let currentTransactionId = null;
    
    // Payment method selection
    paymentCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove previous selection
            paymentCards.forEach(c => {
                c.classList.remove('selected');
                c.querySelector('input[type="radio"]').checked = false;
                c.querySelector('.w-3').classList.add('hidden');
            });
            
            // Select current card
            this.classList.add('selected');
            this.querySelector('input[type="radio"]').checked = true;
            this.querySelector('.w-3').classList.remove('hidden');
            
            selectedMethod = this.dataset.method;
            
            // Show/hide phone section for M-Pesa
            if (selectedMethod === 'MPESA') {
                phoneSection.style.display = 'block';
                btnText.textContent = 'Deposit via M-Pesa';
            } else {
                phoneSection.style.display = 'none';
                btnText.textContent = `Deposit via ${this.querySelector('.text-white').textContent}`;
            }
            
            depositBtn.disabled = false;
            updateButtonState();
        });
    });
    
    // Quick amount buttons
    document.querySelectorAll('.quick-amount-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('amount').value = this.dataset.amount;
            updateButtonState();
        });
    });
    
    // Amount input validation
    document.getElementById('amount').addEventListener('input', updateButtonState);
    document.getElementById('phone_number').addEventListener('input', updateButtonState);
    
    function updateButtonState() {
        const amount = document.getElementById('amount').value;
        const phone = document.getElementById('phone_number').value;
        
        if (selectedMethod && amount && (selectedMethod !== 'MPESA' || phone)) {
            depositBtn.disabled = false;
        } else {
            depositBtn.disabled = true;
        }
    }
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!selectedMethod) {
            showAlert('Please select a payment method', 'error');
            return;
        }
        
        const formData = new FormData(form);
        
        // Show loading state
        depositBtn.disabled = true;
        loadingSpinner.style.display = 'inline-block';
        btnText.textContent = 'Processing...';
        
        fetch('{% url "payments:deposit" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                currentTransactionId = data.transaction_id;
                
                if (selectedMethod === 'MPESA') {
                    showAlert(data.message, 'info');
                    showStatusModal(data.transaction_id);
                    startStatusPolling(data.transaction_id);
                } else {
                    showAlert(data.message, 'success');
                }
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            loadingSpinner.style.display = 'none';
            btnText.textContent = selectedMethod === 'MPESA' ? 'Deposit via M-Pesa' : 'Deposit';
            depositBtn.disabled = false;
        });
    });
    
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        alertContainer.innerHTML = '';
        alertContainer.appendChild(alertDiv);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
    
    function showStatusModal(transactionId) {
        const content = `
            <h3 class="text-xl font-bold text-white mb-4">Payment Status</h3>
            <div class="text-center">
                <div class="loading-spinner mx-auto mb-4" style="display: block;"></div>
                <p class="text-gray-300 mb-4">Waiting for M-Pesa payment confirmation...</p>
                <p class="text-sm text-gray-400">Transaction ID: ${transactionId}</p>
            </div>
            <div class="mt-6 flex justify-end">
                <button onclick="closeStatusModal()" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                    Close
                </button>
            </div>
        `;
        
        document.getElementById('status-content').innerHTML = content;
        statusModal.classList.remove('hidden');
        statusModal.classList.add('flex');
    }
    
    window.closeStatusModal = function() {
        statusModal.classList.add('hidden');
        statusModal.classList.remove('flex');
    };

    // Function to update wallet balance in real-time
    function updateWalletBalance() {
        fetch('{% url "payments:wallet_balance" %}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const balanceElement = document.getElementById('current-balance');
                const updatedElement = document.getElementById('balance-updated');

                // Animate balance update
                balanceElement.style.transform = 'scale(1.1)';
                balanceElement.style.color = '#48bb78';
                balanceElement.textContent = data.formatted_balance;

                // Show update indicator
                updatedElement.style.display = 'block';

                // Reset animation
                setTimeout(() => {
                    balanceElement.style.transform = 'scale(1)';
                    balanceElement.style.color = '#4299e1';
                    updatedElement.style.display = 'none';
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Error updating balance:', error);
        });
    }
    
    function startStatusPolling(transactionId) {
        const pollInterval = setInterval(() => {
            fetch(`/payments/transaction/status/${transactionId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    if (data.transaction_status === 'COMPLETED') {
                        clearInterval(pollInterval);
                        showAlert('Payment completed successfully!', 'success');
                        closeStatusModal();
                        // Update balance immediately
                        updateWalletBalance();
                        // Refresh page to show updated balance
                        setTimeout(() => location.reload(), 2000);
                    } else if (data.transaction_status === 'FAILED' || data.transaction_status === 'CANCELLED') {
                        clearInterval(pollInterval);
                        showAlert('Payment was not completed', 'error');
                        closeStatusModal();
                    }
                }
            })
            .catch(error => {
                console.error('Status polling error:', error);
            });
        }, 3000); // Poll every 3 seconds
        
        // Stop polling after 5 minutes
        setTimeout(() => {
            clearInterval(pollInterval);
        }, 300000);
    }
});
</script>
{% endblock %}
