{% extends 'base.html' %}

{% block title %}Sports Betting - ZBet{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: #0f1419 !important;
        color: #ffffff;
    }
    .sidebar {
        background-color: #1a1a2e;
        min-height: 100vh;
        padding: 0;
    }
    .sidebar .nav-link {
        color: #ffffff;
        padding: 12px 20px;
        border-radius: 0;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        border-bottom: 1px solid #2d3748;
    }
    .sidebar .nav-link:hover, .sidebar .nav-link.active {
        background-color: #16213e;
        color: #ffc107;
    }
    .sidebar .nav-link i {
        margin-right: 10px;
        width: 20px;
    }
    .main-content {
        background-color: #0f1419;
        min-height: 100vh;
        color: #ffffff;
    }
    .promo-banner {
        background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
        border-radius: 10px;
        margin-bottom: 20px;
        color: #000;
        font-weight: bold;
    }
    .match-card {
        background-color: #1e2329;
        border: 1px solid #2d3748;
        border-radius: 8px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    .match-card:hover {
        border-color: #ffc107;
        transform: translateY(-2px);
    }
    .odds-btn {
        background-color: #2d3748;
        border: 1px solid #4a5568;
        color: #ffffff;
        min-width: 60px;
        padding: 8px 12px;
        border-radius: 4px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    .odds-btn:hover {
        background-color: #ffc107;
        color: #000000;
        border-color: #ffc107;
    }
    .betslip-panel {
        background-color: #1a1a2e;
        border-left: 1px solid #2d3748;
        min-height: 100vh;
    }
    .section-header {
        color: #ffc107;
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 1.1em;
    }
    .team-name {
        font-weight: 500;
        color: #ffffff;
    }
    .match-time {
        color: #a0aec0;
        font-size: 0.9em;
    }
    .league-name {
        color: #718096;
        font-size: 0.85em;
        margin-bottom: 5px;
    }
    .navbar-dark {
        background-color: #1a1a2e !important;
    }
    .container-fluid {
        padding: 0 !important;
    }
    .top-nav {
        background-color: #1a1a2e;
        border-bottom: 1px solid #2d3748;
        padding: 10px 0;
    }
    .top-nav .nav-link {
        color: #ffffff;
        margin: 0 15px;
        padding: 8px 15px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }
    .top-nav .nav-link:hover, .top-nav .nav-link.active {
        background-color: #ffc107;
        color: #000000;
    }
    .highlights-section {
        background-color: #16213e;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- Top Navigation -->
    <div class="top-nav">
        <div class="container-fluid">
            <nav class="nav justify-content-center">
                <a class="nav-link active" href="#">Home</a>
                <a class="nav-link" href="{% url 'betting:live_matches' %}">Live ({{ sports.count }})</a>
                <a class="nav-link" href="#">Jackpots</a>
                <a class="nav-link" href="#">Aviator</a>
                <a class="nav-link" href="#">Casino</a>
                <a class="nav-link" href="#">Promotions</a>
                <a class="nav-link" href="{% url 'betting:virtual_sports' %}">Virtuals</a>
                <a class="nav-link" href="#">Live Score</a>
            </nav>
        </div>
    </div>

    <div class="row g-0">
        <!-- Left Sidebar -->
        <div class="col-md-2 sidebar">
            <div class="p-3">
                <h6 class="text-white mb-3">
                    <i class="fas fa-futbol"></i> Soccer
                </h6>
                <nav class="nav flex-column">
                    {% for sport in sports %}
                    <a class="nav-link {% if forloop.first %}active{% endif %}" href="{% url 'betting:sport_matches' sport.slug %}">
                        {% if sport.icon %}
                            <i class="{{ sport.icon }}"></i>
                        {% else %}
                            <i class="fas fa-futbol"></i>
                        {% endif %}
                        {{ sport.name }}
                    </a>
                    {% endfor %}
                </nav>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="col-md-7 main-content p-4">
            <!-- Promotional Banner -->
            <div class="promo-banner p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">CASH IN BEFORE YOUR TEAM CRASHES OUT!</h3>
                        <p class="mb-0">Top the weekly leaderboard using SHARE TA</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <h2 class="mb-0">KES 1,000,000</h2>
                    </div>
                </div>
            </div>

            <!-- Match Listings -->
            <div class="highlights-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="section-header">Highlights</div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-light">Today</button>
                        <button class="btn btn-sm btn-outline-light">Highlights</button>
                        <button class="btn btn-sm btn-outline-light">1x2</button>
                    </div>
                </div>

                <!-- Sample Matches -->
                <div class="match-card p-3">
                    <div class="league-name">
                        <i class="fas fa-flag"></i> Sweden • Allsvenskan
                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="team-name">Djurgardens If</span>
                                <span class="match-time">06/07, 17:30</span>
                            </div>
                            <div class="team-name">Degerfors</div>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-muted small">1</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('djurgardens-vs-degerfors', 'Djurgardens If Vs. Degerfors', '1', '1.67', 'Sweden • Allsvenskan', '06/07, 17:30')">1.67</button>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted small">X</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('djurgardens-vs-degerfors', 'Djurgardens If Vs. Degerfors', 'X', '3.95', 'Sweden • Allsvenskan', '06/07, 17:30')">3.95</button>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted small">2</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('djurgardens-vs-degerfors', 'Djurgardens If Vs. Degerfors', '2', '4.80', 'Sweden • Allsvenskan', '06/07, 17:30')">4.80</button>
                                </div>
                            </div>
                            <div class="text-center mt-2">
                                <small class="text-success">+91 Markets</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="match-card p-3">
                    <div class="league-name">
                        <i class="fas fa-flag"></i> International • CONCACAF Gold Cup
                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="team-name">Usa</span>
                                <span class="match-time">07/07, 02:00</span>
                            </div>
                            <div class="team-name">Mexico</div>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-muted small">1</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('usa-vs-mexico', 'Usa Vs. Mexico', '1', '3.10', 'International • CONCACAF Gold Cup', '07/07, 02:00')">3.10</button>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted small">X</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('usa-vs-mexico', 'Usa Vs. Mexico', 'X', '2.95', 'International • CONCACAF Gold Cup', '07/07, 02:00')">2.95</button>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted small">2</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('usa-vs-mexico', 'Usa Vs. Mexico', '2', '2.49', 'International • CONCACAF Gold Cup', '07/07, 02:00')">2.49</button>
                                </div>
                            </div>
                            <div class="text-center mt-2">
                                <small class="text-success">+104 Markets</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="match-card p-3">
                    <div class="league-name">
                        <i class="fas fa-flag"></i> International Clubs • Club Friendlies
                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="team-name">Desportiva</span>
                                <span class="match-time">05/07, 17:00</span>
                            </div>
                            <div class="team-name">Defensa Y Justicia</div>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-muted small">1</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('desportiva-vs-defensa', 'Desportiva Vs. Defensa Y Justicia', '1', '6.20', 'International Clubs • Club Friendlies', '05/07, 17:00')">6.20</button>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted small">X</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('desportiva-vs-defensa', 'Desportiva Vs. Defensa Y Justicia', 'X', '4.00', 'International Clubs • Club Friendlies', '05/07, 17:00')">4.00</button>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted small">2</div>
                                    <button class="odds-btn w-100" onclick="addToBetslip('desportiva-vs-defensa', 'Desportiva Vs. Defensa Y Justicia', '2', '1.50', 'International Clubs • Club Friendlies', '05/07, 17:00')">1.50</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Sidebar - Betslip -->
        <div class="col-md-3 betslip-panel p-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="section-header mb-0">Do you have a shared betslip code? Enter it here.</h6>
            </div>

            <div class="mb-3">
                <input type="text" class="form-control" placeholder="e.g. VBmSU" style="background-color: #2d3748; border: 1px solid #4a5568; color: #ffffff;">
            </div>

            <button class="btn w-100 mb-4" style="background-color: #ffc107; color: #000; font-weight: 600;">
                Load Betslip
            </button>

            <!-- Betslip Content -->
            <div id="betslip-content">
                <!-- Empty State -->
                <div id="empty-betslip" class="text-center text-muted">
                    <i class="fas fa-ticket-alt fa-3x mb-3" style="opacity: 0.3;"></i>
                    <p>Your betslip is empty</p>
                    <small>Click on odds to add selections</small>
                </div>

                <!-- Betslip Items -->
                <div id="betslip-items" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="section-header">Betslip (<span id="bet-count">0</span>)</span>
                        <button class="btn btn-sm btn-outline-light" onclick="clearBetslip()">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div id="selections-list">
                        <!-- Dynamic selections will be added here -->
                    </div>

                    <!-- Betting Summary -->
                    <div class="mt-3 p-3" style="background-color: #2d3748; border-radius: 8px;">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Odds</span>
                            <span id="total-odds">0.00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Final Payout</span>
                            <span>KES<span id="final-payout">0</span></span>
                        </div>

                        <div class="mb-3">
                            <label class="form-label small">Amount (KES)</label>
                            <input type="number" id="bet-amount" class="form-control" value="100" min="1"
                                   style="background-color: #1e2329; border: 1px solid #4a5568; color: #ffffff;"
                                   oninput="updatePayout()">
                        </div>

                        <button class="btn w-100" style="background-color: #ffc107; color: #000; font-weight: 600;" onclick="placeBet()">
                            Place Bet KES<span id="bet-amount-display">100</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="mt-4">
                <h6 class="section-header">Quick Access</h6>
                <div class="d-grid gap-2">
                    <a href="{% url 'betting:live_matches' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-broadcast-tower"></i> Live Matches
                    </a>
                    <a href="{% url 'betting:virtual_sports' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-gamepad"></i> Virtual Sports
                    </a>
                    <a href="{% url 'betting:user_bets' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-history"></i> My Bets
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let betslipSelections = [];

function addToBetslip(matchId, matchName, betType, odds, league, time) {
    // Check if selection already exists
    const existingIndex = betslipSelections.findIndex(sel => sel.matchId === matchId);

    if (existingIndex !== -1) {
        // Update existing selection
        betslipSelections[existingIndex] = {
            matchId: matchId,
            matchName: matchName,
            betType: betType,
            odds: parseFloat(odds),
            league: league,
            time: time
        };
    } else {
        // Add new selection
        betslipSelections.push({
            matchId: matchId,
            matchName: matchName,
            betType: betType,
            odds: parseFloat(odds),
            league: league,
            time: time
        });
    }

    updateBetslipDisplay();
    updateOddsButtonStates();
}

function removeFromBetslip(matchId) {
    betslipSelections = betslipSelections.filter(sel => sel.matchId !== matchId);
    updateBetslipDisplay();
    updateOddsButtonStates();
}

function clearBetslip() {
    betslipSelections = [];
    updateBetslipDisplay();
    updateOddsButtonStates();
}

function updateBetslipDisplay() {
    const emptyState = document.getElementById('empty-betslip');
    const betslipItems = document.getElementById('betslip-items');
    const selectionsList = document.getElementById('selections-list');
    const betCount = document.getElementById('bet-count');

    if (betslipSelections.length === 0) {
        emptyState.style.display = 'block';
        betslipItems.style.display = 'none';
    } else {
        emptyState.style.display = 'none';
        betslipItems.style.display = 'block';
        betCount.textContent = betslipSelections.length;

        // Build selections HTML
        let selectionsHTML = '';
        betslipSelections.forEach(selection => {
            selectionsHTML += `
                <div class="selection-item mb-3 p-3" style="background-color: #1e2329; border-radius: 8px; border-left: 3px solid #ffc107;">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="flex-grow-1">
                            <div class="fw-bold text-white">${selection.matchName}</div>
                            <div class="small text-muted">${selection.betType} • ${selection.league}</div>
                            <div class="small text-muted">Starts ${selection.time}</div>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-warning">${selection.odds.toFixed(2)}</div>
                            <button class="btn btn-sm btn-outline-danger mt-1" onclick="removeFromBetslip('${selection.matchId}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        selectionsList.innerHTML = selectionsHTML;
        updatePayout();
    }
}

function updatePayout() {
    if (betslipSelections.length === 0) return;

    // Calculate total odds (multiply all odds together for accumulator)
    const totalOdds = betslipSelections.reduce((total, selection) => total * selection.odds, 1);
    const betAmount = parseFloat(document.getElementById('bet-amount').value) || 0;
    const finalPayout = Math.round(totalOdds * betAmount);

    document.getElementById('total-odds').textContent = totalOdds.toFixed(2);
    document.getElementById('final-payout').textContent = finalPayout;
    document.getElementById('bet-amount-display').textContent = betAmount;
}

function updateOddsButtonStates() {
    // Reset all buttons
    document.querySelectorAll('.odds-btn').forEach(btn => {
        btn.classList.remove('selected');
        btn.style.backgroundColor = '#2d3748';
        btn.style.color = '#ffffff';
    });

    // Highlight selected buttons
    betslipSelections.forEach(selection => {
        const buttons = document.querySelectorAll(`[onclick*="${selection.matchId}"]`);
        buttons.forEach(btn => {
            if (btn.onclick.toString().includes(`'${selection.betType}'`)) {
                btn.classList.add('selected');
                btn.style.backgroundColor = '#ffc107';
                btn.style.color = '#000000';
            }
        });
    });
}

function placeBet() {
    if (betslipSelections.length === 0) {
        alert('Please add selections to your betslip');
        return;
    }

    const betAmount = parseFloat(document.getElementById('bet-amount').value);
    if (!betAmount || betAmount < 1) {
        alert('Please enter a valid bet amount');
        return;
    }

    // Here you would typically send the bet to your backend
    alert(`Bet placed successfully!\nSelections: ${betslipSelections.length}\nTotal Odds: ${document.getElementById('total-odds').textContent}\nBet Amount: KES ${betAmount}\nPotential Payout: KES ${document.getElementById('final-payout').textContent}`);

    // Clear betslip after successful bet
    clearBetslip();
}

// Initialize bet amount input listener
document.addEventListener('DOMContentLoaded', function() {
    const betAmountInput = document.getElementById('bet-amount');
    if (betAmountInput) {
        betAmountInput.addEventListener('input', updatePayout);
    }
});
</script>
{% endblock %}
