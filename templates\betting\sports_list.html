{% extends 'base.html' %}

{% block title %}Sports Betting - ZBet{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-trophy"></i> Welcome to ZBet
                    </h1>
                    <p class="lead mb-4">Your premier destination for sports betting</p>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h3><i class="fas fa-futbol"></i></h3>
                            <p>Live Sports</p>
                        </div>
                        <div class="col-md-4">
                            <h3><i class="fas fa-chart-line"></i></h3>
                            <p>Best Odds</p>
                        </div>
                        <div class="col-md-4">
                            <h3><i class="fas fa-mobile-alt"></i></h3>
                            <p>Mobile Betting</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Sports -->
    {% if featured_sports %}
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-star text-warning"></i> Featured Sports
            </h2>
            <div class="row">
                {% for sport in featured_sports %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            {% if sport.icon %}
                                <i class="fas fa-{{ sport.icon }} fa-3x text-primary mb-3"></i>
                            {% else %}
                                <i class="fas fa-futbol fa-3x text-primary mb-3"></i>
                            {% endif %}
                            <h5 class="card-title">{{ sport.name }}</h5>
                            <p class="card-text text-muted">{{ sport.description|truncatewords:15 }}</p>
                            <a href="{% url 'betting:sport_matches' sport.slug %}" class="btn btn-primary">
                                <i class="fas fa-eye"></i> View Matches
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- All Sports -->
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-list"></i> All Sports
            </h2>
            
            {% if sports %}
                <div class="row">
                    {% for sport in sports %}
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card h-100 shadow-sm hover-shadow">
                            <div class="card-body text-center">
                                {% if sport.icon %}
                                    <i class="fas fa-{{ sport.icon }} fa-2x text-primary mb-3"></i>
                                {% else %}
                                    <i class="fas fa-futbol fa-2x text-primary mb-3"></i>
                                {% endif %}
                                <h6 class="card-title">{{ sport.name }}</h6>
                                {% if sport.description %}
                                    <p class="card-text small text-muted">{{ sport.description|truncatewords:8 }}</p>
                                {% endif %}
                                <a href="{% url 'betting:sport_matches' sport.slug %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-arrow-right"></i> Bet Now
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Sports Available</h4>
                    <p class="text-muted">Sports will be available soon. Please check back later.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-bolt"></i> Quick Access
                    </h5>
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{% url 'betting:live_matches' %}" class="btn btn-outline-success w-100">
                                <i class="fas fa-broadcast-tower"></i><br>
                                <small>Live Matches</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{% url 'betting:bet_slip' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-ticket-alt"></i><br>
                                <small>Bet Slip</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{% url 'betting:virtual_sports' %}" class="btn btn-outline-info w-100">
                                <i class="fas fa-gamepad"></i><br>
                                <small>Virtual Sports</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="{% url 'betting:user_bets' %}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-history"></i><br>
                                <small>My Bets</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hover-shadow:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.card {
    transition: all 0.3s ease;
}
</style>
{% endblock %}
