{% extends 'base.html' %}
{% load static %}

{% block title %}Promotions & Bonuses - ZBet{% endblock %}

{% block extra_css %}
<style>
.promotions-hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 60px 0;
    text-align: center;
    color: white;
}

.promotions-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.promo-code-section {
    background: #0f3460;
    padding: 20px 0;
    text-align: center;
}

.promo-code-form {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 25px;
}

.promo-code-input {
    background: transparent;
    border: 2px solid #ffd700;
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 1rem;
    min-width: 200px;
}

.promo-code-input::placeholder {
    color: #aaa;
}

.btn-apply-code {
    background: #ffd700;
    color: #1a1a2e;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-apply-code:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

.promotions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
    padding: 30px 0;
}

.promotion-card {
    background: #1e1e2e;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #333;
    position: relative;
}

.promotion-card:hover {
    transform: translateY(-8px);
    border-color: #ffd700;
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.2);
}

.promotion-banner {
    height: 200px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.promotion-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.promotion-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #ffd700;
    color: #1a1a2e;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.promotion-content {
    padding: 25px;
}

.promotion-title {
    color: #ffd700;
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.promotion-description {
    color: #ccc;
    line-height: 1.5;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.promotion-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    text-align: center;
    padding: 10px;
    background: #2a2a3e;
    border-radius: 8px;
}

.detail-label {
    color: #aaa;
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.detail-value {
    color: #ffd700;
    font-weight: bold;
    font-size: 1.1rem;
}

.promotion-actions {
    display: flex;
    gap: 10px;
}

.btn-claim {
    flex: 1;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-claim:hover {
    background: linear-gradient(45deg, #ffed4e, #fff59d);
    color: #1a1a2e;
    transform: translateY(-2px);
}

.btn-details {
    background: transparent;
    color: #ffd700;
    border: 2px solid #ffd700;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-details:hover {
    background: #ffd700;
    color: #1a1a2e;
    transform: translateY(-2px);
}

.user-bonuses-section {
    background: #1e1e2e;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.section-title {
    color: #ffd700;
    font-size: 1.8rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bonus-card {
    background: #2a2a3e;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid #444;
}

.bonus-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.bonus-title {
    color: white;
    font-weight: bold;
}

.bonus-amount {
    color: #ffd700;
    font-size: 1.2rem;
    font-weight: bold;
}

.bonus-progress {
    margin-bottom: 10px;
}

.progress-bar {
    background: #333;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    height: 100%;
    transition: width 0.3s ease;
}

.bonus-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    font-size: 0.9rem;
}

.info-item {
    color: #aaa;
}

.info-value {
    color: white;
    font-weight: bold;
}

.loyalty-widget {
    background: linear-gradient(45deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #444;
    text-align: center;
}

.loyalty-tier {
    color: #ffd700;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.loyalty-points {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 15px;
}

.loyalty-progress {
    margin-bottom: 15px;
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.quick-link {
    background: #2a2a3e;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    border: 1px solid #444;
}

.quick-link:hover {
    background: #333;
    border-color: #ffd700;
    color: white;
    transform: translateY(-3px);
}

.quick-link-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #ffd700;
}

.quick-link-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.quick-link-desc {
    font-size: 0.9rem;
    color: #aaa;
}

@media (max-width: 768px) {
    .promotions-hero h1 {
        font-size: 2rem;
    }
    
    .promotions-grid {
        grid-template-columns: 1fr;
    }
    
    .promo-code-form {
        flex-direction: column;
        gap: 15px;
    }
    
    .promo-code-input {
        width: 100%;
    }
    
    .promotion-details {
        grid-template-columns: 1fr;
    }
    
    .promotion-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Promotions Hero Section -->
<div class="promotions-hero">
    <div class="container">
        <h1>🎁 Promotions & Bonuses</h1>
        <p class="lead">Boost your winnings with our amazing bonuses and promotions!</p>
    </div>
</div>

<!-- Promo Code Section -->
<div class="promo-code-section">
    <div class="container">
        <form method="post" action="{% url 'promotions:use_promo_code' %}" class="promo-code-form">
            {% csrf_token %}
            <span style="color: #ffd700; font-weight: bold;">🎫 Have a Promo Code?</span>
            <input type="text" name="code" class="promo-code-input" placeholder="Enter promo code" required>
            <button type="submit" class="btn-apply-code">Apply Code</button>
        </form>
    </div>
</div>

<div class="container">
    <!-- User Bonuses Section (if logged in) -->
    {% if user.is_authenticated and user_bonuses %}
    <div class="user-bonuses-section">
        <h2 class="section-title">🎯 Your Active Bonuses</h2>
        {% for bonus in user_bonuses %}
        <div class="bonus-card">
            <div class="bonus-header">
                <div class="bonus-title">{{ bonus.bonus_type.name }}</div>
                <div class="bonus-amount">KES {{ bonus.remaining_amount|floatformat:2 }}</div>
            </div>
            
            {% if bonus.wagering_requirement > 0 %}
            <div class="bonus-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ bonus.wagering_progress }}%"></div>
                </div>
                <small style="color: #aaa;">Wagering Progress: {{ bonus.wagering_progress|floatformat:1 }}%</small>
            </div>
            {% endif %}
            
            <div class="bonus-info">
                <div class="info-item">
                    <div class="info-value">{{ bonus.get_status_display }}</div>
                    <div>Status</div>
                </div>
                <div class="info-item">
                    <div class="info-value">KES {{ bonus.remaining_wagering|floatformat:2 }}</div>
                    <div>Remaining Wagering</div>
                </div>
                <div class="info-item">
                    <div class="info-value">{{ bonus.expires_at|date:"M j, Y"|default:"No Expiry" }}</div>
                    <div>Expires</div>
                </div>
            </div>
        </div>
        {% endfor %}
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="{% url 'promotions:my_bonuses' %}" class="btn-details">View All Bonuses</a>
        </div>
    </div>
    {% endif %}

    <!-- Loyalty Program Widget (if logged in) -->
    {% if user.is_authenticated and user_loyalty %}
    <div class="loyalty-widget">
        <div class="loyalty-tier">
            {% if user_loyalty.current_program %}
                {{ user_loyalty.current_program.name }}
            {% else %}
                Bronze Tier
            {% endif %}
        </div>
        <div class="loyalty-points">{{ user_loyalty.available_points }} Points</div>
        {% if user_loyalty.points_to_next_tier > 0 %}
        <div class="loyalty-progress">
            <div class="progress-bar">
                <div class="progress-fill" style="width: 70%"></div>
            </div>
            <small style="color: #aaa;">{{ user_loyalty.points_to_next_tier }} points to next tier</small>
        </div>
        {% endif %}
        <a href="{% url 'promotions:loyalty' %}" class="btn-details">View Loyalty Program</a>
    </div>
    {% endif %}

    <!-- Featured Promotions -->
    {% if featured_promotions %}
    <h2 class="section-title">⭐ Featured Promotions</h2>
    <div class="promotions-grid">
        {% for promotion in featured_promotions %}
        <div class="promotion-card">
            <div class="promotion-banner">
                {% if promotion.banner_image %}
                <img src="{{ promotion.banner_image.url }}" alt="{{ promotion.name }}">
                {% else %}
                <div style="color: #ffd700; font-size: 3rem;">🎁</div>
                {% endif %}
                {% if promotion.is_featured %}
                <div class="promotion-badge">Featured</div>
                {% endif %}
            </div>
            
            <div class="promotion-content">
                <h3 class="promotion-title">{{ promotion.name }}</h3>
                <p class="promotion-description">{{ promotion.short_description|default:promotion.description }}</p>
                
                <div class="promotion-details">
                    <div class="detail-item">
                        <div class="detail-label">Bonus</div>
                        <div class="detail-value">
                            {% if promotion.promotion_type == 'PERCENTAGE' %}
                                {{ promotion.percentage_value }}%
                            {% elif promotion.promotion_type == 'FIXED_AMOUNT' %}
                                KES {{ promotion.fixed_amount }}
                            {% else %}
                                {{ promotion.get_promotion_type_display }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Min Deposit</div>
                        <div class="detail-value">KES {{ promotion.min_deposit_amount }}</div>
                    </div>
                </div>
                
                <div class="promotion-actions">
                    {% if user.is_authenticated %}
                    <a href="{% url 'promotions:claim' promotion.slug %}" class="btn-claim">Claim Bonus</a>
                    {% else %}
                    <a href="{% url 'account_login' %}" class="btn-claim">Login to Claim</a>
                    {% endif %}
                    <a href="{% url 'promotions:detail' promotion.slug %}" class="btn-details">Details</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- All Active Promotions -->
    <h2 class="section-title">🎯 All Active Promotions</h2>
    <div class="promotions-grid">
        {% for promotion in active_promotions %}
        <div class="promotion-card">
            <div class="promotion-banner">
                {% if promotion.banner_image %}
                <img src="{{ promotion.banner_image.url }}" alt="{{ promotion.name }}">
                {% else %}
                <div style="color: #ffd700; font-size: 3rem;">
                    {% if promotion.bonus_type.category == 'WELCOME' %}🎉
                    {% elif promotion.bonus_type.category == 'DEPOSIT' %}💰
                    {% elif promotion.bonus_type.category == 'CASHBACK' %}💸
                    {% elif promotion.bonus_type.category == 'REFERRAL' %}👥
                    {% else %}🎁
                    {% endif %}
                </div>
                {% endif %}
                {% if promotion.is_featured %}
                <div class="promotion-badge">Featured</div>
                {% endif %}
            </div>
            
            <div class="promotion-content">
                <h3 class="promotion-title">{{ promotion.name }}</h3>
                <p class="promotion-description">{{ promotion.short_description|default:promotion.description }}</p>
                
                <div class="promotion-details">
                    <div class="detail-item">
                        <div class="detail-label">Bonus</div>
                        <div class="detail-value">
                            {% if promotion.promotion_type == 'PERCENTAGE' %}
                                {{ promotion.percentage_value }}%
                            {% elif promotion.promotion_type == 'FIXED_AMOUNT' %}
                                KES {{ promotion.fixed_amount }}
                            {% else %}
                                {{ promotion.get_promotion_type_display }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Expires In</div>
                        <div class="detail-value">{{ promotion.days_remaining }} days</div>
                    </div>
                </div>
                
                <div class="promotion-actions">
                    {% if user.is_authenticated %}
                    <a href="{% url 'promotions:claim' promotion.slug %}" class="btn-claim">Claim Bonus</a>
                    {% else %}
                    <a href="{% url 'account_login' %}" class="btn-claim">Login to Claim</a>
                    {% endif %}
                    <a href="{% url 'promotions:detail' promotion.slug %}" class="btn-details">Details</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Quick Links -->
    <div class="quick-links">
        <a href="{% url 'promotions:loyalty' %}" class="quick-link">
            <div class="quick-link-icon">🏆</div>
            <div class="quick-link-title">Loyalty Program</div>
            <div class="quick-link-desc">Earn points and unlock exclusive benefits</div>
        </a>
        
        <a href="{% url 'promotions:referral' %}" class="quick-link">
            <div class="quick-link-icon">👥</div>
            <div class="quick-link-title">Refer Friends</div>
            <div class="quick-link-desc">Earn bonuses for every friend you refer</div>
        </a>
        
        <a href="{% url 'promotions:cashback' %}" class="quick-link">
            <div class="quick-link-icon">💸</div>
            <div class="quick-link-title">Cashback</div>
            <div class="quick-link-desc">Get money back on your losses</div>
        </a>
        
        {% if user.is_authenticated %}
        <a href="{% url 'promotions:my_bonuses' %}" class="quick-link">
            <div class="quick-link-icon">🎁</div>
            <div class="quick-link-title">My Bonuses</div>
            <div class="quick-link-desc">Track your active bonuses and progress</div>
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}
