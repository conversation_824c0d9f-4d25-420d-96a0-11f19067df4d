# Generated by Django 5.2.4 on 2025-07-05 20:29

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("betting", "0001_initial"),
        ("sports", "0002_remove_match_statistics_match_stats_data_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BetSettlementRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("match_result", "Match Result"),
                            ("goal_total", "Goal Total"),
                            ("handicap", "Handicap"),
                            ("correct_score", "Correct Score"),
                            ("first_goal", "First Goal Scorer"),
                            ("custom", "Custom Rule"),
                        ],
                        max_length=20,
                    ),
                ),
                ("rule_name", models.CharField(max_length=200)),
                ("rule_description", models.TextField(blank=True)),
                (
                    "rule_config",
                    models.J<PERSON><PERSON>ield(
                        default=dict, help_text="Rule configuration parameters"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=0, help_text="Rule execution priority"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "bet_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settlement_rules",
                        to="betting.bettype",
                    ),
                ),
                (
                    "sport",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settlement_rules",
                        to="sports.sport",
                    ),
                ),
            ],
            options={
                "verbose_name": "Settlement Rule",
                "verbose_name_plural": "Settlement Rules",
                "db_table": "betting_settlement_rule",
                "ordering": ["priority", "rule_name"],
            },
        ),
        migrations.CreateModel(
            name="Settlement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("settlement_id", models.CharField(max_length=20, unique=True)),
                (
                    "settlement_type",
                    models.CharField(
                        choices=[
                            ("automatic", "Automatic Settlement"),
                            ("manual", "Manual Settlement"),
                            ("correction", "Settlement Correction"),
                            ("void", "Void Settlement"),
                        ],
                        default="automatic",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Settlement"),
                            ("settled", "Settled"),
                            ("voided", "Voided"),
                            ("disputed", "Disputed"),
                            ("reviewed", "Under Review"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "settlement_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "net_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("settlement_reason", models.TextField(blank=True)),
                ("settlement_notes", models.TextField(blank=True)),
                (
                    "settlement_data",
                    models.JSONField(
                        blank=True, default=dict, help_text="Additional settlement data"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "bet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settlements",
                        to="betting.bet",
                    ),
                ),
                (
                    "market",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settlements",
                        to="betting.market",
                    ),
                ),
                (
                    "settled_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="settlements_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "winning_selection",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="winning_settlements",
                        to="betting.selection",
                    ),
                ),
            ],
            options={
                "verbose_name": "Settlement",
                "verbose_name_plural": "Settlements",
                "db_table": "betting_settlement",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SettlementHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("action", models.CharField(max_length=50)),
                ("old_status", models.CharField(blank=True, max_length=20)),
                ("new_status", models.CharField(blank=True, max_length=20)),
                (
                    "old_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "new_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("reason", models.TextField(blank=True)),
                ("notes", models.TextField(blank=True)),
                ("changed_at", models.DateTimeField(auto_now_add=True)),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "settlement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="history",
                        to="betting.settlement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Settlement History",
                "verbose_name_plural": "Settlement History",
                "db_table": "betting_settlement_history",
                "ordering": ["-changed_at"],
            },
        ),
    ]
