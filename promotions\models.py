from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
import json
from datetime import timedelta

User = get_user_model()


class BonusType(models.Model):
    """Types of bonuses available"""
    BONUS_CATEGORY_CHOICES = [
        ('WELCOME', 'Welcome Bonus'),
        ('DEPOSIT', 'Deposit Bonus'),
        ('CASHBACK', 'Cashback'),
        ('REFERRAL', 'Referral Bonus'),
        ('LOYALTY', 'Loyalty Bonus'),
        ('SEASONAL', 'Seasonal Promotion'),
        ('VIP', 'VIP Bonus'),
        ('FREE_BET', 'Free Bet'),
        ('NO_DEPOSIT', 'No Deposit Bonus'),
        ('RELOAD', 'Reload Bonus'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    category = models.CharField(max_length=20, choices=BONUS_CATEGORY_CHOICES)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'promotions_bonus_types'
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"


class Promotion(models.Model):
    """Main promotion campaigns"""
    PROMOTION_STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('ACTIVE', 'Active'),
        ('PAUSED', 'Paused'),
        ('EXPIRED', 'Expired'),
        ('CANCELLED', 'Cancelled'),
    ]

    PROMOTION_TYPE_CHOICES = [
        ('PERCENTAGE', 'Percentage Bonus'),
        ('FIXED_AMOUNT', 'Fixed Amount'),
        ('FREE_BETS', 'Free Bets'),
        ('CASHBACK', 'Cashback'),
        ('LOYALTY_POINTS', 'Loyalty Points'),
        ('COMBO', 'Combination Bonus'),
    ]

    TARGET_AUDIENCE_CHOICES = [
        ('ALL', 'All Users'),
        ('NEW', 'New Users'),
        ('EXISTING', 'Existing Users'),
        ('VIP', 'VIP Users'),
        ('INACTIVE', 'Inactive Users'),
        ('HIGH_ROLLERS', 'High Rollers'),
        ('SPECIFIC', 'Specific Users'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    description = models.TextField()
    short_description = models.CharField(max_length=255, blank=True)

    # Promotion configuration
    promotion_type = models.CharField(max_length=20, choices=PROMOTION_TYPE_CHOICES)
    bonus_type = models.ForeignKey(BonusType, on_delete=models.CASCADE, related_name='promotions')

    # Bonus values
    percentage_value = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Percentage bonus (0-100%)"
    )
    fixed_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Fixed bonus amount in KES"
    )
    max_bonus_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True,
        help_text="Maximum bonus amount for percentage bonuses"
    )
    min_deposit_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="Minimum deposit required to qualify"
    )

    # Targeting
    target_audience = models.CharField(max_length=20, choices=TARGET_AUDIENCE_CHOICES, default='ALL')
    specific_users = models.ManyToManyField(User, blank=True, related_name='targeted_promotions')

    # Validity
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_unlimited = models.BooleanField(default=False, help_text="No usage limit")
    max_uses_total = models.PositiveIntegerField(null=True, blank=True, help_text="Total usage limit")
    max_uses_per_user = models.PositiveIntegerField(default=1, help_text="Per user usage limit")

    # Wagering requirements
    wagering_requirement = models.DecimalField(
        max_digits=5, decimal_places=2, default=Decimal('1.00'),
        help_text="Wagering multiplier (e.g., 35.00 = 35x)"
    )
    wagering_contribution_sports = models.DecimalField(
        max_digits=5, decimal_places=2, default=Decimal('100.00'),
        help_text="Sports betting contribution percentage"
    )
    wagering_contribution_casino = models.DecimalField(
        max_digits=5, decimal_places=2, default=Decimal('100.00'),
        help_text="Casino games contribution percentage"
    )

    # Restrictions
    min_odds = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True,
        help_text="Minimum odds for sports bets"
    )
    excluded_games = models.JSONField(default=list, blank=True, help_text="Excluded casino games")

    # Status and display
    status = models.CharField(max_length=20, choices=PROMOTION_STATUS_CHOICES, default='DRAFT')
    is_featured = models.BooleanField(default=False)
    priority = models.PositiveIntegerField(default=0, help_text="Higher numbers appear first")

    # Media
    banner_image = models.ImageField(upload_to='promotions/banners/', null=True, blank=True)
    thumbnail_image = models.ImageField(upload_to='promotions/thumbnails/', null=True, blank=True)

    # Terms and conditions
    terms_and_conditions = models.TextField(blank=True)

    # Statistics
    total_uses = models.PositiveIntegerField(default=0)
    total_bonus_awarded = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_promotions')

    class Meta:
        db_table = 'promotions_promotions'
        ordering = ['-priority', '-created_at']
        indexes = [
            models.Index(fields=['status', 'start_date', 'end_date']),
            models.Index(fields=['target_audience', 'status']),
            models.Index(fields=['is_featured', 'status']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_active(self):
        """Check if promotion is currently active"""
        now = timezone.now()
        return (
            self.status == 'ACTIVE' and
            self.start_date <= now <= self.end_date
        )

    @property
    def is_expired(self):
        """Check if promotion has expired"""
        return timezone.now() > self.end_date

    @property
    def days_remaining(self):
        """Get days remaining for promotion"""
        if self.is_expired:
            return 0
        return (self.end_date - timezone.now()).days

    def can_user_claim(self, user):
        """Check if user can claim this promotion"""
        if not self.is_active:
            return False, "Promotion is not active"

        # Check target audience
        if self.target_audience == 'NEW' and user.date_joined < (timezone.now() - timedelta(days=7)):
            return False, "Only for new users"
        elif self.target_audience == 'VIP' and not getattr(user, 'is_vip', False):
            return False, "Only for VIP users"
        elif self.target_audience == 'SPECIFIC' and user not in self.specific_users.all():
            return False, "Not eligible for this promotion"

        # Check usage limits
        user_claims = self.bonus_claims.filter(user=user).count()
        if user_claims >= self.max_uses_per_user:
            return False, f"Maximum {self.max_uses_per_user} uses per user"

        if not self.is_unlimited and self.total_uses >= (self.max_uses_total or 0):
            return False, "Promotion usage limit reached"

        return True, "Eligible"

    def calculate_bonus(self, amount):
        """Calculate bonus amount for given deposit/bet amount"""
        if self.promotion_type == 'PERCENTAGE':
            bonus = amount * (self.percentage_value / 100)
            if self.max_bonus_amount:
                bonus = min(bonus, self.max_bonus_amount)
            return bonus
        elif self.promotion_type == 'FIXED_AMOUNT':
            return self.fixed_amount or Decimal('0.00')
        return Decimal('0.00')


class Bonus(models.Model):
    """Individual bonus instances awarded to users"""
    BONUS_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('ACTIVE', 'Active'),
        ('WAGERING', 'Wagering in Progress'),
        ('COMPLETED', 'Completed'),
        ('EXPIRED', 'Expired'),
        ('CANCELLED', 'Cancelled'),
        ('FORFEITED', 'Forfeited'),
    ]

    BONUS_SOURCE_CHOICES = [
        ('PROMOTION', 'Promotion'),
        ('WELCOME', 'Welcome Bonus'),
        ('DEPOSIT', 'Deposit Bonus'),
        ('CASHBACK', 'Cashback'),
        ('REFERRAL', 'Referral'),
        ('LOYALTY', 'Loyalty Program'),
        ('MANUAL', 'Manual Award'),
        ('COMPENSATION', 'Compensation'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bonuses')
    promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE, null=True, blank=True, related_name='bonus_claims')
    bonus_type = models.ForeignKey(BonusType, on_delete=models.CASCADE, related_name='bonuses')

    # Bonus details
    source = models.CharField(max_length=20, choices=BONUS_SOURCE_CHOICES)
    reference = models.CharField(max_length=50, unique=True)

    # Amounts
    bonus_amount = models.DecimalField(max_digits=10, decimal_places=2)
    original_amount = models.DecimalField(max_digits=10, decimal_places=2, help_text="Deposit/bet amount that triggered bonus")
    remaining_amount = models.DecimalField(max_digits=10, decimal_places=2)

    # Wagering requirements
    wagering_requirement = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    wagered_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    remaining_wagering = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Status and dates
    status = models.CharField(max_length=20, choices=BONUS_STATUS_CHOICES, default='PENDING')
    awarded_at = models.DateTimeField(auto_now_add=True)
    activated_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Additional data
    terms_accepted = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'promotions_bonuses'
        ordering = ['-awarded_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'expires_at']),
            models.Index(fields=['reference']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.bonus_type.name} - KES {self.bonus_amount}"

    @property
    def is_expired(self):
        """Check if bonus has expired"""
        return self.expires_at and timezone.now() > self.expires_at

    @property
    def wagering_progress(self):
        """Get wagering progress percentage"""
        if self.wagering_requirement <= 0:
            return 100
        return min((self.wagered_amount / self.wagering_requirement) * 100, 100)

    def activate(self):
        """Activate the bonus"""
        if self.status == 'PENDING':
            self.status = 'ACTIVE'
            self.activated_at = timezone.now()
            self.remaining_amount = self.bonus_amount
            self.remaining_wagering = self.wagering_requirement
            self.save()

    def add_wagering(self, amount, contribution_percentage=100):
        """Add wagering progress"""
        if self.status in ['ACTIVE', 'WAGERING']:
            contribution = amount * (Decimal(contribution_percentage) / 100)
            self.wagered_amount += contribution
            self.remaining_wagering = max(0, self.wagering_requirement - self.wagered_amount)

            if self.remaining_wagering <= 0:
                self.status = 'COMPLETED'
                self.completed_at = timezone.now()
            else:
                self.status = 'WAGERING'

            self.save()

    def forfeit(self, reason="User forfeited"):
        """Forfeit the bonus"""
        if self.status in ['ACTIVE', 'WAGERING']:
            self.status = 'FORFEITED'
            self.notes = reason
            self.save()


class Cashback(models.Model):
    """Cashback bonuses for losses"""
    CASHBACK_TYPE_CHOICES = [
        ('DAILY', 'Daily Cashback'),
        ('WEEKLY', 'Weekly Cashback'),
        ('MONTHLY', 'Monthly Cashback'),
        ('LOSS_BASED', 'Loss-based Cashback'),
        ('GAME_SPECIFIC', 'Game-specific Cashback'),
    ]

    CASHBACK_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('CALCULATED', 'Calculated'),
        ('AWARDED', 'Awarded'),
        ('EXPIRED', 'Expired'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='cashbacks')

    # Cashback configuration
    cashback_type = models.CharField(max_length=20, choices=CASHBACK_TYPE_CHOICES)
    percentage = models.DecimalField(
        max_digits=5, decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    # Period and amounts
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    total_losses = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    cashback_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    min_loss_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    max_cashback_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=CASHBACK_STATUS_CHOICES, default='PENDING')
    calculated_at = models.DateTimeField(null=True, blank=True)
    awarded_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Related bonus
    bonus = models.OneToOneField(Bonus, on_delete=models.SET_NULL, null=True, blank=True, related_name='cashback_source')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'promotions_cashbacks'
        ordering = ['-created_at']
        unique_together = ['user', 'cashback_type', 'period_start', 'period_end']

    def __str__(self):
        return f"{self.user.username} - {self.get_cashback_type_display()} - KES {self.cashback_amount}"

    def calculate_cashback(self):
        """Calculate cashback amount based on losses"""
        if self.total_losses >= self.min_loss_amount:
            cashback = self.total_losses * (self.percentage / 100)
            if self.max_cashback_amount:
                cashback = min(cashback, self.max_cashback_amount)
            self.cashback_amount = cashback
            self.status = 'CALCULATED'
            self.calculated_at = timezone.now()
            self.save()
            return cashback
        return Decimal('0.00')

    def award_cashback(self):
        """Award cashback as bonus"""
        if self.status == 'CALCULATED' and self.cashback_amount > 0:
            # Create bonus type for cashback
            cashback_type, _ = BonusType.objects.get_or_create(
                name='Cashback Bonus',
                category='CASHBACK'
            )

            # Create bonus
            bonus = Bonus.objects.create(
                user=self.user,
                bonus_type=cashback_type,
                source='CASHBACK',
                reference=f'CB-{self.id.hex[:8].upper()}',
                bonus_amount=self.cashback_amount,
                original_amount=self.total_losses,
                remaining_amount=self.cashback_amount,
                wagering_requirement=0,  # Cashback usually has no wagering
                status='ACTIVE',
                activated_at=timezone.now()
            )

            self.bonus = bonus
            self.status = 'AWARDED'
            self.awarded_at = timezone.now()
            self.save()

            return bonus
        return None


class ReferralBonus(models.Model):
    """Referral bonus system"""
    REFERRAL_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('QUALIFIED', 'Qualified'),
        ('AWARDED', 'Awarded'),
        ('EXPIRED', 'Expired'),
        ('CANCELLED', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    referrer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='referrals_made')
    referred_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='referral_source')

    # Referral details
    referral_code = models.CharField(max_length=20, unique=True)

    # Bonus amounts
    referrer_bonus = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    referred_bonus = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Qualification requirements
    min_deposit_required = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    min_wagering_required = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    qualification_period_days = models.PositiveIntegerField(default=30)

    # Status tracking
    status = models.CharField(max_length=20, choices=REFERRAL_STATUS_CHOICES, default='PENDING')
    referred_user_deposit = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    referred_user_wagering = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Dates
    created_at = models.DateTimeField(auto_now_add=True)
    qualified_at = models.DateTimeField(null=True, blank=True)
    awarded_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Related bonuses
    referrer_bonus_instance = models.ForeignKey(Bonus, on_delete=models.SET_NULL, null=True, blank=True, related_name='referrer_bonus_source')
    referred_bonus_instance = models.ForeignKey(Bonus, on_delete=models.SET_NULL, null=True, blank=True, related_name='referred_bonus_source')

    class Meta:
        db_table = 'promotions_referral_bonuses'
        ordering = ['-created_at']
        unique_together = ['referrer', 'referred_user']

    def __str__(self):
        return f"{self.referrer.username} referred {self.referred_user.username}"

    def check_qualification(self):
        """Check if referral qualifies for bonus"""
        if self.status != 'PENDING':
            return False

        # Check if qualification period has expired
        if self.expires_at and timezone.now() > self.expires_at:
            self.status = 'EXPIRED'
            self.save()
            return False

        # Check deposit requirement
        if self.referred_user_deposit >= self.min_deposit_required:
            # Check wagering requirement
            if self.referred_user_wagering >= self.min_wagering_required:
                self.status = 'QUALIFIED'
                self.qualified_at = timezone.now()
                self.save()
                return True

        return False

    def award_bonuses(self):
        """Award bonuses to both referrer and referred user"""
        if self.status == 'QUALIFIED':
            referral_type, _ = BonusType.objects.get_or_create(
                name='Referral Bonus',
                category='REFERRAL'
            )

            # Award referrer bonus
            if self.referrer_bonus > 0:
                referrer_bonus = Bonus.objects.create(
                    user=self.referrer,
                    bonus_type=referral_type,
                    source='REFERRAL',
                    reference=f'REF-R-{self.id.hex[:8].upper()}',
                    bonus_amount=self.referrer_bonus,
                    original_amount=self.referred_user_deposit,
                    remaining_amount=self.referrer_bonus,
                    status='ACTIVE',
                    activated_at=timezone.now()
                )
                self.referrer_bonus_instance = referrer_bonus

            # Award referred user bonus
            if self.referred_bonus > 0:
                referred_bonus = Bonus.objects.create(
                    user=self.referred_user,
                    bonus_type=referral_type,
                    source='REFERRAL',
                    reference=f'REF-U-{self.id.hex[:8].upper()}',
                    bonus_amount=self.referred_bonus,
                    original_amount=self.referred_user_deposit,
                    remaining_amount=self.referred_bonus,
                    status='ACTIVE',
                    activated_at=timezone.now()
                )
                self.referred_bonus_instance = referred_bonus

            self.status = 'AWARDED'
            self.awarded_at = timezone.now()
            self.save()


class LoyaltyProgram(models.Model):
    """Loyalty program configuration"""
    TIER_CHOICES = [
        ('BRONZE', 'Bronze'),
        ('SILVER', 'Silver'),
        ('GOLD', 'Gold'),
        ('PLATINUM', 'Platinum'),
        ('DIAMOND', 'Diamond'),
        ('VIP', 'VIP'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    tier = models.CharField(max_length=20, choices=TIER_CHOICES)

    # Requirements
    min_points_required = models.PositiveIntegerField(default=0)
    min_deposit_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    min_wagering_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Benefits
    points_multiplier = models.DecimalField(max_digits=5, decimal_places=2, default=1.00)
    cashback_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    bonus_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Perks
    free_withdrawals_per_month = models.PositiveIntegerField(default=0)
    dedicated_support = models.BooleanField(default=False)
    exclusive_promotions = models.BooleanField(default=False)
    birthday_bonus = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Status
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'promotions_loyalty_programs'
        ordering = ['priority', 'min_points_required']

    def __str__(self):
        return f"{self.name} ({self.get_tier_display()})"


class LoyaltyPoints(models.Model):
    """User loyalty points tracking"""
    POINTS_TYPE_CHOICES = [
        ('EARNED', 'Points Earned'),
        ('REDEEMED', 'Points Redeemed'),
        ('EXPIRED', 'Points Expired'),
        ('BONUS', 'Bonus Points'),
        ('ADJUSTMENT', 'Manual Adjustment'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='loyalty_points')

    # Points details
    points_type = models.CharField(max_length=20, choices=POINTS_TYPE_CHOICES)
    points = models.IntegerField()
    balance_after = models.IntegerField(default=0)

    # Source information
    source_description = models.CharField(max_length=255)
    source_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Expiration
    expires_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'promotions_loyalty_points'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.points} points ({self.get_points_type_display()})"


class UserLoyaltyStatus(models.Model):
    """User's current loyalty status"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='loyalty_status')
    current_program = models.ForeignKey(LoyaltyProgram, on_delete=models.SET_NULL, null=True, blank=True)

    # Current status
    total_points = models.PositiveIntegerField(default=0)
    available_points = models.PositiveIntegerField(default=0)
    lifetime_deposits = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    lifetime_wagering = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Tier progression
    points_to_next_tier = models.PositiveIntegerField(default=0)
    next_tier_program = models.ForeignKey(LoyaltyProgram, on_delete=models.SET_NULL, null=True, blank=True, related_name='next_tier_users')

    # Benefits used this month
    free_withdrawals_used = models.PositiveIntegerField(default=0)
    last_withdrawal_reset = models.DateTimeField(auto_now_add=True)

    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'promotions_user_loyalty_status'

    def __str__(self):
        return f"{self.user.username} - {self.current_program.name if self.current_program else 'No Tier'}"

    def add_points(self, points, description, source_amount=None):
        """Add loyalty points to user"""
        self.total_points += points
        self.available_points += points

        # Create points record
        LoyaltyPoints.objects.create(
            user=self.user,
            points_type='EARNED',
            points=points,
            balance_after=self.available_points,
            source_description=description,
            source_amount=source_amount
        )

        # Check for tier upgrade
        self.check_tier_upgrade()
        self.save()

    def redeem_points(self, points, description):
        """Redeem loyalty points"""
        if self.available_points >= points:
            self.available_points -= points

            # Create redemption record
            LoyaltyPoints.objects.create(
                user=self.user,
                points_type='REDEEMED',
                points=-points,
                balance_after=self.available_points,
                source_description=description
            )

            self.save()
            return True
        return False

    def check_tier_upgrade(self):
        """Check if user qualifies for tier upgrade"""
        next_tier = LoyaltyProgram.objects.filter(
            is_active=True,
            min_points_required__lte=self.total_points,
            min_deposit_amount__lte=self.lifetime_deposits,
            min_wagering_amount__lte=self.lifetime_wagering
        ).order_by('-min_points_required').first()

        if next_tier and next_tier != self.current_program:
            self.current_program = next_tier

            # Calculate points to next tier
            higher_tier = LoyaltyProgram.objects.filter(
                is_active=True,
                min_points_required__gt=self.total_points
            ).order_by('min_points_required').first()

            if higher_tier:
                self.points_to_next_tier = higher_tier.min_points_required - self.total_points
                self.next_tier_program = higher_tier
            else:
                self.points_to_next_tier = 0
                self.next_tier_program = None


class PromoCode(models.Model):
    """Promotional codes system"""
    CODE_TYPE_CHOICES = [
        ('SINGLE_USE', 'Single Use'),
        ('MULTI_USE', 'Multi Use'),
        ('USER_SPECIFIC', 'User Specific'),
        ('UNLIMITED', 'Unlimited'),
    ]

    CODE_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('EXPIRED', 'Expired'),
        ('EXHAUSTED', 'Exhausted'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=50, unique=True)
    promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE, related_name='promo_codes')

    # Code configuration
    code_type = models.CharField(max_length=20, choices=CODE_TYPE_CHOICES, default='MULTI_USE')
    max_uses = models.PositiveIntegerField(null=True, blank=True)
    max_uses_per_user = models.PositiveIntegerField(default=1)

    # Targeting
    specific_users = models.ManyToManyField(User, blank=True, related_name='assigned_promo_codes')

    # Validity
    valid_from = models.DateTimeField()
    valid_until = models.DateTimeField()

    # Status
    status = models.CharField(max_length=20, choices=CODE_STATUS_CHOICES, default='ACTIVE')
    total_uses = models.PositiveIntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_promo_codes')

    class Meta:
        db_table = 'promotions_promo_codes'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.code} - {self.promotion.name}"

    @property
    def is_valid(self):
        """Check if promo code is currently valid"""
        now = timezone.now()
        return (
            self.status == 'ACTIVE' and
            self.valid_from <= now <= self.valid_until and
            (not self.max_uses or self.total_uses < self.max_uses)
        )

    def can_user_use(self, user):
        """Check if user can use this promo code"""
        if not self.is_valid:
            return False, "Promo code is not valid"

        # Check user-specific codes
        if self.code_type == 'USER_SPECIFIC':
            if user not in self.specific_users.all():
                return False, "This code is not for your account"

        # Check usage limits
        user_uses = self.promo_code_uses.filter(user=user).count()
        if user_uses >= self.max_uses_per_user:
            return False, f"You have already used this code {self.max_uses_per_user} time(s)"

        return True, "Valid"

    def use_code(self, user):
        """Use the promo code"""
        can_use, message = self.can_user_use(user)
        if not can_use:
            return False, message

        # Create usage record
        PromoCodeUse.objects.create(
            promo_code=self,
            user=user
        )

        # Update usage count
        self.total_uses += 1

        # Check if exhausted
        if self.max_uses and self.total_uses >= self.max_uses:
            self.status = 'EXHAUSTED'

        self.save()
        return True, "Code used successfully"


class PromoCodeUse(models.Model):
    """Track promo code usage"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    promo_code = models.ForeignKey(PromoCode, on_delete=models.CASCADE, related_name='promo_code_uses')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='promo_code_uses')
    bonus = models.ForeignKey(Bonus, on_delete=models.SET_NULL, null=True, blank=True)

    used_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        db_table = 'promotions_promo_code_uses'
        ordering = ['-used_at']
        unique_together = ['promo_code', 'user']

    def __str__(self):
        return f"{self.user.username} used {self.promo_code.code}"


class PromotionalCampaign(models.Model):
    """Marketing campaigns for promotions"""
    CAMPAIGN_TYPE_CHOICES = [
        ('EMAIL', 'Email Campaign'),
        ('SMS', 'SMS Campaign'),
        ('PUSH', 'Push Notification'),
        ('BANNER', 'Website Banner'),
        ('POPUP', 'Website Popup'),
        ('SOCIAL', 'Social Media'),
    ]

    CAMPAIGN_STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('SCHEDULED', 'Scheduled'),
        ('ACTIVE', 'Active'),
        ('PAUSED', 'Paused'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE, related_name='campaigns')

    # Campaign configuration
    campaign_type = models.CharField(max_length=20, choices=CAMPAIGN_TYPE_CHOICES)
    target_audience = models.CharField(max_length=20, choices=Promotion.TARGET_AUDIENCE_CHOICES, default='ALL')
    specific_users = models.ManyToManyField(User, blank=True, related_name='targeted_campaigns')

    # Content
    subject = models.CharField(max_length=255, blank=True)
    message = models.TextField()
    banner_image = models.ImageField(upload_to='campaigns/banners/', null=True, blank=True)
    call_to_action = models.CharField(max_length=100, blank=True)

    # Scheduling
    scheduled_at = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=CAMPAIGN_STATUS_CHOICES, default='DRAFT')

    # Statistics
    total_sent = models.PositiveIntegerField(default=0)
    total_opened = models.PositiveIntegerField(default=0)
    total_clicked = models.PositiveIntegerField(default=0)
    total_conversions = models.PositiveIntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_campaigns')

    class Meta:
        db_table = 'promotions_campaigns'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_campaign_type_display()})"


class PromotionalBanner(models.Model):
    """Website promotional banners"""
    BANNER_POSITION_CHOICES = [
        ('HEADER', 'Header Banner'),
        ('SIDEBAR', 'Sidebar Banner'),
        ('FOOTER', 'Footer Banner'),
        ('POPUP', 'Popup Banner'),
        ('HOMEPAGE_HERO', 'Homepage Hero'),
        ('CASINO_PAGE', 'Casino Page'),
        ('SPORTS_PAGE', 'Sports Page'),
    ]

    BANNER_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('SCHEDULED', 'Scheduled'),
        ('EXPIRED', 'Expired'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE, null=True, blank=True, related_name='banners')

    # Banner configuration
    position = models.CharField(max_length=20, choices=BANNER_POSITION_CHOICES)
    title = models.CharField(max_length=255)
    subtitle = models.CharField(max_length=255, blank=True)
    description = models.TextField(blank=True)

    # Media
    banner_image = models.ImageField(upload_to='banners/')
    mobile_image = models.ImageField(upload_to='banners/mobile/', null=True, blank=True)

    # Action
    call_to_action_text = models.CharField(max_length=50, default='Learn More')
    link_url = models.URLField(blank=True)

    # Display settings
    priority = models.PositiveIntegerField(default=0)
    show_from = models.DateTimeField()
    show_until = models.DateTimeField()

    # Targeting
    target_audience = models.CharField(max_length=20, choices=Promotion.TARGET_AUDIENCE_CHOICES, default='ALL')

    # Status
    status = models.CharField(max_length=20, choices=BANNER_STATUS_CHOICES, default='ACTIVE')

    # Statistics
    impressions = models.PositiveIntegerField(default=0)
    clicks = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'promotions_banners'
        ordering = ['-priority', '-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_position_display()})"

    @property
    def is_active(self):
        """Check if banner is currently active"""
        now = timezone.now()
        return (
            self.status == 'ACTIVE' and
            self.show_from <= now <= self.show_until
        )

    @property
    def click_through_rate(self):
        """Calculate click-through rate"""
        if self.impressions > 0:
            return (self.clicks / self.impressions) * 100
        return 0


class PromotionAnalytics(models.Model):
    """Daily analytics for promotions"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()

    # Usage statistics
    total_claims = models.PositiveIntegerField(default=0)
    unique_users = models.PositiveIntegerField(default=0)
    total_bonus_awarded = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Conversion statistics
    total_deposits = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_wagering = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    completed_wagering = models.PositiveIntegerField(default=0)

    # Performance metrics
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    average_bonus_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'promotions_analytics'
        unique_together = ['promotion', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.promotion.name} - {self.date}"
