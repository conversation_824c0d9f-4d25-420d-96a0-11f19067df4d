# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
media/
staticfiles/
static/
logs/
*.sqlite3
*.db

# Local development
local_settings.py
.env.local
.env.development
.env.production

# Backup files
*.bak
*.backup

# Temporary files
*.tmp
*.temp

# Node modules (if using any frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Pytest
.pytest_cache/

# Celery
celerybeat-schedule.db

# Redis dump
dump.rdb

# Docker
.dockerignore
docker-compose.override.yml

# Certificates
*.pem
*.key
*.crt

# Payment gateway test files
test_payments/
sandbox_data/

# Sports data cache
sports_cache/
odds_cache/

# User uploads
uploads/
user_documents/

# Logs
*.log
logs/

# Database backups
*.sql
*.dump

# Cache
.cache/
cache/

# Temporary Django files
*.pyc
*.pyo
*.pyd
__pycache__/
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
*.cover
.hypothesis
.pytest_cache

# Local settings
local_settings.py

# Migrations (uncomment if you want to ignore migrations)
# */migrations/*.py
# !*/migrations/__init__.py

# Static files (collected)
/static/
/staticfiles/

# Media files
/media/

# Webpack bundles
/assets/bundles/
/webpack-stats.json

# Sass
.sass-cache/
*.css.map

# Bower
bower_components/

# Grunt
.grunt/

# Gulp
.gulp/

# NPM
npm-debug.log
node_modules/

# Yarn
yarn-error.log
.yarn-integrity

# dotenv
.env*
!.env.example

# virtualenv
.venv
venv/
ENV/
env/
.env

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# PyCharm
.idea/

# VSCode
.vscode/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.stackdump
