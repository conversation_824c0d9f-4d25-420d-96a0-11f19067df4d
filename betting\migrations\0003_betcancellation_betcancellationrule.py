# Generated by Django 5.2.4 on 2025-07-05 20:34

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("betting", "0002_betsettlementrule_settlement_settlementhistory"),
        ("sports", "0002_remove_match_statistics_match_stats_data_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BetCancellation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("cancellation_id", models.CharField(max_length=20, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("requested", "Cancellation Requested"),
                            ("approved", "Cancellation Approved"),
                            ("rejected", "Cancellation Rejected"),
                            ("processed", "Cancellation Processed"),
                        ],
                        default="requested",
                        max_length=20,
                    ),
                ),
                (
                    "reason",
                    models.Char<PERSON>ield(
                        choices=[
                            ("user_request", "User Request"),
                            ("match_postponed", "Match Postponed"),
                            ("match_cancelled", "Match Cancelled"),
                            ("technical_issue", "Technical Issue"),
                            ("odds_error", "Odds Error"),
                            ("admin_action", "Admin Action"),
                        ],
                        default="user_request",
                        max_length=20,
                    ),
                ),
                ("reason_details", models.TextField(blank=True)),
                (
                    "refund_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "cancellation_fee",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "net_refund",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("processing_notes", models.TextField(blank=True)),
                ("requested_at", models.DateTimeField(auto_now_add=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "bet",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cancellation",
                        to="betting.bet",
                    ),
                ),
                (
                    "processed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="processed_cancellations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Bet Cancellation",
                "verbose_name_plural": "Bet Cancellations",
                "db_table": "betting_cancellation",
                "ordering": ["-requested_at"],
            },
        ),
        migrations.CreateModel(
            name="BetCancellationRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("rule_name", models.CharField(max_length=200)),
                (
                    "rule_type",
                    models.CharField(
                        choices=[
                            ("time_based", "Time-based Cancellation"),
                            ("match_status", "Match Status Based"),
                            ("odds_change", "Odds Change Based"),
                            ("user_tier", "User Tier Based"),
                            ("bet_amount", "Bet Amount Based"),
                            ("custom", "Custom Rule"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "rule_config",
                    models.JSONField(
                        default=dict, help_text="Rule configuration parameters"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("priority", models.PositiveIntegerField(default=0)),
                (
                    "cancellation_fee_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Percentage fee for cancellation",
                        max_digits=5,
                    ),
                ),
                (
                    "minimum_fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Minimum cancellation fee",
                        max_digits=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "bet_types",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Applicable bet types",
                        to="betting.bettype",
                    ),
                ),
                (
                    "sports",
                    models.ManyToManyField(
                        blank=True, help_text="Applicable sports", to="sports.sport"
                    ),
                ),
            ],
            options={
                "verbose_name": "Cancellation Rule",
                "verbose_name_plural": "Cancellation Rules",
                "db_table": "betting_cancellation_rule",
                "ordering": ["priority", "rule_name"],
            },
        ),
    ]
