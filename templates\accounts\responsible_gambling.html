{% extends 'base.html' %}

{% block title %}Responsible Gambling - ZBet{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card">
                <div class="card-header zbet-primary text-white">
                    <h6 class="mb-0">Account Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'accounts:profile_update' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user"></i> Profile
                    </a>
                    <a href="{% url 'accounts:responsible_gambling' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-shield-alt"></i> Responsible Gambling
                    </a>
                    <a href="{% url 'accounts:self_exclusion' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-ban"></i> Self-Exclusion
                    </a>
                    <a href="{% url 'accounts:account_closure' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-times-circle"></i> Account Closure
                    </a>
                    <a href="{% url 'accounts:notification_preferences' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Responsible Gambling Settings</h2>
                    <p class="text-muted">Set limits to help you gamble responsibly</p>
                </div>
                <div>
                    <span class="badge bg-info fs-6">
                        <i class="fas fa-shield-alt"></i> Protected
                    </span>
                </div>
            </div>

            <!-- Important Notice -->
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> Important Information</h6>
                <p class="mb-0">
                    These limits are designed to help you maintain control over your gambling. 
                    Once set, limits can only be increased after a 24-hour cooling-off period.
                    Decreases take effect immediately.
                </p>
            </div>

            <form method="post" id="responsibleGamblingForm">
                {% csrf_token %}
                
                <!-- Deposit Limits -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card"></i> Deposit Limits</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.daily_deposit_limit }}
                                    <label for="{{ form.daily_deposit_limit.id_for_label }}">Daily Limit (KSh)</label>
                                    {% if form.daily_deposit_limit.errors %}
                                        <div class="text-danger small">{{ form.daily_deposit_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.weekly_deposit_limit }}
                                    <label for="{{ form.weekly_deposit_limit.id_for_label }}">Weekly Limit (KSh)</label>
                                    {% if form.weekly_deposit_limit.errors %}
                                        <div class="text-danger small">{{ form.weekly_deposit_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.monthly_deposit_limit }}
                                    <label for="{{ form.monthly_deposit_limit.id_for_label }}">Monthly Limit (KSh)</label>
                                    {% if form.monthly_deposit_limit.errors %}
                                        <div class="text-danger small">{{ form.monthly_deposit_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Betting Limits -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-dice"></i> Betting Limits</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.daily_bet_limit }}
                                    <label for="{{ form.daily_bet_limit.id_for_label }}">Daily Limit (KSh)</label>
                                    {% if form.daily_bet_limit.errors %}
                                        <div class="text-danger small">{{ form.daily_bet_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.weekly_bet_limit }}
                                    <label for="{{ form.weekly_bet_limit.id_for_label }}">Weekly Limit (KSh)</label>
                                    {% if form.weekly_bet_limit.errors %}
                                        <div class="text-danger small">{{ form.weekly_bet_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.monthly_bet_limit }}
                                    <label for="{{ form.monthly_bet_limit.id_for_label }}">Monthly Limit (KSh)</label>
                                    {% if form.monthly_bet_limit.errors %}
                                        <div class="text-danger small">{{ form.monthly_bet_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loss Limits -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line-down"></i> Loss Limits</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.daily_loss_limit }}
                                    <label for="{{ form.daily_loss_limit.id_for_label }}">Daily Limit (KSh)</label>
                                    {% if form.daily_loss_limit.errors %}
                                        <div class="text-danger small">{{ form.daily_loss_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.weekly_loss_limit }}
                                    <label for="{{ form.weekly_loss_limit.id_for_label }}">Weekly Limit (KSh)</label>
                                    {% if form.weekly_loss_limit.errors %}
                                        <div class="text-danger small">{{ form.weekly_loss_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    {{ form.monthly_loss_limit }}
                                    <label for="{{ form.monthly_loss_limit.id_for_label }}">Monthly Limit (KSh)</label>
                                    {% if form.monthly_loss_limit.errors %}
                                        <div class="text-danger small">{{ form.monthly_loss_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Session Limits -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock"></i> Session Limits</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.session_time_limit }}
                                    <label for="{{ form.session_time_limit.id_for_label }}">Session Time Limit (Minutes)</label>
                                    {% if form.session_time_limit.errors %}
                                        <div class="text-danger small">{{ form.session_time_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.daily_session_limit }}
                                    <label for="{{ form.daily_session_limit.id_for_label }}">Daily Session Limit (Minutes)</label>
                                    {% if form.daily_session_limit.errors %}
                                        <div class="text-danger small">{{ form.daily_session_limit.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reality Checks -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bell"></i> Reality Checks</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    {{ form.reality_check_enabled }}
                                    <label class="form-check-label" for="{{ form.reality_check_enabled.id_for_label }}">
                                        Enable Reality Check Reminders
                                    </label>
                                    {% if form.reality_check_enabled.errors %}
                                        <div class="text-danger small">{{ form.reality_check_enabled.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.reality_check_interval }}
                                    <label for="{{ form.reality_check_interval.id_for_label }}">Reminder Interval (Minutes)</label>
                                    {% if form.reality_check_interval.errors %}
                                        <div class="text-danger small">{{ form.reality_check_interval.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                    <button type="submit" class="btn btn-zbet btn-lg">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </div>
            </form>

            <!-- Additional Resources -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Need Help?</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <i class="fas fa-phone fa-2x text-primary mb-2"></i>
                            <h6>Gambling Helpline</h6>
                            <p class="text-muted">Call: 0800-123-456</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="fas fa-globe fa-2x text-success mb-2"></i>
                            <h6>Online Support</h6>
                            <p class="text-muted">Visit: gamblinghelp.ke</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="fas fa-ban fa-2x text-warning mb-2"></i>
                            <h6>Self-Exclusion</h6>
                            <a href="{% url 'accounts:self_exclusion' %}" class="btn btn-outline-warning btn-sm">
                                Exclude Yourself
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    $('#responsibleGamblingForm').on('submit', function(e) {
        // Add any client-side validation if needed
    });
    
    // Reality check toggle
    $('#{{ form.reality_check_enabled.id_for_label }}').on('change', function() {
        const intervalField = $('#{{ form.reality_check_interval.id_for_label }}');
        if ($(this).is(':checked')) {
            intervalField.prop('disabled', false);
        } else {
            intervalField.prop('disabled', true);
        }
    });
    
    // Initialize reality check state
    $('#{{ form.reality_check_enabled.id_for_label }}').trigger('change');
});
</script>
{% endblock %}
