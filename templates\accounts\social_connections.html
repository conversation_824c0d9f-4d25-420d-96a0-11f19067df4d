{% extends 'base.html' %}

{% block title %}Social Connections - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Social Connections</h3>
                <p class="mb-0">Manage your connected social accounts</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-link fa-3x text-primary mb-3"></i>
                    <h5>Connected Accounts</h5>
                    <p class="text-muted">
                        Link your social accounts for easier login and enhanced security.
                    </p>
                </div>
                
                {% load socialaccount %}
                {% get_social_accounts user as accounts %}
                
                <!-- Google Account -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <i class="fab fa-google fa-2x text-danger"></i>
                            </div>
                            <div class="col">
                                <h6 class="mb-1">Google</h6>
                                {% if accounts.google %}
                                    {% for account in accounts.google %}
                                        <p class="text-muted mb-0">
                                            Connected as: {{ account.extra_data.email }}
                                        </p>
                                        <small class="text-success">
                                            <i class="fas fa-check-circle"></i> Connected
                                        </small>
                                    {% endfor %}
                                {% else %}
                                    <p class="text-muted mb-0">Not connected</p>
                                {% endif %}
                            </div>
                            <div class="col-auto">
                                {% if accounts.google %}
                                    <form method="post" action="{% url 'socialaccount_connections' %}" style="display: inline;">
                                        {% csrf_token %}
                                        {% for account in accounts.google %}
                                            <input type="hidden" name="account" value="{{ account.id }}">
                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                Disconnect
                                            </button>
                                        {% endfor %}
                                    </form>
                                {% else %}
                                    <a href="{% provider_login_url 'google' process='connect' %}" class="btn btn-outline-danger btn-sm">
                                        Connect
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Facebook Account -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <i class="fab fa-facebook-f fa-2x text-primary"></i>
                            </div>
                            <div class="col">
                                <h6 class="mb-1">Facebook</h6>
                                {% if accounts.facebook %}
                                    {% for account in accounts.facebook %}
                                        <p class="text-muted mb-0">
                                            Connected as: {{ account.extra_data.name }}
                                        </p>
                                        <small class="text-success">
                                            <i class="fas fa-check-circle"></i> Connected
                                        </small>
                                    {% endfor %}
                                {% else %}
                                    <p class="text-muted mb-0">Not connected</p>
                                {% endif %}
                            </div>
                            <div class="col-auto">
                                {% if accounts.facebook %}
                                    <form method="post" action="{% url 'socialaccount_connections' %}" style="display: inline;">
                                        {% csrf_token %}
                                        {% for account in accounts.facebook %}
                                            <input type="hidden" name="account" value="{{ account.id }}">
                                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                                Disconnect
                                            </button>
                                        {% endfor %}
                                    </form>
                                {% else %}
                                    <a href="{% provider_login_url 'facebook' process='connect' %}" class="btn btn-outline-primary btn-sm">
                                        Connect
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Benefits Section -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Benefits of Connecting Social Accounts</h6>
                    <ul class="mb-0">
                        <li>Faster login without remembering passwords</li>
                        <li>Enhanced account security</li>
                        <li>Account recovery options</li>
                        <li>Seamless experience across devices</li>
                    </ul>
                </div>
                
                <!-- Security Notice -->
                <div class="alert alert-warning">
                    <h6><i class="fas fa-shield-alt"></i> Security Notice</h6>
                    <p class="mb-0">
                        Connecting social accounts allows you to log in using those services. 
                        Make sure your social accounts are secure and use strong passwords.
                    </p>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:dashboard' %}" class="btn btn-outline-secondary">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Additional Security -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-lock"></i> Additional Security</h6>
            </div>
            <div class="card-body">
                <p class="mb-2">
                    <strong>Two-Factor Authentication:</strong> 
                    {% load two_factor_tags %}
                    {% if user|is_2fa_enabled %}
                        <span class="text-success"><i class="fas fa-check"></i> Enabled</span>
                    {% else %}
                        <span class="text-warning"><i class="fas fa-times"></i> Disabled</span>
                    {% endif %}
                </p>
                <p class="mb-0">
                    <a href="{% url 'accounts:two_factor_settings' %}" class="btn btn-sm btn-outline-primary">
                        Manage 2FA Settings
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Confirm disconnection
    $('form[action*="connections"] button[type="submit"]').on('click', function(e) {
        const provider = $(this).closest('.card').find('h6').text().trim();
        if (!confirm(`Are you sure you want to disconnect your ${provider} account?`)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
