from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db.models import Q, Sum, Count
from django.contrib import messages
from decimal import Decimal
import json
import uuid

from .models import (
    Promotion, Bonus, Cashback, ReferralBonus, PromoCode,
    LoyaltyProgram, UserLoyaltyStatus, PromotionalBanner,
    BonusType
)
from payments.models import Wallet


def promotions_home(request):
    """Promotions homepage"""
    # Get active promotions
    active_promotions = Promotion.objects.filter(
        status='ACTIVE',
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now()
    ).order_by('-is_featured', '-priority')[:12]

    # Get featured promotions
    featured_promotions = active_promotions.filter(is_featured=True)[:6]

    # Get promotional banners
    banners = PromotionalBanner.objects.filter(
        status='ACTIVE',
        show_from__lte=timezone.now(),
        show_until__gte=timezone.now(),
        position='HOMEPAGE_HERO'
    ).order_by('-priority')[:3]

    # Get user-specific data if logged in
    user_bonuses = []
    user_loyalty = None
    if request.user.is_authenticated:
        user_bonuses = Bonus.objects.filter(
            user=request.user,
            status__in=['ACTIVE', 'WAGERING']
        ).select_related('bonus_type', 'promotion')[:5]

        try:
            user_loyalty = UserLoyaltyStatus.objects.get(user=request.user)
        except UserLoyaltyStatus.DoesNotExist:
            pass

    context = {
        'active_promotions': active_promotions,
        'featured_promotions': featured_promotions,
        'banners': banners,
        'user_bonuses': user_bonuses,
        'user_loyalty': user_loyalty,
    }
    return render(request, 'promotions/home.html', context)


def promotion_detail(request, slug):
    """Promotion detail page"""
    promotion = get_object_or_404(
        Promotion,
        slug=slug,
        status='ACTIVE',
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now()
    )

    # Check if user can claim
    can_claim = False
    claim_message = ""
    if request.user.is_authenticated:
        can_claim, claim_message = promotion.can_user_claim(request.user)

    # Get related promotions
    related_promotions = Promotion.objects.filter(
        status='ACTIVE',
        bonus_type=promotion.bonus_type,
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now()
    ).exclude(id=promotion.id)[:4]

    context = {
        'promotion': promotion,
        'can_claim': can_claim,
        'claim_message': claim_message,
        'related_promotions': related_promotions,
    }
    return render(request, 'promotions/detail.html', context)


@login_required
def claim_promotion(request, slug):
    """Claim a promotion"""
    promotion = get_object_or_404(
        Promotion,
        slug=slug,
        status='ACTIVE',
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now()
    )

    # Check if user can claim
    can_claim, claim_message = promotion.can_user_claim(request.user)
    if not can_claim:
        messages.error(request, claim_message)
        return redirect('promotions:detail', slug=slug)

    # Check minimum deposit requirement
    if promotion.min_deposit_amount > 0:
        try:
            wallet = Wallet.objects.get(user=request.user)
            # This would need to check recent deposits - simplified for now
            if wallet.balance < promotion.min_deposit_amount:
                messages.error(request, f'Minimum deposit of KES {promotion.min_deposit_amount} required')
                return redirect('promotions:detail', slug=slug)
        except Wallet.DoesNotExist:
            messages.error(request, 'Wallet not found')
            return redirect('promotions:detail', slug=slug)

    # Create bonus
    bonus_amount = promotion.calculate_bonus(promotion.min_deposit_amount or Decimal('100.00'))

    bonus = Bonus.objects.create(
        user=request.user,
        promotion=promotion,
        bonus_type=promotion.bonus_type,
        source='PROMOTION',
        reference=f'PROMO-{uuid.uuid4().hex[:8].upper()}',
        bonus_amount=bonus_amount,
        original_amount=promotion.min_deposit_amount or Decimal('100.00'),
        remaining_amount=bonus_amount,
        wagering_requirement=bonus_amount * promotion.wagering_requirement,
        terms_accepted=True
    )

    # Activate bonus
    bonus.activate()

    # Update promotion statistics
    promotion.total_uses += 1
    promotion.total_bonus_awarded += bonus_amount
    promotion.save(update_fields=['total_uses', 'total_bonus_awarded'])

    messages.success(request, f'Congratulations! You have claimed KES {bonus_amount} bonus.')
    return redirect('promotions:my_bonuses')


@login_required
def use_promo_code(request):
    """Use a promo code"""
    if request.method == 'POST':
        code = request.POST.get('code', '').strip().upper()

        if not code:
            messages.error(request, 'Please enter a promo code')
            return redirect('promotions:home')

        try:
            promo_code = PromoCode.objects.get(code=code)

            # Check if user can use the code
            can_use, message = promo_code.can_user_use(request.user)
            if not can_use:
                messages.error(request, message)
                return redirect('promotions:home')

            # Use the code
            success, message = promo_code.use_code(request.user)
            if not success:
                messages.error(request, message)
                return redirect('promotions:home')

            # Create bonus from promotion
            promotion = promo_code.promotion
            bonus_amount = promotion.calculate_bonus(promotion.min_deposit_amount or Decimal('100.00'))

            bonus = Bonus.objects.create(
                user=request.user,
                promotion=promotion,
                bonus_type=promotion.bonus_type,
                source='PROMOTION',
                reference=f'CODE-{uuid.uuid4().hex[:8].upper()}',
                bonus_amount=bonus_amount,
                original_amount=promotion.min_deposit_amount or Decimal('100.00'),
                remaining_amount=bonus_amount,
                wagering_requirement=bonus_amount * promotion.wagering_requirement,
                terms_accepted=True
            )

            # Activate bonus
            bonus.activate()

            # Update promo code usage
            from .models import PromoCodeUse
            PromoCodeUse.objects.create(
                promo_code=promo_code,
                user=request.user,
                bonus=bonus,
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, f'Promo code applied! You received KES {bonus_amount} bonus.')
            return redirect('promotions:my_bonuses')

        except PromoCode.DoesNotExist:
            messages.error(request, 'Invalid promo code')
            return redirect('promotions:home')

    return redirect('promotions:home')


@login_required
def my_bonuses(request):
    """User's bonuses page"""
    # Get user bonuses
    bonuses = Bonus.objects.filter(user=request.user).select_related(
        'bonus_type', 'promotion'
    ).order_by('-awarded_at')

    # Get active bonuses
    active_bonuses = bonuses.filter(status__in=['ACTIVE', 'WAGERING'])

    # Get bonus statistics
    stats = {
        'total_bonuses': bonuses.count(),
        'active_bonuses': active_bonuses.count(),
        'total_awarded': bonuses.aggregate(total=Sum('bonus_amount'))['total'] or 0,
        'total_wagered': bonuses.aggregate(total=Sum('wagered_amount'))['total'] or 0,
    }

    context = {
        'bonuses': bonuses,
        'active_bonuses': active_bonuses,
        'stats': stats,
    }
    return render(request, 'promotions/my_bonuses.html', context)


@login_required
def loyalty_program(request):
    """Loyalty program page"""
    # Get or create user loyalty status
    user_loyalty, created = UserLoyaltyStatus.objects.get_or_create(
        user=request.user,
        defaults={
            'total_points': 0,
            'available_points': 0,
            'lifetime_deposits': 0,
            'lifetime_wagering': 0,
        }
    )

    # Get all loyalty programs
    loyalty_programs = LoyaltyProgram.objects.filter(is_active=True).order_by('min_points_required')

    # Get recent points activity
    from .models import LoyaltyPoints
    recent_points = LoyaltyPoints.objects.filter(user=request.user).order_by('-created_at')[:10]

    context = {
        'user_loyalty': user_loyalty,
        'loyalty_programs': loyalty_programs,
        'recent_points': recent_points,
    }
    return render(request, 'promotions/loyalty.html', context)


@login_required
def referral_program(request):
    """Referral program page"""
    # Get user's referral code (create if doesn't exist)
    referral_code = f"REF{request.user.id:06d}"

    # Get user's referrals
    referrals = ReferralBonus.objects.filter(referrer=request.user).order_by('-created_at')

    # Get referral statistics
    stats = {
        'total_referrals': referrals.count(),
        'qualified_referrals': referrals.filter(status='QUALIFIED').count(),
        'awarded_referrals': referrals.filter(status='AWARDED').count(),
        'total_earned': referrals.filter(status='AWARDED').aggregate(
            total=Sum('referrer_bonus')
        )['total'] or 0,
    }

    context = {
        'referral_code': referral_code,
        'referrals': referrals,
        'stats': stats,
    }
    return render(request, 'promotions/referral.html', context)


@login_required
@require_http_methods(["POST"])
def forfeit_bonus(request, bonus_id):
    """Forfeit a bonus"""
    try:
        bonus = Bonus.objects.get(
            id=bonus_id,
            user=request.user,
            status__in=['ACTIVE', 'WAGERING']
        )

        bonus.forfeit("User forfeited")

        return JsonResponse({
            'success': True,
            'message': 'Bonus forfeited successfully'
        })

    except Bonus.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Bonus not found or cannot be forfeited'
        })


# API Endpoints
@csrf_exempt
@require_http_methods(["GET"])
def api_active_promotions(request):
    """API endpoint for active promotions"""
    promotions = Promotion.objects.filter(
        status='ACTIVE',
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now()
    ).order_by('-is_featured', '-priority')

    promotions_data = []
    for promotion in promotions:
        promotions_data.append({
            'id': str(promotion.id),
            'name': promotion.name,
            'slug': promotion.slug,
            'description': promotion.short_description or promotion.description[:200],
            'promotion_type': promotion.promotion_type,
            'percentage_value': float(promotion.percentage_value) if promotion.percentage_value else None,
            'fixed_amount': float(promotion.fixed_amount) if promotion.fixed_amount else None,
            'max_bonus_amount': float(promotion.max_bonus_amount) if promotion.max_bonus_amount else None,
            'min_deposit_amount': float(promotion.min_deposit_amount),
            'is_featured': promotion.is_featured,
            'days_remaining': promotion.days_remaining,
            'banner_image': promotion.banner_image.url if promotion.banner_image else None,
        })

    return JsonResponse({
        'success': True,
        'promotions': promotions_data
    })


@csrf_exempt
@require_http_methods(["GET"])
def api_promotional_banners(request):
    """API endpoint for promotional banners"""
    position = request.GET.get('position', 'HOMEPAGE_HERO')

    banners = PromotionalBanner.objects.filter(
        status='ACTIVE',
        position=position,
        show_from__lte=timezone.now(),
        show_until__gte=timezone.now()
    ).order_by('-priority')

    banners_data = []
    for banner in banners:
        banners_data.append({
            'id': str(banner.id),
            'name': banner.name,
            'title': banner.title,
            'subtitle': banner.subtitle,
            'description': banner.description,
            'banner_image': banner.banner_image.url if banner.banner_image else None,
            'mobile_image': banner.mobile_image.url if banner.mobile_image else None,
            'call_to_action_text': banner.call_to_action_text,
            'link_url': banner.link_url,
        })

    return JsonResponse({
        'success': True,
        'banners': banners_data
    })


@login_required
@csrf_exempt
@require_http_methods(["GET"])
def api_user_bonuses(request):
    """API endpoint for user bonuses"""
    bonuses = Bonus.objects.filter(user=request.user).select_related(
        'bonus_type', 'promotion'
    ).order_by('-awarded_at')

    bonuses_data = []
    for bonus in bonuses:
        bonuses_data.append({
            'id': str(bonus.id),
            'reference': bonus.reference,
            'bonus_type': bonus.bonus_type.name,
            'source': bonus.get_source_display(),
            'bonus_amount': float(bonus.bonus_amount),
            'remaining_amount': float(bonus.remaining_amount),
            'wagering_requirement': float(bonus.wagering_requirement),
            'wagered_amount': float(bonus.wagered_amount),
            'remaining_wagering': float(bonus.remaining_wagering),
            'wagering_progress': bonus.wagering_progress,
            'status': bonus.get_status_display(),
            'awarded_at': bonus.awarded_at.isoformat(),
            'expires_at': bonus.expires_at.isoformat() if bonus.expires_at else None,
            'promotion_name': bonus.promotion.name if bonus.promotion else None,
        })

    return JsonResponse({
        'success': True,
        'bonuses': bonuses_data
    })


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def api_add_wagering(request):
    """API endpoint to add wagering progress to bonuses"""
    try:
        data = json.loads(request.body)
        amount = Decimal(str(data.get('amount', 0)))
        game_type = data.get('game_type', 'sports')  # 'sports' or 'casino'

        if amount <= 0:
            return JsonResponse({
                'success': False,
                'error': 'Invalid amount'
            })

        # Get active bonuses for user
        active_bonuses = Bonus.objects.filter(
            user=request.user,
            status__in=['ACTIVE', 'WAGERING']
        ).order_by('awarded_at')  # FIFO

        updated_bonuses = []
        for bonus in active_bonuses:
            if bonus.remaining_wagering > 0:
                # Get contribution percentage based on game type
                if game_type == 'sports':
                    contribution_pct = bonus.promotion.wagering_contribution_sports if bonus.promotion else 100
                else:
                    contribution_pct = bonus.promotion.wagering_contribution_casino if bonus.promotion else 100

                bonus.add_wagering(amount, contribution_pct)
                updated_bonuses.append({
                    'id': str(bonus.id),
                    'wagering_progress': bonus.wagering_progress,
                    'remaining_wagering': float(bonus.remaining_wagering),
                    'status': bonus.get_status_display(),
                })

        return JsonResponse({
            'success': True,
            'updated_bonuses': updated_bonuses
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def cashback_calculator(request):
    """Cashback calculator page"""
    # Get cashback promotions
    cashback_promotions = Promotion.objects.filter(
        status='ACTIVE',
        promotion_type='CASHBACK',
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now()
    )

    # Get user's recent cashbacks if logged in
    user_cashbacks = []
    if request.user.is_authenticated:
        user_cashbacks = Cashback.objects.filter(user=request.user).order_by('-created_at')[:5]

    context = {
        'cashback_promotions': cashback_promotions,
        'user_cashbacks': user_cashbacks,
    }
    return render(request, 'promotions/cashback.html', context)
