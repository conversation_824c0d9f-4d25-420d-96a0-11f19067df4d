from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    BonusType, Promotion, Bonus, Cashback, ReferralBonus,
    LoyaltyProgram, LoyaltyPoints, UserLoyaltyStatus,
    PromoCode, PromoCodeUse, PromotionalCampaign,
    PromotionalBanner, PromotionAnalytics
)


@admin.register(BonusType)
class BonusTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'is_active', 'promotions_count', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'created_at']

    def promotions_count(self, obj):
        return obj.promotions.count()
    promotions_count.short_description = 'Promotions'


@admin.register(Promotion)
class PromotionAdmin(admin.ModelAdmin):
    list_display = ['name', 'promotion_type', 'status', 'target_audience', 'start_date', 'end_date', 'total_uses', 'total_bonus_awarded']
    list_filter = ['promotion_type', 'status', 'target_audience', 'bonus_type', 'is_featured', 'start_date']
    search_fields = ['name', 'slug', 'description']
    readonly_fields = ['id', 'slug', 'total_uses', 'total_bonus_awarded', 'created_at', 'updated_at']
    filter_horizontal = ['specific_users']
    prepopulated_fields = {'slug': ('name',)}
    actions = ['activate_promotions', 'deactivate_promotions', 'duplicate_promotions']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'short_description', 'bonus_type')
        }),
        ('Promotion Configuration', {
            'fields': ('promotion_type', 'percentage_value', 'fixed_amount', 'max_bonus_amount', 'min_deposit_amount')
        }),
        ('Targeting', {
            'fields': ('target_audience', 'specific_users')
        }),
        ('Validity & Limits', {
            'fields': ('start_date', 'end_date', 'is_unlimited', 'max_uses_total', 'max_uses_per_user')
        }),
        ('Wagering Requirements', {
            'fields': ('wagering_requirement', 'wagering_contribution_sports', 'wagering_contribution_casino', 'min_odds'),
            'classes': ('collapse',)
        }),
        ('Display Settings', {
            'fields': ('status', 'is_featured', 'priority', 'banner_image', 'thumbnail_image')
        }),
        ('Terms & Conditions', {
            'fields': ('terms_and_conditions',),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': ('total_uses', 'total_bonus_awarded'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        })
    )

    def activate_promotions(self, request, queryset):
        count = queryset.update(status='ACTIVE')
        self.message_user(request, f'Successfully activated {count} promotions.')
    activate_promotions.short_description = 'Activate selected promotions'

    def deactivate_promotions(self, request, queryset):
        count = queryset.update(status='PAUSED')
        self.message_user(request, f'Successfully paused {count} promotions.')
    deactivate_promotions.short_description = 'Pause selected promotions'


@admin.register(Bonus)
class BonusAdmin(admin.ModelAdmin):
    list_display = ['reference', 'user', 'bonus_type', 'source', 'bonus_amount', 'status', 'wagering_progress_display', 'awarded_at']
    list_filter = ['status', 'source', 'bonus_type', 'awarded_at']
    search_fields = ['reference', 'user__username', 'user__email']
    readonly_fields = ['id', 'reference', 'wagering_progress', 'awarded_at', 'activated_at', 'completed_at']
    actions = ['activate_bonuses', 'forfeit_bonuses']

    fieldsets = (
        ('Bonus Information', {
            'fields': ('user', 'promotion', 'bonus_type', 'source', 'reference')
        }),
        ('Amounts', {
            'fields': ('bonus_amount', 'original_amount', 'remaining_amount')
        }),
        ('Wagering', {
            'fields': ('wagering_requirement', 'wagered_amount', 'remaining_wagering', 'wagering_progress')
        }),
        ('Status & Dates', {
            'fields': ('status', 'awarded_at', 'activated_at', 'expires_at', 'completed_at')
        }),
        ('Additional Info', {
            'fields': ('terms_accepted', 'notes', 'metadata'),
            'classes': ('collapse',)
        })
    )

    def wagering_progress_display(self, obj):
        progress = obj.wagering_progress
        color = 'green' if progress >= 100 else 'orange' if progress >= 50 else 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, progress
        )
    wagering_progress_display.short_description = 'Wagering Progress'

    def activate_bonuses(self, request, queryset):
        count = 0
        for bonus in queryset.filter(status='PENDING'):
            bonus.activate()
            count += 1
        self.message_user(request, f'Successfully activated {count} bonuses.')
    activate_bonuses.short_description = 'Activate selected bonuses'

    def forfeit_bonuses(self, request, queryset):
        count = 0
        for bonus in queryset.filter(status__in=['ACTIVE', 'WAGERING']):
            bonus.forfeit("Admin forfeited")
            count += 1
        self.message_user(request, f'Successfully forfeited {count} bonuses.')
    forfeit_bonuses.short_description = 'Forfeit selected bonuses'


@admin.register(Cashback)
class CashbackAdmin(admin.ModelAdmin):
    list_display = ['user', 'cashback_type', 'period_start', 'period_end', 'total_losses', 'cashback_amount', 'status']
    list_filter = ['cashback_type', 'status', 'period_start']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['id', 'calculated_at', 'awarded_at', 'created_at']
    actions = ['calculate_cashbacks', 'award_cashbacks']

    def calculate_cashbacks(self, request, queryset):
        count = 0
        for cashback in queryset.filter(status='PENDING'):
            cashback.calculate_cashback()
            count += 1
        self.message_user(request, f'Successfully calculated {count} cashbacks.')
    calculate_cashbacks.short_description = 'Calculate selected cashbacks'

    def award_cashbacks(self, request, queryset):
        count = 0
        for cashback in queryset.filter(status='CALCULATED'):
            if cashback.award_cashback():
                count += 1
        self.message_user(request, f'Successfully awarded {count} cashbacks.')
    award_cashbacks.short_description = 'Award selected cashbacks'


@admin.register(ReferralBonus)
class ReferralBonusAdmin(admin.ModelAdmin):
    list_display = ['referrer', 'referred_user', 'referral_code', 'status', 'referrer_bonus', 'referred_bonus', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['referrer__username', 'referred_user__username', 'referral_code']
    readonly_fields = ['id', 'created_at', 'qualified_at', 'awarded_at', 'expires_at']
    actions = ['check_qualifications', 'award_bonuses']

    def check_qualifications(self, request, queryset):
        count = 0
        for referral in queryset.filter(status='PENDING'):
            if referral.check_qualification():
                count += 1
        self.message_user(request, f'Successfully qualified {count} referrals.')
    check_qualifications.short_description = 'Check qualification for selected referrals'

    def award_bonuses(self, request, queryset):
        count = 0
        for referral in queryset.filter(status='QUALIFIED'):
            referral.award_bonuses()
            count += 1
        self.message_user(request, f'Successfully awarded bonuses for {count} referrals.')
    award_bonuses.short_description = 'Award bonuses for qualified referrals'


@admin.register(LoyaltyProgram)
class LoyaltyProgramAdmin(admin.ModelAdmin):
    list_display = ['name', 'tier', 'min_points_required', 'points_multiplier', 'cashback_percentage', 'is_active', 'users_count']
    list_filter = ['tier', 'is_active', 'created_at']
    search_fields = ['name']
    readonly_fields = ['id', 'created_at']
    ordering = ['priority', 'min_points_required']

    def users_count(self, obj):
        return obj.userloyaltystatus_set.count()
    users_count.short_description = 'Users in Tier'


@admin.register(LoyaltyPoints)
class LoyaltyPointsAdmin(admin.ModelAdmin):
    list_display = ['user', 'points_type', 'points', 'balance_after', 'source_description', 'created_at']
    list_filter = ['points_type', 'created_at']
    search_fields = ['user__username', 'source_description']
    readonly_fields = ['id', 'created_at']


@admin.register(UserLoyaltyStatus)
class UserLoyaltyStatusAdmin(admin.ModelAdmin):
    list_display = ['user', 'current_program', 'total_points', 'available_points', 'lifetime_deposits', 'lifetime_wagering']
    list_filter = ['current_program', 'updated_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['points_to_next_tier', 'updated_at']


@admin.register(PromoCode)
class PromoCodeAdmin(admin.ModelAdmin):
    list_display = ['code', 'promotion', 'code_type', 'status', 'total_uses', 'max_uses', 'valid_from', 'valid_until']
    list_filter = ['code_type', 'status', 'valid_from']
    search_fields = ['code', 'promotion__name']
    readonly_fields = ['id', 'total_uses', 'created_at']
    filter_horizontal = ['specific_users']
    actions = ['activate_codes', 'deactivate_codes']

    def activate_codes(self, request, queryset):
        count = queryset.update(status='ACTIVE')
        self.message_user(request, f'Successfully activated {count} promo codes.')
    activate_codes.short_description = 'Activate selected promo codes'

    def deactivate_codes(self, request, queryset):
        count = queryset.update(status='INACTIVE')
        self.message_user(request, f'Successfully deactivated {count} promo codes.')
    deactivate_codes.short_description = 'Deactivate selected promo codes'


@admin.register(PromoCodeUse)
class PromoCodeUseAdmin(admin.ModelAdmin):
    list_display = ['user', 'promo_code', 'bonus', 'used_at', 'ip_address']
    list_filter = ['used_at', 'promo_code__promotion']
    search_fields = ['user__username', 'promo_code__code']
    readonly_fields = ['id', 'used_at']


@admin.register(PromotionalCampaign)
class PromotionalCampaignAdmin(admin.ModelAdmin):
    list_display = ['name', 'campaign_type', 'promotion', 'status', 'total_sent', 'total_opened', 'total_clicked', 'conversion_rate']
    list_filter = ['campaign_type', 'status', 'scheduled_at']
    search_fields = ['name', 'promotion__name']
    readonly_fields = ['id', 'sent_at', 'total_sent', 'total_opened', 'total_clicked', 'total_conversions', 'created_at']
    filter_horizontal = ['specific_users']

    def conversion_rate(self, obj):
        if obj.total_sent > 0:
            rate = (obj.total_conversions / obj.total_sent) * 100
            return f"{rate:.2f}%"
        return "0%"
    conversion_rate.short_description = 'Conversion Rate'


@admin.register(PromotionalBanner)
class PromotionalBannerAdmin(admin.ModelAdmin):
    list_display = ['name', 'position', 'promotion', 'status', 'show_from', 'show_until', 'impressions', 'clicks', 'ctr']
    list_filter = ['position', 'status', 'target_audience', 'show_from']
    search_fields = ['name', 'title']
    readonly_fields = ['id', 'impressions', 'clicks', 'click_through_rate', 'created_at']

    def ctr(self, obj):
        return f"{obj.click_through_rate:.2f}%"
    ctr.short_description = 'CTR'


@admin.register(PromotionAnalytics)
class PromotionAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['promotion', 'date', 'total_claims', 'unique_users', 'total_bonus_awarded', 'conversion_rate']
    list_filter = ['date', 'promotion']
    search_fields = ['promotion__name']
    readonly_fields = ['id', 'created_at']
    date_hierarchy = 'date'
