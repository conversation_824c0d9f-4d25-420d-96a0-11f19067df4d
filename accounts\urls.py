from django.urls import path
from . import views

app_name = 'accounts'

urlpatterns = [
    # Authentication URLs
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),

    # Password Reset URLs
    path('password-reset/', views.password_reset_view, name='password_reset'),
    path('password-reset-done/', views.password_reset_done_view, name='password_reset_done'),
    path('password-reset-confirm/<uidb64>/<token>/', views.password_reset_confirm_view, name='password_reset_confirm'),

    # Registration URLs
    path('register/', views.register_view, name='register'),
    path('verify-email/', views.verify_email_view, name='verify_email'),
    path('verify-phone/', views.verify_phone_view, name='verify_phone'),
    path('registration-complete/', views.registration_complete_view, name='registration_complete'),

    # AJAX endpoints for registration
    path('resend-verification/', views.resend_verification_view, name='resend_verification'),
    path('check-username/', views.check_username_availability, name='check_username'),
    path('check-email/', views.check_email_availability, name='check_email'),
    path('validate-referral/', views.validate_referral_code, name='validate_referral'),

    # Email Verification URLs
    path('request-email-verification/', views.request_email_verification_view, name='request_email_verification'),
    path('verify-email-standalone/', views.verify_email_standalone_view, name='verify_email_standalone'),
    path('change-email/', views.change_email_view, name='change_email'),
    path('verify-email-change/', views.verify_email_change_view, name='verify_email_change'),

    # SMS Verification URLs
    path('request-sms-verification/', views.request_sms_verification_view, name='request_sms_verification'),
    path('verify-sms-standalone/', views.verify_sms_standalone_view, name='verify_sms_standalone'),
    path('change-phone/', views.change_phone_view, name='change_phone'),
    path('verify-phone-change/', views.verify_phone_change_view, name='verify_phone_change'),
    path('resend-sms-verification/', views.resend_sms_verification_view, name='resend_sms_verification'),

    # Two-Factor Authentication URLs
    path('two-factor-login/', views.two_factor_login_view, name='two_factor_login'),
    path('enable-2fa/', views.enable_2fa_view, name='enable_2fa'),
    path('setup-totp/', views.setup_totp_view, name='setup_totp'),
    path('2fa-settings/', views.two_factor_settings_view, name='two_factor_settings'),
    path('disable-2fa/', views.disable_2fa_view, name='disable_2fa'),
    path('regenerate-backup-codes/', views.regenerate_backup_codes_view, name='regenerate_backup_codes'),
    path('view-backup-codes/', views.view_backup_codes_view, name='view_backup_codes'),
    path('resend-2fa-sms/', views.resend_2fa_sms_view, name='resend_2fa_sms'),

    # Social Connections
    path('social-connections/', views.social_connections_view, name='social_connections'),

    # Profile Management
    path('profile/update/', views.profile_update_view, name='profile_update'),
    path('profile/picture/', views.profile_picture_upload_view, name='profile_picture_upload'),
    path('profile/notifications/', views.notification_preferences_view, name='notification_preferences'),

    # Betting & Transactions
    path('betting-history/', views.betting_history_view, name='betting_history'),
    path('bet/<str:bet_id>/', views.bet_detail_view, name='bet_detail'),
    path('transactions/', views.transaction_history_view, name='transaction_history'),
    path('balance/', views.account_balance_view, name='account_balance'),

    # Responsible Gambling
    path('responsible-gambling/', views.responsible_gambling_view, name='responsible_gambling'),
    path('self-exclusion/', views.self_exclusion_view, name='self_exclusion'),
    path('account-closure/', views.account_closure_view, name='account_closure'),

    # KYC Verification
    path('kyc/', views.kyc_verification_view, name='kyc_verification'),
    path('kyc/upload/', views.kyc_document_upload_view, name='kyc_document_upload'),
    path('kyc/upload/<str:document_type>/', views.kyc_document_upload_view, name='kyc_document_upload_type'),
    path('kyc/delete/<int:document_id>/', views.kyc_document_delete_view, name='kyc_document_delete'),

    # Device Management
    path('devices/', views.device_management_view, name='device_management'),
    path('devices/trust/<int:device_id>/', views.device_trust_toggle_view, name='device_trust_toggle'),
    path('sessions/terminate/<int:session_id>/', views.session_terminate_view, name='session_terminate'),

    # Dashboard
    path('dashboard/', views.dashboard_view, name='dashboard'),
]
