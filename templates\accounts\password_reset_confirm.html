{% extends 'base.html' %}

{% block title %}Set New Password - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        {% if validlink %}
            <div class="card shadow">
                <div class="card-header zbet-primary text-white text-center">
                    <h3 class="mb-0">Set New Password</h3>
                    <p class="mb-0">Create a strong password for your account</p>
                </div>
                
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-lock fa-3x text-primary mb-3"></i>
                        <p class="text-muted">
                            Please enter your new password below.
                        </p>
                    </div>
                    
                    <form method="post" id="setPasswordForm">
                        {% csrf_token %}
                        
                        <!-- New Password -->
                        <div class="form-floating mb-3">
                            {{ form.new_password1 }}
                            <label for="{{ form.new_password1.id_for_label }}">{{ form.new_password1.label }}</label>
                            <div class="form-text">{{ form.new_password1.help_text }}</div>
                            {% if form.new_password1.errors %}
                                <div class="text-danger small">{{ form.new_password1.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Confirm Password -->
                        <div class="form-floating mb-3">
                            {{ form.new_password2 }}
                            <label for="{{ form.new_password2.id_for_label }}">{{ form.new_password2.label }}</label>
                            {% if form.new_password2.errors %}
                                <div class="text-danger small">{{ form.new_password2.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Password Strength Indicator -->
                        <div class="mb-3">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="passwordStrengthText" class="text-muted">Password strength</small>
                        </div>
                        
                        <!-- Non-field errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}
                        
                        <!-- Submit Button -->
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-zbet btn-lg">
                                Set New Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        {% else %}
            <div class="card shadow">
                <div class="card-header bg-danger text-white text-center">
                    <h3 class="mb-0">Invalid Reset Link</h3>
                    <p class="mb-0">This password reset link is invalid or expired</p>
                </div>
                
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle fa-4x text-danger mb-3"></i>
                        <h4>Link Expired or Invalid</h4>
                        <p class="text-muted">
                            This password reset link is either invalid or has expired.
                            Password reset links are only valid for 1 hour for security reasons.
                        </p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> What to do:</h6>
                        <ul class="list-unstyled mb-0 text-start">
                            <li>✓ Request a new password reset</li>
                            <li>✓ Check your email for the new link</li>
                            <li>✓ Use the link within 1 hour</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:password_reset' %}" class="btn btn-zbet">
                            Request New Reset Link
                        </a>
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-secondary">
                            Back to Login
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if validlink %}
<script>
$(document).ready(function() {
    // Auto-focus on password field
    $('#{{ form.new_password1.id_for_label }}').focus();
    
    // Password strength checker
    $('#{{ form.new_password1.id_for_label }}').on('input', function() {
        const password = $(this).val();
        let strength = 0;
        let strengthText = '';
        let strengthClass = '';
        
        if (password.length >= 8) strength += 25;
        if (/[a-z]/.test(password)) strength += 25;
        if (/[A-Z]/.test(password)) strength += 25;
        if (/[0-9]/.test(password)) strength += 25;
        
        if (strength < 50) {
            strengthText = 'Weak';
            strengthClass = 'bg-danger';
        } else if (strength < 75) {
            strengthText = 'Fair';
            strengthClass = 'bg-warning';
        } else if (strength < 100) {
            strengthText = 'Good';
            strengthClass = 'bg-info';
        } else {
            strengthText = 'Strong';
            strengthClass = 'bg-success';
        }
        
        $('#passwordStrength').css('width', strength + '%').removeClass().addClass('progress-bar ' + strengthClass);
        $('#passwordStrengthText').text('Password strength: ' + strengthText);
    });
    
    // Form validation
    $('#setPasswordForm').on('submit', function(e) {
        const password1 = $('#{{ form.new_password1.id_for_label }}').val();
        const password2 = $('#{{ form.new_password2.id_for_label }}').val();
        
        if (!password1) {
            e.preventDefault();
            alert('Please enter a new password.');
            $('#{{ form.new_password1.id_for_label }}').focus();
            return false;
        }
        
        if (password1 !== password2) {
            e.preventDefault();
            alert('Passwords do not match.');
            $('#{{ form.new_password2.id_for_label }}').focus();
            return false;
        }
    });
});
</script>
{% endif %}
{% endblock %}
