{% extends 'base.html' %}
{% load static %}

{% block title %}M-Pesa Deposit - ZBet{% endblock %}

{% block extra_css %}
<style>
/* Additional mobile-specific styles */
.mobile-mpesa-container {
    padding-top: 80px;
    padding-bottom: 20px;
}

@media (max-width: 767px) {
    .mobile-mpesa-container {
        padding-top: 60px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="mobile-mpesa-container">
    <!-- M-Pesa Header -->
    <div class="mobile-mpesa-header">
        <div class="mobile-mpesa-logo">M</div>
        <h1 class="mobile-mpesa-title">M-Pesa Deposit</h1>
        <p class="mobile-mpesa-subtitle">Add money to your ZBet wallet instantly</p>
    </div>

    <!-- M-Pesa Form -->
    <form class="mobile-mpesa-form" id="mpesa-deposit-form">
        {% csrf_token %}
        
        <!-- Phone Number Input -->
        <div class="mobile-form-group">
            <label for="mobile-phone-input" class="mobile-form-label">
                <i class="fas fa-phone"></i> Phone Number
            </label>
            <div class="mobile-phone-input">
                <span class="mobile-phone-prefix">+254</span>
                <input 
                    type="tel" 
                    id="mobile-phone-input" 
                    class="mobile-form-input" 
                    placeholder="7XXXXXXXX"
                    maxlength="9"
                    pattern="[7][0-9]{8}"
                    required
                >
            </div>
            <small style="color: #aaa; font-size: 0.8rem; margin-top: 4px; display: block;">
                Enter your Safaricom number (e.g., 712345678)
            </small>
        </div>

        <!-- Amount Input -->
        <div class="mobile-form-group">
            <label for="mobile-amount-input" class="mobile-form-label">
                <i class="fas fa-money-bill"></i> Amount
            </label>
            <div class="mobile-amount-input">
                <span class="mobile-currency-symbol">KES</span>
                <input 
                    type="number" 
                    id="mobile-amount-input" 
                    class="mobile-form-input" 
                    placeholder="0.00"
                    min="10"
                    max="150000"
                    step="0.01"
                    required
                >
            </div>
            <small style="color: #aaa; font-size: 0.8rem; margin-top: 4px; display: block;">
                Minimum: KES 10 | Maximum: KES 150,000
            </small>
        </div>

        <!-- Quick Amount Buttons -->
        <div class="mobile-quick-amounts">
            <button type="button" class="mobile-quick-amount" data-amount="100">
                <div class="mobile-quick-amount-value">100</div>
                <div class="mobile-quick-amount-label">KES</div>
            </button>
            <button type="button" class="mobile-quick-amount" data-amount="500">
                <div class="mobile-quick-amount-value">500</div>
                <div class="mobile-quick-amount-label">KES</div>
            </button>
            <button type="button" class="mobile-quick-amount" data-amount="1000">
                <div class="mobile-quick-amount-value">1,000</div>
                <div class="mobile-quick-amount-label">KES</div>
            </button>
            <button type="button" class="mobile-quick-amount" data-amount="2000">
                <div class="mobile-quick-amount-value">2,000</div>
                <div class="mobile-quick-amount-label">KES</div>
            </button>
            <button type="button" class="mobile-quick-amount" data-amount="5000">
                <div class="mobile-quick-amount-value">5,000</div>
                <div class="mobile-quick-amount-label">KES</div>
            </button>
            <button type="button" class="mobile-quick-amount" data-amount="10000">
                <div class="mobile-quick-amount-value">10,000</div>
                <div class="mobile-quick-amount-label">KES</div>
            </button>
        </div>

        <!-- Submit Button -->
        <button type="submit" class="mobile-mpesa-submit" disabled>
            <i class="fas fa-mobile-alt"></i>
            <span>Send M-Pesa Request</span>
        </button>

        <!-- Information -->
        <div style="background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 16px; margin-top: 20px;">
            <h4 style="color: #ffd700; font-size: 1rem; margin-bottom: 12px;">
                <i class="fas fa-info-circle"></i> How it works
            </h4>
            <ol style="color: #ccc; font-size: 0.9rem; line-height: 1.6; padding-left: 20px;">
                <li>Enter your Safaricom phone number and amount</li>
                <li>Click "Send M-Pesa Request" and enter your M-Pesa PIN</li>
                <li>You'll receive an M-Pesa prompt on your phone</li>
                <li>Enter your M-Pesa PIN to complete the transaction</li>
                <li>Your ZBet wallet will be credited instantly</li>
            </ol>
        </div>

        <!-- Security Notice -->
        <div style="background: rgba(0, 166, 81, 0.1); border: 1px solid #00a651; border-radius: 8px; padding: 12px; margin-top: 16px;">
            <p style="color: #00a651; font-size: 0.8rem; margin: 0; text-align: center;">
                <i class="fas fa-shield-alt"></i>
                Your transaction is secured with 256-bit SSL encryption
            </p>
        </div>
    </form>
</div>

<!-- PIN Entry Popup -->
<div class="mobile-pin-overlay">
    <div class="mobile-pin-popup">
        <h3 class="mobile-pin-title">Enter M-Pesa PIN</h3>
        <p class="mobile-pin-subtitle">Enter your 4-digit M-Pesa PIN to complete the transaction</p>
        
        <!-- PIN Display -->
        <div class="mobile-pin-display">
            <div class="mobile-pin-dot"></div>
            <div class="mobile-pin-dot"></div>
            <div class="mobile-pin-dot"></div>
            <div class="mobile-pin-dot"></div>
        </div>
        
        <!-- PIN Keypad -->
        <div class="mobile-pin-keypad">
            <button type="button" class="mobile-pin-key" data-value="1">1</button>
            <button type="button" class="mobile-pin-key" data-value="2">2</button>
            <button type="button" class="mobile-pin-key" data-value="3">3</button>
            <button type="button" class="mobile-pin-key" data-value="4">4</button>
            <button type="button" class="mobile-pin-key" data-value="5">5</button>
            <button type="button" class="mobile-pin-key" data-value="6">6</button>
            <button type="button" class="mobile-pin-key" data-value="7">7</button>
            <button type="button" class="mobile-pin-key" data-value="8">8</button>
            <button type="button" class="mobile-pin-key" data-value="9">9</button>
            <div></div>
            <button type="button" class="mobile-pin-key" data-value="0">0</button>
            <button type="button" class="mobile-pin-key delete" data-value="delete">
                <i class="fas fa-backspace"></i>
            </button>
        </div>
        
        <!-- PIN Actions -->
        <div class="mobile-pin-actions">
            <button type="button" class="mobile-pin-cancel">Cancel</button>
            <button type="button" class="mobile-pin-confirm" disabled>Confirm</button>
        </div>
    </div>
</div>

<!-- Wallet Balance Widget -->
{% if user.is_authenticated %}
<div class="mobile-wallet-widget">
    <div class="mobile-wallet-header">
        <span class="mobile-wallet-title">Current Balance</span>
        <i class="fas fa-wallet mobile-wallet-icon"></i>
    </div>
    <div class="mobile-wallet-balance">
        KES {{ user.wallet.balance|floatformat:2|default:"0.00" }}
    </div>
    <div class="mobile-wallet-actions">
        <a href="{% url 'payments:transaction_history' %}" class="mobile-wallet-btn">
            <i class="fas fa-history"></i> History
        </a>
        <a href="{% url 'payments:withdrawal_request' %}" class="mobile-wallet-btn">
            <i class="fas fa-arrow-up"></i> Withdraw
        </a>
    </div>
</div>
{% endif %}

<!-- Install App Button (PWA) -->
<button id="install-app-button" style="display: none; position: fixed; bottom: 20px; left: 20px; background: #ffd700; color: #1a1a2e; border: none; border-radius: 50px; padding: 12px 20px; font-weight: bold; box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4); z-index: 1000;">
    <i class="fas fa-download"></i> Install App
</button>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile M-Pesa if the class exists
    if (window.MobileMPesa) {
        new MobileMPesa();
    }
    
    // Format amount input with commas
    const amountInput = document.getElementById('mobile-amount-input');
    if (amountInput) {
        amountInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/,/g, '');
            if (value && !isNaN(value)) {
                e.target.value = parseFloat(value).toLocaleString('en-KE', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                });
            }
        });
    }
    
    // Auto-focus phone input on mobile
    if (window.innerWidth <= 767) {
        const phoneInput = document.getElementById('mobile-phone-input');
        if (phoneInput) {
            setTimeout(() => {
                phoneInput.focus();
            }, 500);
        }
    }
    
    // Prevent form submission on Enter key (use button instead)
    const form = document.getElementById('mpesa-deposit-form');
    if (form) {
        form.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const submitButton = form.querySelector('.mobile-mpesa-submit');
                if (submitButton && !submitButton.disabled) {
                    submitButton.click();
                }
            }
        });
    }
    
    // Add haptic feedback for quick amount buttons
    const quickAmounts = document.querySelectorAll('.mobile-quick-amount');
    quickAmounts.forEach(button => {
        button.addEventListener('click', function() {
            // Trigger haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(10);
            }
        });
    });
    
    // Show success message if redirected from successful payment
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('success') === 'true') {
        if (window.mobileInteractions) {
            window.mobileInteractions.showToast('Deposit successful! Your wallet has been credited.', 'success', 5000);
        }
    }
    
    // Show error message if redirected from failed payment
    if (urlParams.get('error')) {
        const errorMessage = decodeURIComponent(urlParams.get('error'));
        if (window.mobileInteractions) {
            window.mobileInteractions.showToast(errorMessage, 'error', 5000);
        }
    }
});

// Handle online/offline status
window.addEventListener('online', function() {
    if (window.mobileInteractions) {
        window.mobileInteractions.showToast('Connection restored', 'success', 2000);
    }
});

window.addEventListener('offline', function() {
    if (window.mobileInteractions) {
        window.mobileInteractions.showToast('You are offline. Some features may not work.', 'error', 3000);
    }
});
</script>
{% endblock %}
