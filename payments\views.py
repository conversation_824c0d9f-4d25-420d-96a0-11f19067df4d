from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from .models import PaymentMethod, Transaction, Wallet, MPesaTransaction, PaymentLimit
import json
import requests
import uuid

@login_required
def initiate_deposit(request):
    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method_code = request.POST.get('payment_method')
        
        try:
            payment_method = PaymentMethod.objects.get(code=payment_method_code, is_active=True)
            
            # Validate amount against limits
            min_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MIN_DEPOSIT').amount
            max_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MAX_DEPOSIT').amount
            
            if float(amount) < float(min_limit) or float(amount) > float(max_limit):
                return JsonResponse({
                    'status': 'error',
                    'message': f'Amount must be between {min_limit} and {max_limit}'
                })
            
            # Create transaction record
            wallet = Wallet.objects.get(user=request.user)
            transaction = Transaction.objects.create(
                user=request.user,
                wallet=wallet,
                transaction_type='DEPOSIT',
                amount=amount,
                payment_method=payment_method,
                reference=f'DEP-{uuid.uuid4().hex[:8].upper()}'
            )
            
            if payment_method_code == 'MPESA':
                return initiate_mpesa_payment(request, transaction)
            elif payment_method_code == 'AIRTEL':
                return initiate_airtel_payment(request, transaction)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Unsupported payment method'
                })
                
        except (PaymentMethod.DoesNotExist, PaymentLimit.DoesNotExist, Wallet.DoesNotExist) as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            })
            
    payment_methods = PaymentMethod.objects.filter(is_active=True)
    return render(request, 'payments/deposit.html', {'payment_methods': payment_methods})

def initiate_mpesa_payment(request, transaction):
    phone_number = request.POST.get('phone_number')
    
    # Create MPesa transaction record
    mpesa_transaction = MPesaTransaction.objects.create(
        transaction=transaction,
        phone_number=phone_number,
        checkout_request_id=f'REQ-{uuid.uuid4().hex[:8].upper()}',
        merchant_request_id=f'MERCH-{uuid.uuid4().hex[:8].upper()}'
    )
    
    # TODO: Integrate with actual M-Pesa API
    # This is where you would make the API call to M-Pesa
    
    return JsonResponse({
        'status': 'success',
        'message': 'M-Pesa payment initiated',
        'transaction_id': transaction.reference
    })

def initiate_airtel_payment(request, transaction):
    phone_number = request.POST.get('phone_number')
    
    # TODO: Implement Airtel Money integration as backup
    # This is where you would integrate with Airtel Money API
    
    return JsonResponse({
        'status': 'success',
        'message': 'Airtel Money payment initiated',
        'transaction_id': transaction.reference
    })

@csrf_exempt
@require_http_methods(['POST'])
def mpesa_callback(request):
    try:
        callback_data = json.loads(request.body)
        # Process M-Pesa callback data
        # Update transaction status
        # Credit user wallet if successful
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def transaction_status(request, transaction_id):
    try:
        transaction = Transaction.objects.get(reference=transaction_id, user=request.user)
        return JsonResponse({
            'status': 'success',
            'transaction_status': transaction.status
        })
    except Transaction.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Transaction not found'
        })

@login_required
def withdrawal_request(request):
    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method_code = request.POST.get('payment_method')
        phone_number = request.POST.get('phone_number')
        
        try:
            payment_method = PaymentMethod.objects.get(code=payment_method_code, is_active=True)
            wallet = Wallet.objects.get(user=request.user)
            
            # Validate withdrawal amount and limits
            if float(amount) > float(wallet.balance):
                return JsonResponse({
                    'status': 'error',
                    'message': 'Insufficient balance'
                })
            
            min_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MIN_WITHDRAWAL').amount
            max_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MAX_WITHDRAWAL').amount
            
            if float(amount) < float(min_limit) or float(amount) > float(max_limit):
                return JsonResponse({
                    'status': 'error',
                    'message': f'Withdrawal amount must be between {min_limit} and {max_limit}'
                })
            
            # Create withdrawal transaction
            transaction = Transaction.objects.create(
                user=request.user,
                wallet=wallet,
                transaction_type='WITHDRAWAL',
                amount=amount,
                payment_method=payment_method,
                reference=f'WD-{uuid.uuid4().hex[:8].upper()}'
            )
            
            if payment_method_code == 'MPESA':
                # Process M-Pesa withdrawal
                # TODO: Implement actual M-Pesa B2C integration
                pass
            elif payment_method_code == 'AIRTEL':
                # Process Airtel Money withdrawal
                # TODO: Implement Airtel Money withdrawal
                pass
            
            return JsonResponse({
                'status': 'success',
                'message': 'Withdrawal request submitted',
                'transaction_id': transaction.reference
            })
            
        except (PaymentMethod.DoesNotExist, PaymentLimit.DoesNotExist, Wallet.DoesNotExist) as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            })
    
    payment_methods = PaymentMethod.objects.filter(is_active=True)
    return render(request, 'payments/withdrawal.html', {'payment_methods': payment_methods})
