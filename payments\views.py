from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.db import transaction as db_transaction
from django.utils import timezone
from django.contrib import messages
from .models import (
    PaymentMethod, Transaction, Wallet, MPesaTransaction,
    PaymentLimit, UserPaymentStats, PaymentNotification
)
from .services.mpesa_service import mpesa_service
import json
import uuid
import logging
from datetime import date
from decimal import Decimal

logger = logging.getLogger(__name__)

@login_required
def initiate_deposit(request):
    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method_code = request.POST.get('payment_method')
        phone_number = request.POST.get('phone_number', '')

        try:
            # Validate amount
            amount_decimal = Decimal(str(amount))
            if amount_decimal <= 0:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Amount must be greater than zero'
                })

            payment_method = PaymentMethod.objects.get(code=payment_method_code, is_active=True)

            # Validate amount against limits
            try:
                min_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MIN_DEPOSIT').amount
                max_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MAX_DEPOSIT').amount

                if amount_decimal < min_limit or amount_decimal > max_limit:
                    return JsonResponse({
                        'status': 'error',
                        'message': f'Amount must be between KES {min_limit} and KES {max_limit}'
                    })
            except PaymentLimit.DoesNotExist:
                # If no limits set, use default validation
                if amount_decimal < Decimal('10') or amount_decimal > Decimal('100000'):
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Amount must be between KES 10 and KES 100,000'
                    })

            # Get or create wallet
            wallet, created = Wallet.objects.get_or_create(user=request.user)

            # Check daily limits
            today = date.today()
            user_stats, created = UserPaymentStats.objects.get_or_create(
                user=request.user,
                date=today,
                defaults={'total_deposits': 0, 'deposit_count': 0}
            )

            # Check daily deposit limit if exists
            try:
                daily_limit = PaymentLimit.objects.get(
                    payment_method=payment_method,
                    limit_type='DAILY_DEPOSIT'
                ).amount

                if user_stats.total_deposits + amount_decimal > daily_limit:
                    return JsonResponse({
                        'status': 'error',
                        'message': f'Daily deposit limit of KES {daily_limit} would be exceeded'
                    })
            except PaymentLimit.DoesNotExist:
                pass

            # Create transaction record
            transaction = Transaction.objects.create(
                user=request.user,
                wallet=wallet,
                transaction_type='DEPOSIT',
                amount=amount_decimal,
                payment_method=payment_method,
                reference=f'DEP-{uuid.uuid4().hex[:8].upper()}',
                description=f'Deposit via {payment_method.name}',
                balance_before=wallet.balance
            )

            if payment_method_code == 'MPESA':
                return initiate_mpesa_payment(request, transaction, phone_number)
            elif payment_method_code == 'AIRTEL':
                return initiate_airtel_payment(request, transaction, phone_number)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Unsupported payment method'
                })

        except (PaymentMethod.DoesNotExist, ValueError) as e:
            logger.error(f"Deposit initiation error: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid payment request'
            })
        except Exception as e:
            logger.error(f"Unexpected error in deposit: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'Payment service temporarily unavailable'
            })

    # GET request - show deposit form
    payment_methods = PaymentMethod.objects.filter(is_active=True).order_by('priority')
    wallet, created = Wallet.objects.get_or_create(user=request.user)

    context = {
        'payment_methods': payment_methods,
        'wallet': wallet,
        'user': request.user
    }
    return render(request, 'payments/deposit.html', context)

def initiate_mpesa_payment(request, transaction, phone_number):
    """Initiate M-Pesa STK Push payment"""
    try:
        if not phone_number:
            return JsonResponse({
                'status': 'error',
                'message': 'Phone number is required for M-Pesa payment'
            })

        # Format phone number
        formatted_phone = mpesa_service.format_phone_number(phone_number)

        # Initiate STK Push
        stk_response = mpesa_service.initiate_stk_push(
            phone_number=formatted_phone,
            amount=transaction.amount,
            account_reference=transaction.reference,
            transaction_desc=f"ZBet Deposit - {transaction.reference}"
        )

        if stk_response['success']:
            # Create MPesa transaction record
            mpesa_transaction = MPesaTransaction.objects.create(
                transaction=transaction,
                phone_number=formatted_phone,
                checkout_request_id=stk_response['checkout_request_id'],
                merchant_request_id=stk_response['merchant_request_id'],
                account_reference=transaction.reference,
                transaction_desc=f"ZBet Deposit - {transaction.reference}"
            )

            # Update transaction status
            transaction.status = 'PROCESSING'
            transaction.save()

            return JsonResponse({
                'status': 'success',
                'message': 'Please check your phone and enter your M-Pesa PIN to complete the payment',
                'transaction_id': transaction.reference,
                'checkout_request_id': stk_response['checkout_request_id'],
                'customer_message': stk_response.get('customer_message', '')
            })
        else:
            # Mark transaction as failed
            transaction.mark_failed(stk_response.get('message', 'STK Push failed'))

            return JsonResponse({
                'status': 'error',
                'message': stk_response.get('message', 'Payment initiation failed'),
                'transaction_id': transaction.reference
            })

    except Exception as e:
        logger.error(f"M-Pesa payment initiation error: {str(e)}")
        transaction.mark_failed(f"System error: {str(e)}")

        return JsonResponse({
            'status': 'error',
            'message': 'Payment service temporarily unavailable',
            'transaction_id': transaction.reference
        })

def initiate_airtel_payment(request, transaction):
    phone_number = request.POST.get('phone_number')
    
    # TODO: Implement Airtel Money integration as backup
    # This is where you would integrate with Airtel Money API
    
    return JsonResponse({
        'status': 'success',
        'message': 'Airtel Money payment initiated',
        'transaction_id': transaction.reference
    })

@csrf_exempt
@require_http_methods(['POST'])
def mpesa_callback(request):
    """Handle M-Pesa payment callback"""
    try:
        callback_data = json.loads(request.body)
        logger.info(f"M-Pesa callback received: {callback_data}")

        # Process callback using service
        callback_result = mpesa_service.process_callback(callback_data)

        if callback_result['success']:
            checkout_request_id = callback_result['checkout_request_id']
            result_code = callback_result['result_code']
            result_desc = callback_result['result_desc']
            transaction_data = callback_result['transaction_data']

            try:
                # Find the M-Pesa transaction
                mpesa_transaction = MPesaTransaction.objects.get(
                    checkout_request_id=checkout_request_id
                )

                # Update M-Pesa transaction with callback data
                mpesa_transaction.result_code = result_code
                mpesa_transaction.result_description = result_desc

                if result_code == '0':  # Success
                    # Update with transaction details
                    mpesa_transaction.mpesa_receipt_number = transaction_data.get('mpesa_receipt_number')
                    mpesa_transaction.save()

                    # Update main transaction
                    transaction = mpesa_transaction.transaction
                    transaction.external_reference = transaction_data.get('mpesa_receipt_number')
                    transaction.callback_data = callback_data
                    transaction.mark_completed()

                    # Update user payment stats
                    today = date.today()
                    user_stats, created = UserPaymentStats.objects.get_or_create(
                        user=transaction.user,
                        date=today,
                        defaults={'total_deposits': 0, 'deposit_count': 0}
                    )
                    user_stats.total_deposits += transaction.amount
                    user_stats.deposit_count += 1
                    user_stats.save()

                    # Create success notification
                    PaymentNotification.objects.create(
                        user=transaction.user,
                        transaction=transaction,
                        notification_type='DEPOSIT_SUCCESS',
                        title='Deposit Successful',
                        message=f'Your deposit of KES {transaction.amount} has been processed successfully. Receipt: {transaction_data.get("mpesa_receipt_number")}'
                    )

                    logger.info(f"M-Pesa payment successful: {transaction.reference}")

                else:  # Failed
                    mpesa_transaction.save()

                    # Mark transaction as failed
                    transaction = mpesa_transaction.transaction
                    transaction.callback_data = callback_data
                    transaction.mark_failed(result_desc)

                    # Create failure notification
                    PaymentNotification.objects.create(
                        user=transaction.user,
                        transaction=transaction,
                        notification_type='DEPOSIT_FAILED',
                        title='Deposit Failed',
                        message=f'Your deposit of KES {transaction.amount} failed. Reason: {result_desc}'
                    )

                    logger.warning(f"M-Pesa payment failed: {transaction.reference} - {result_desc}")

                return JsonResponse({'ResultCode': 0, 'ResultDesc': 'Success'})

            except MPesaTransaction.DoesNotExist:
                logger.error(f"M-Pesa transaction not found for checkout_request_id: {checkout_request_id}")
                return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Transaction not found'})

        else:
            logger.error(f"Failed to process M-Pesa callback: {callback_result.get('message')}")
            return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Callback processing failed'})

    except Exception as e:
        logger.error(f"M-Pesa callback error: {str(e)}")
        return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Internal server error'})

@login_required
def transaction_status(request, transaction_id):
    """Get transaction status with detailed information"""
    try:
        transaction = Transaction.objects.get(reference=transaction_id, user=request.user)

        response_data = {
            'status': 'success',
            'transaction_status': transaction.status,
            'transaction_id': transaction.reference,
            'amount': str(transaction.amount),
            'payment_method': transaction.payment_method.name,
            'created_at': transaction.created_at.isoformat(),
            'updated_at': transaction.updated_at.isoformat()
        }

        # Add M-Pesa specific information if available
        if hasattr(transaction, 'mpesa_transaction'):
            mpesa_tx = transaction.mpesa_transaction
            response_data.update({
                'mpesa_receipt_number': mpesa_tx.mpesa_receipt_number,
                'phone_number': mpesa_tx.formatted_phone,
                'result_description': mpesa_tx.result_description
            })

        return JsonResponse(response_data)

    except Transaction.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Transaction not found'
        })


@login_required
def check_stk_status(request, transaction_id):
    """Check STK Push status for M-Pesa transactions"""
    try:
        transaction = Transaction.objects.get(reference=transaction_id, user=request.user)

        if not hasattr(transaction, 'mpesa_transaction'):
            return JsonResponse({
                'status': 'error',
                'message': 'Not an M-Pesa transaction'
            })

        mpesa_tx = transaction.mpesa_transaction

        # Query STK status from M-Pesa
        status_result = mpesa_service.query_stk_status(mpesa_tx.checkout_request_id)

        if status_result['success']:
            status_data = status_result['data']
            result_code = status_data.get('ResultCode')

            if result_code == '0':  # Success
                # Update transaction if not already completed
                if transaction.status != 'COMPLETED':
                    transaction.mark_completed()

                return JsonResponse({
                    'status': 'success',
                    'transaction_status': 'COMPLETED',
                    'message': 'Payment completed successfully'
                })
            elif result_code == '1032':  # Cancelled by user
                if transaction.status != 'CANCELLED':
                    transaction.status = 'CANCELLED'
                    transaction.save()

                return JsonResponse({
                    'status': 'success',
                    'transaction_status': 'CANCELLED',
                    'message': 'Payment was cancelled'
                })
            else:  # Other failure codes
                if transaction.status not in ['FAILED', 'CANCELLED']:
                    transaction.mark_failed(status_data.get('ResultDesc', 'Payment failed'))

                return JsonResponse({
                    'status': 'success',
                    'transaction_status': 'FAILED',
                    'message': status_data.get('ResultDesc', 'Payment failed')
                })
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Unable to check payment status'
            })

    except Transaction.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Transaction not found'
        })
    except Exception as e:
        logger.error(f"STK status check error: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': 'Status check failed'
        })

@login_required
def withdrawal_request(request):
    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method_code = request.POST.get('payment_method')
        phone_number = request.POST.get('phone_number')
        
        try:
            payment_method = PaymentMethod.objects.get(code=payment_method_code, is_active=True)
            wallet = Wallet.objects.get(user=request.user)
            
            # Validate withdrawal amount and limits
            if float(amount) > float(wallet.balance):
                return JsonResponse({
                    'status': 'error',
                    'message': 'Insufficient balance'
                })
            
            min_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MIN_WITHDRAWAL').amount
            max_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MAX_WITHDRAWAL').amount
            
            if float(amount) < float(min_limit) or float(amount) > float(max_limit):
                return JsonResponse({
                    'status': 'error',
                    'message': f'Withdrawal amount must be between {min_limit} and {max_limit}'
                })
            
            # Create withdrawal transaction
            transaction = Transaction.objects.create(
                user=request.user,
                wallet=wallet,
                transaction_type='WITHDRAWAL',
                amount=amount,
                payment_method=payment_method,
                reference=f'WD-{uuid.uuid4().hex[:8].upper()}'
            )
            
            if payment_method_code == 'MPESA':
                # Process M-Pesa withdrawal
                # TODO: Implement actual M-Pesa B2C integration
                pass
            elif payment_method_code == 'AIRTEL':
                # Process Airtel Money withdrawal
                # TODO: Implement Airtel Money withdrawal
                pass
            
            return JsonResponse({
                'status': 'success',
                'message': 'Withdrawal request submitted',
                'transaction_id': transaction.reference
            })
            
        except (PaymentMethod.DoesNotExist, PaymentLimit.DoesNotExist, Wallet.DoesNotExist) as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            })
    
    payment_methods = PaymentMethod.objects.filter(is_active=True)
    return render(request, 'payments/withdrawal.html', {'payment_methods': payment_methods})


@login_required
def wallet_dashboard(request):
    """Display user wallet dashboard with balance and recent transactions"""
    wallet, created = Wallet.objects.get_or_create(user=request.user)

    # Get recent transactions
    recent_transactions = Transaction.objects.filter(
        user=request.user
    ).select_related('payment_method').order_by('-created_at')[:10]

    # Get today's stats
    today = date.today()
    today_stats, created = UserPaymentStats.objects.get_or_create(
        user=request.user,
        date=today,
        defaults={'total_deposits': 0, 'total_withdrawals': 0, 'deposit_count': 0, 'withdrawal_count': 0}
    )

    # Get unread notifications
    unread_notifications = PaymentNotification.objects.filter(
        user=request.user,
        is_read=False
    ).order_by('-created_at')[:5]

    context = {
        'wallet': wallet,
        'recent_transactions': recent_transactions,
        'today_stats': today_stats,
        'unread_notifications': unread_notifications,
        'payment_methods': PaymentMethod.objects.filter(is_active=True).order_by('priority')
    }

    return render(request, 'payments/wallet_dashboard.html', context)


@login_required
def transaction_history(request):
    """Display user transaction history with filtering and pagination"""
    transactions = Transaction.objects.filter(
        user=request.user
    ).select_related('payment_method').order_by('-created_at')

    # Filter by transaction type if specified
    transaction_type = request.GET.get('type')
    if transaction_type and transaction_type in ['DEPOSIT', 'WITHDRAWAL', 'BET_PLACEMENT', 'BET_WINNING']:
        transactions = transactions.filter(transaction_type=transaction_type)

    # Filter by status if specified
    status = request.GET.get('status')
    if status and status in ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED']:
        transactions = transactions.filter(status=status)

    # Filter by date range if specified
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        transactions = transactions.filter(created_at__date__gte=date_from)
    if date_to:
        transactions = transactions.filter(created_at__date__lte=date_to)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(transactions, 20)  # Show 20 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'transaction_types': Transaction.TRANSACTION_TYPES,
        'status_choices': Transaction.STATUS_CHOICES,
        'current_filters': {
            'type': transaction_type,
            'status': status,
            'date_from': date_from,
            'date_to': date_to
        }
    }

    return render(request, 'payments/transaction_history.html', context)
