from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.db import transaction as db_transaction
from django.utils import timezone
from django.contrib import messages
from datetime import timedelta
from .models import (
    PaymentMethod, Transaction, Wallet, MPesaTransaction,
    PaymentLimit, UserPaymentStats, PaymentNotification,
    Deposit, Withdrawal
)
from django.core.paginator import Paginator
from django.db.models import Q
from .services.mpesa_service import mpesa_service
import json
import uuid
import logging
from datetime import date
from decimal import Decimal

logger = logging.getLogger(__name__)


def calculate_withdrawal_fee(amount, payment_method_code):
    """Calculate withdrawal fee based on amount and payment method"""
    amount = Decimal(str(amount))

    if payment_method_code == 'MPESA':
        # M-Pesa withdrawal fee structure
        if amount <= 100:
            return Decimal('0')
        elif amount <= 500:
            return Decimal('5')
        elif amount <= 1000:
            return Decimal('10')
        elif amount <= 5000:
            return Decimal('15')
        elif amount <= 10000:
            return Decimal('25')
        else:
            return Decimal('30')
    elif payment_method_code == 'AIRTEL':
        # Airtel Money fee structure (similar to M-Pesa)
        if amount <= 100:
            return Decimal('0')
        elif amount <= 500:
            return Decimal('5')
        elif amount <= 1000:
            return Decimal('10')
        else:
            return Decimal('15')
    else:
        # Default fee for other methods
        return Decimal('10')


def process_mpesa_withdrawal(withdrawal):
    """Process M-Pesa B2C withdrawal"""
    try:
        # Initiate M-Pesa B2C withdrawal
        result = mpesa_service.initiate_b2c_withdrawal(
            phone_number=withdrawal.phone_number,
            amount=withdrawal.net_amount,
            reference=withdrawal.reference,
            description=f"ZBet Withdrawal - {withdrawal.reference}"
        )

        if result['success']:
            logger.info(f"M-Pesa B2C withdrawal initiated successfully for {withdrawal.reference}")
            return result
        else:
            logger.error(f"M-Pesa B2C withdrawal failed for {withdrawal.reference}: {result.get('error')}")
            return result

    except Exception as e:
        logger.error(f"Error processing M-Pesa withdrawal {withdrawal.reference}: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }

@login_required
def initiate_deposit(request):
    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method_code = request.POST.get('payment_method')
        phone_number = request.POST.get('phone_number', '')

        try:
            # Validate amount
            amount_decimal = Decimal(str(amount))
            if amount_decimal <= 0:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Amount must be greater than zero'
                })

            payment_method = PaymentMethod.objects.get(code=payment_method_code, is_active=True)

            # Validate amount against limits
            try:
                min_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MIN_DEPOSIT').amount
                max_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MAX_DEPOSIT').amount

                if amount_decimal < min_limit or amount_decimal > max_limit:
                    return JsonResponse({
                        'status': 'error',
                        'message': f'Amount must be between KES {min_limit} and KES {max_limit}'
                    })
            except PaymentLimit.DoesNotExist:
                # If no limits set, use default validation
                if amount_decimal < Decimal('10') or amount_decimal > Decimal('100000'):
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Amount must be between KES 10 and KES 100,000'
                    })

            # Get or create wallet
            wallet, created = Wallet.objects.get_or_create(user=request.user)

            # Check daily limits
            today = date.today()
            user_stats, created = UserPaymentStats.objects.get_or_create(
                user=request.user,
                date=today,
                defaults={'total_deposits': 0, 'deposit_count': 0}
            )

            # Check daily deposit limit if exists
            try:
                daily_limit = PaymentLimit.objects.get(
                    payment_method=payment_method,
                    limit_type='DAILY_DEPOSIT'
                ).amount

                if user_stats.total_deposits + amount_decimal > daily_limit:
                    return JsonResponse({
                        'status': 'error',
                        'message': f'Daily deposit limit of KES {daily_limit} would be exceeded'
                    })
            except PaymentLimit.DoesNotExist:
                pass

            # Create transaction record
            transaction = Transaction.objects.create(
                user=request.user,
                wallet=wallet,
                transaction_type='DEPOSIT',
                amount=amount_decimal,
                payment_method=payment_method,
                reference=f'DEP-{uuid.uuid4().hex[:8].upper()}',
                description=f'Deposit via {payment_method.name}',
                balance_before=wallet.balance
            )

            if payment_method_code == 'MPESA':
                return initiate_mpesa_payment(request, transaction, phone_number)
            elif payment_method_code == 'AIRTEL':
                return initiate_airtel_payment(request, transaction, phone_number)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Unsupported payment method'
                })

        except (PaymentMethod.DoesNotExist, ValueError) as e:
            logger.error(f"Deposit initiation error: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid payment request'
            })
        except Exception as e:
            logger.error(f"Unexpected error in deposit: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'Payment service temporarily unavailable'
            })

    # GET request - show deposit form
    payment_methods = PaymentMethod.objects.filter(is_active=True).order_by('priority')
    wallet, created = Wallet.objects.get_or_create(user=request.user)

    context = {
        'payment_methods': payment_methods,
        'wallet': wallet,
        'user': request.user
    }
    return render(request, 'payments/deposit.html', context)

def initiate_mpesa_payment(request, transaction, phone_number):
    """Initiate M-Pesa STK Push payment"""
    try:
        if not phone_number:
            return JsonResponse({
                'status': 'error',
                'message': 'Phone number is required for M-Pesa payment'
            })

        # Format phone number
        formatted_phone = mpesa_service.format_phone_number(phone_number)

        # Initiate STK Push
        stk_response = mpesa_service.initiate_stk_push(
            phone_number=formatted_phone,
            amount=transaction.amount,
            account_reference=transaction.reference,
            transaction_desc=f"ZBet Deposit - {transaction.reference}"
        )

        if stk_response['success']:
            # Create MPesa transaction record
            mpesa_transaction = MPesaTransaction.objects.create(
                transaction=transaction,
                phone_number=formatted_phone,
                checkout_request_id=stk_response['checkout_request_id'],
                merchant_request_id=stk_response['merchant_request_id'],
                account_reference=transaction.reference,
                transaction_desc=f"ZBet Deposit - {transaction.reference}"
            )

            # Update transaction status
            transaction.status = 'PROCESSING'
            transaction.save()

            return JsonResponse({
                'status': 'success',
                'message': 'Please check your phone and enter your M-Pesa PIN to complete the payment',
                'transaction_id': transaction.reference,
                'checkout_request_id': stk_response['checkout_request_id'],
                'customer_message': stk_response.get('customer_message', '')
            })
        else:
            # Mark transaction as failed
            transaction.mark_failed(stk_response.get('message', 'STK Push failed'))

            return JsonResponse({
                'status': 'error',
                'message': stk_response.get('message', 'Payment initiation failed'),
                'transaction_id': transaction.reference
            })

    except Exception as e:
        logger.error(f"M-Pesa payment initiation error: {str(e)}")
        transaction.mark_failed(f"System error: {str(e)}")

        return JsonResponse({
            'status': 'error',
            'message': 'Payment service temporarily unavailable',
            'transaction_id': transaction.reference
        })

def initiate_airtel_payment(request, transaction):
    phone_number = request.POST.get('phone_number')
    
    # TODO: Implement Airtel Money integration as backup
    # This is where you would integrate with Airtel Money API
    
    return JsonResponse({
        'status': 'success',
        'message': 'Airtel Money payment initiated',
        'transaction_id': transaction.reference
    })

@csrf_exempt
@require_http_methods(['POST'])
def mpesa_callback(request):
    """Handle M-Pesa payment callback"""
    try:
        callback_data = json.loads(request.body)
        logger.info(f"M-Pesa callback received: {callback_data}")

        # Process callback using service
        callback_result = mpesa_service.process_callback(callback_data)

        if callback_result['success']:
            checkout_request_id = callback_result['checkout_request_id']
            result_code = callback_result['result_code']
            result_desc = callback_result['result_desc']
            transaction_data = callback_result['transaction_data']

            try:
                # Find the M-Pesa transaction
                mpesa_transaction = MPesaTransaction.objects.get(
                    checkout_request_id=checkout_request_id
                )

                # Update M-Pesa transaction with callback data
                mpesa_transaction.result_code = result_code
                mpesa_transaction.result_description = result_desc

                if result_code == '0':  # Success
                    # Update with transaction details
                    mpesa_transaction.mpesa_receipt_number = transaction_data.get('mpesa_receipt_number')
                    mpesa_transaction.save()

                    # Update main transaction
                    transaction = mpesa_transaction.transaction
                    transaction.external_reference = transaction_data.get('mpesa_receipt_number')
                    transaction.callback_data = callback_data
                    transaction.mark_completed()

                    # Update user payment stats
                    today = date.today()
                    user_stats, created = UserPaymentStats.objects.get_or_create(
                        user=transaction.user,
                        date=today,
                        defaults={'total_deposits': 0, 'deposit_count': 0}
                    )
                    user_stats.total_deposits += transaction.amount
                    user_stats.deposit_count += 1
                    user_stats.save()

                    # Create success notification
                    PaymentNotification.objects.create(
                        user=transaction.user,
                        transaction=transaction,
                        notification_type='DEPOSIT_SUCCESS',
                        title='Deposit Successful',
                        message=f'Your deposit of KES {transaction.amount} has been processed successfully. Receipt: {transaction_data.get("mpesa_receipt_number")}'
                    )

                    logger.info(f"M-Pesa payment successful: {transaction.reference}")

                else:  # Failed
                    mpesa_transaction.save()

                    # Mark transaction as failed
                    transaction = mpesa_transaction.transaction
                    transaction.callback_data = callback_data
                    transaction.mark_failed(result_desc)

                    # Create failure notification
                    PaymentNotification.objects.create(
                        user=transaction.user,
                        transaction=transaction,
                        notification_type='DEPOSIT_FAILED',
                        title='Deposit Failed',
                        message=f'Your deposit of KES {transaction.amount} failed. Reason: {result_desc}'
                    )

                    logger.warning(f"M-Pesa payment failed: {transaction.reference} - {result_desc}")

                return JsonResponse({'ResultCode': 0, 'ResultDesc': 'Success'})

            except MPesaTransaction.DoesNotExist:
                logger.error(f"M-Pesa transaction not found for checkout_request_id: {checkout_request_id}")
                return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Transaction not found'})

        else:
            logger.error(f"Failed to process M-Pesa callback: {callback_result.get('message')}")
            return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Callback processing failed'})

    except Exception as e:
        logger.error(f"M-Pesa callback error: {str(e)}")
        return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Internal server error'})

@login_required
def transaction_status(request, transaction_id):
    """Get transaction status with detailed information"""
    try:
        transaction = Transaction.objects.get(reference=transaction_id, user=request.user)

        response_data = {
            'status': 'success',
            'transaction_status': transaction.status,
            'transaction_id': transaction.reference,
            'amount': str(transaction.amount),
            'payment_method': transaction.payment_method.name,
            'created_at': transaction.created_at.isoformat(),
            'updated_at': transaction.updated_at.isoformat()
        }

        # Add M-Pesa specific information if available
        if hasattr(transaction, 'mpesa_transaction'):
            mpesa_tx = transaction.mpesa_transaction
            response_data.update({
                'mpesa_receipt_number': mpesa_tx.mpesa_receipt_number,
                'phone_number': mpesa_tx.formatted_phone,
                'result_description': mpesa_tx.result_description
            })

        return JsonResponse(response_data)

    except Transaction.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Transaction not found'
        })


@login_required
def check_stk_status(request, transaction_id):
    """Check STK Push status for M-Pesa transactions"""
    try:
        transaction = Transaction.objects.get(reference=transaction_id, user=request.user)

        if not hasattr(transaction, 'mpesa_transaction'):
            return JsonResponse({
                'status': 'error',
                'message': 'Not an M-Pesa transaction'
            })

        mpesa_tx = transaction.mpesa_transaction

        # Query STK status from M-Pesa
        status_result = mpesa_service.query_stk_status(mpesa_tx.checkout_request_id)

        if status_result['success']:
            status_data = status_result['data']
            result_code = status_data.get('ResultCode')

            if result_code == '0':  # Success
                # Update transaction if not already completed
                if transaction.status != 'COMPLETED':
                    transaction.mark_completed()

                return JsonResponse({
                    'status': 'success',
                    'transaction_status': 'COMPLETED',
                    'message': 'Payment completed successfully'
                })
            elif result_code == '1032':  # Cancelled by user
                if transaction.status != 'CANCELLED':
                    transaction.status = 'CANCELLED'
                    transaction.save()

                return JsonResponse({
                    'status': 'success',
                    'transaction_status': 'CANCELLED',
                    'message': 'Payment was cancelled'
                })
            else:  # Other failure codes
                if transaction.status not in ['FAILED', 'CANCELLED']:
                    transaction.mark_failed(status_data.get('ResultDesc', 'Payment failed'))

                return JsonResponse({
                    'status': 'success',
                    'transaction_status': 'FAILED',
                    'message': status_data.get('ResultDesc', 'Payment failed')
                })
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Unable to check payment status'
            })

    except Transaction.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Transaction not found'
        })
    except Exception as e:
        logger.error(f"STK status check error: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': 'Status check failed'
        })

@login_required
def withdrawal_request(request):
    """Handle withdrawal requests with M-Pesa B2C integration"""
    if request.method == 'POST':
        amount = request.POST.get('amount')
        payment_method_code = request.POST.get('payment_method')
        phone_number = request.POST.get('phone_number')

        try:
            # Validate inputs
            if not amount or not payment_method_code:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Amount and payment method are required'
                })

            amount = Decimal(str(amount))
            payment_method = PaymentMethod.objects.get(code=payment_method_code, is_active=True)
            wallet = Wallet.objects.get(user=request.user)

            # Validate M-Pesa phone number
            if payment_method_code == 'MPESA':
                if not phone_number:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Phone number is required for M-Pesa withdrawals'
                    })

                # Validate phone number format
                formatted_phone = mpesa_service.format_phone_number(phone_number)
                if not formatted_phone:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Invalid phone number format. Use 254XXXXXXXXX'
                    })
                phone_number = formatted_phone

            # Validate withdrawal amount and limits
            if amount > wallet.balance:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Insufficient balance'
                })

            try:
                min_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MIN_WITHDRAWAL').amount
                max_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MAX_WITHDRAWAL').amount

                if amount < min_limit or amount > max_limit:
                    return JsonResponse({
                        'status': 'error',
                        'message': f'Withdrawal amount must be between KES {min_limit} and KES {max_limit}'
                    })
            except PaymentLimit.DoesNotExist:
                # If no limits are set, allow the withdrawal
                pass

            # Calculate fees
            fee_amount = calculate_withdrawal_fee(amount, payment_method_code)
            net_amount = amount - fee_amount

            # Determine if approval is required
            approval_required = (
                amount >= Decimal('10000') or  # Large amounts
                payment_method_code in ['BANK', 'CARD'] or  # Specific methods
                request.user.withdrawals.filter(
                    created_at__date=timezone.now().date()
                ).count() >= 3  # Multiple withdrawals per day
            )

            # Create withdrawal record
            withdrawal = Withdrawal.objects.create(
                user=request.user,
                wallet=wallet,
                payment_method=payment_method,
                amount=amount,
                fee_amount=fee_amount,
                net_amount=net_amount,
                phone_number=phone_number,
                reference=f'WD-{uuid.uuid4().hex[:8].upper()}',
                description=f'Withdrawal via {payment_method.name}',
                approval_required=approval_required
            )

            # Process withdrawal based on approval requirement
            if approval_required:
                # Mark as pending for admin approval
                withdrawal.status = 'PENDING'
                withdrawal.save()

                # Create notification for pending approval
                withdrawal.create_notification('WITHDRAWAL_PENDING_APPROVAL',
                                             'Withdrawal Pending Approval',
                                             f'Your withdrawal of KES {amount:,.2f} is pending admin approval due to amount or frequency limits.')
            else:
                # Auto-approve small amounts for M-Pesa
                if payment_method_code == 'MPESA':
                    withdrawal.auto_approved = True
                    withdrawal.approved_by = None  # System approval
                    withdrawal.approved_at = timezone.now()
                    withdrawal.status = 'APPROVED'
                    withdrawal.save()

                    # Process immediately
                    result = process_mpesa_withdrawal(withdrawal)
                    if not result['success']:
                        withdrawal.status = 'FAILED'
                        withdrawal.save()
                        return JsonResponse({
                            'status': 'error',
                            'message': result.get('error', 'M-Pesa withdrawal failed')
                        })

                    # Update withdrawal with M-Pesa response
                    withdrawal.external_reference = result.get('conversation_id')
                    withdrawal.callback_data = result
                    withdrawal.status = 'PROCESSING'
                    withdrawal.save()
                else:
                    # Other payment methods require manual processing
                    withdrawal.status = 'PENDING'
                    withdrawal.approval_required = True
                    withdrawal.save()

            # Create notification
            withdrawal.create_notification('WITHDRAWAL_INITIATED',
                                         'Withdrawal Request Submitted',
                                         f'Your withdrawal request of KES {amount:,.2f} has been submitted and is being processed.')

            return JsonResponse({
                'status': 'success',
                'message': 'Withdrawal request submitted successfully',
                'withdrawal_id': str(withdrawal.id),
                'reference': withdrawal.reference,
                'status': withdrawal.get_status_display()
            })

        except (PaymentMethod.DoesNotExist, Wallet.DoesNotExist) as e:
            logger.error(f"Withdrawal request error: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid request parameters'
            })
        except ValueError as e:
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid amount format'
            })
        except Exception as e:
            logger.error(f"Unexpected error in withdrawal request: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'Withdrawal service temporarily unavailable'
            })

    # GET request - show withdrawal form
    payment_methods = PaymentMethod.objects.filter(is_active=True).order_by('priority')
    wallet, created = Wallet.objects.get_or_create(user=request.user)

    context = {
        'payment_methods': payment_methods,
        'wallet': wallet,
        'user': request.user
    }
    return render(request, 'payments/withdrawal.html', context)


@login_required
def wallet_dashboard(request):
    """Display user wallet dashboard with balance and recent transactions"""
    wallet, created = Wallet.objects.get_or_create(user=request.user)

    # Get recent transactions
    recent_transactions = Transaction.objects.filter(
        user=request.user
    ).select_related('payment_method').order_by('-created_at')[:10]

    # Get today's stats
    today = date.today()
    today_stats, created = UserPaymentStats.objects.get_or_create(
        user=request.user,
        date=today,
        defaults={'total_deposits': 0, 'total_withdrawals': 0, 'deposit_count': 0, 'withdrawal_count': 0}
    )

    # Get unread notifications
    unread_notifications = PaymentNotification.objects.filter(
        user=request.user,
        is_read=False
    ).order_by('-created_at')[:5]

    context = {
        'wallet': wallet,
        'recent_transactions': recent_transactions,
        'today_stats': today_stats,
        'unread_notifications': unread_notifications,
        'payment_methods': PaymentMethod.objects.filter(is_active=True).order_by('priority')
    }

    return render(request, 'payments/wallet_dashboard.html', context)


@login_required
def transaction_history(request):
    """Display user transaction history with filtering and pagination"""
    transactions = Transaction.objects.filter(
        user=request.user
    ).select_related('payment_method').order_by('-created_at')

    # Filter by transaction type if specified
    transaction_type = request.GET.get('type')
    if transaction_type and transaction_type in ['DEPOSIT', 'WITHDRAWAL', 'BET_PLACEMENT', 'BET_WINNING']:
        transactions = transactions.filter(transaction_type=transaction_type)

    # Filter by status if specified
    status = request.GET.get('status')
    if status and status in ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED']:
        transactions = transactions.filter(status=status)

    # Filter by date range if specified
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        transactions = transactions.filter(created_at__date__gte=date_from)
    if date_to:
        transactions = transactions.filter(created_at__date__lte=date_to)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(transactions, 20)  # Show 20 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'transaction_types': Transaction.TRANSACTION_TYPES,
        'status_choices': Transaction.STATUS_CHOICES,
        'current_filters': {
            'type': transaction_type,
            'status': status,
            'date_from': date_from,
            'date_to': date_to
        }
    }

    return render(request, 'payments/transaction_history.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_b2c_result(request):
    """Handle M-Pesa B2C withdrawal result callback"""
    try:
        callback_data = json.loads(request.body)
        logger.info(f"M-Pesa B2C Result: {callback_data}")

        result = callback_data.get('Result', {})
        result_code = result.get('ResultCode')
        result_desc = result.get('ResultDesc')
        originator_conversation_id = result.get('OriginatorConversationID')
        conversation_id = result.get('ConversationID')

        # Find the withdrawal record
        try:
            from .models import Withdrawal
            withdrawal = Withdrawal.objects.get(
                external_reference=originator_conversation_id
            )

            if result_code == 0:
                # Withdrawal successful
                withdrawal.status = 'COMPLETED'
                withdrawal.processed_at = timezone.now()

                # Extract transaction details
                result_parameters = result.get('ResultParameters', {}).get('ResultParameter', [])
                transaction_receipt = None
                for param in result_parameters:
                    name = param.get('Key')
                    value = param.get('Value')
                    if name == 'TransactionReceipt':
                        transaction_receipt = value
                    elif name == 'TransactionAmount':
                        # Verify the amount matches
                        if Decimal(str(value)) != withdrawal.net_amount:
                            logger.warning(f"Amount mismatch for withdrawal {withdrawal.reference}: expected {withdrawal.net_amount}, got {value}")

                # Update external reference with transaction receipt
                if transaction_receipt:
                    withdrawal.external_reference = transaction_receipt

                # Debit wallet balance
                withdrawal.wallet.debit(withdrawal.amount, f"Withdrawal {withdrawal.reference}")

                # Update callback data
                withdrawal.callback_data.update(callback_data)
                withdrawal.save()

                # Generate receipt
                withdrawal.generate_receipt()

                # Create success notification
                withdrawal.create_notification('WITHDRAWAL_SUCCESS',
                                             'Withdrawal Completed',
                                             f'Your withdrawal of KES {withdrawal.amount:,.2f} has been completed successfully. Transaction ID: {transaction_receipt}')

                logger.info(f"B2C withdrawal {withdrawal.reference} completed successfully")

            else:
                # Withdrawal failed
                withdrawal.status = 'FAILED'
                withdrawal.processed_at = timezone.now()

                # Update callback data
                withdrawal.callback_data.update(callback_data)
                withdrawal.save()

                # Create failure notification
                withdrawal.create_notification('WITHDRAWAL_FAILED',
                                             'Withdrawal Failed',
                                             f'Your withdrawal of KES {withdrawal.amount:,.2f} has failed. Reason: {result_desc}. Your wallet balance has not been affected.')

                logger.error(f"B2C withdrawal {withdrawal.reference} failed: {result_desc}")

        except Withdrawal.DoesNotExist:
            logger.error(f"Withdrawal not found for conversation ID: {originator_conversation_id}")

        return JsonResponse({'status': 'success'})

    except Exception as e:
        logger.error(f"Error processing B2C result: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_b2c_timeout(request):
    """Handle M-Pesa B2C withdrawal timeout callback"""
    try:
        callback_data = json.loads(request.body)
        logger.warning(f"M-Pesa B2C Timeout: {callback_data}")

        # Mark withdrawal as timed out
        originator_conversation_id = callback_data.get('OriginatorConversationID')

        try:
            from .models import Withdrawal
            withdrawal = Withdrawal.objects.get(
                external_reference=originator_conversation_id
            )
            withdrawal.status = 'TIMEOUT'
            withdrawal.processed_at = timezone.now()
            withdrawal.save()

            logger.warning(f"B2C withdrawal {withdrawal.reference} timed out")

        except Withdrawal.DoesNotExist:
            logger.error(f"Withdrawal not found for timeout conversation ID: {originator_conversation_id}")

        return JsonResponse({'status': 'success'})

    except Exception as e:
        logger.error(f"Error processing B2C timeout: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_reconciliation_result(request):
    """Handle M-Pesa reconciliation result callback"""
    try:
        callback_data = json.loads(request.body)
        logger.info(f"M-Pesa Reconciliation Result: {callback_data}")

        # Process reconciliation data
        # This would typically involve comparing M-Pesa records with local database
        # and identifying discrepancies

        return JsonResponse({'status': 'success'})

    except Exception as e:
        logger.error(f"Error processing reconciliation result: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_reconciliation_timeout(request):
    """Handle M-Pesa reconciliation timeout callback"""
    try:
        callback_data = json.loads(request.body)
        logger.warning(f"M-Pesa Reconciliation Timeout: {callback_data}")

        return JsonResponse({'status': 'success'})

    except Exception as e:
        logger.error(f"Error processing reconciliation timeout: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def airtel_callback(request):
    """Handle Airtel Money callback"""
    try:
        callback_data = json.loads(request.body)
        logger.info(f"Airtel Money Callback: {callback_data}")

        from .services.airtel_service import airtel_service
        from .models import AirtelTransaction

        # Process callback data
        result = airtel_service.process_callback(callback_data)

        if result['success']:
            transaction_id = result['transaction_id']
            status = result['status']

            try:
                airtel_tx = AirtelTransaction.objects.get(
                    airtel_transaction_id=transaction_id
                )

                # Update transaction status
                airtel_tx.status = status
                airtel_tx.status_description = callback_data.get('message', '')
                airtel_tx.save()

                # Update main transaction
                transaction = airtel_tx.transaction
                if status.lower() in ['success', 'completed']:
                    transaction.mark_completed()
                    logger.info(f"Airtel transaction {transaction_id} completed successfully")
                elif status.lower() in ['failed', 'cancelled']:
                    transaction.mark_failed("Airtel Money payment failed")
                    logger.error(f"Airtel transaction {transaction_id} failed")

            except AirtelTransaction.DoesNotExist:
                logger.error(f"Airtel transaction not found: {transaction_id}")

        return JsonResponse({'status': 'success'})

    except Exception as e:
        logger.error(f"Error processing Airtel callback: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def card_webhook(request):
    """Handle card payment webhook (Stripe)"""
    try:
        payload = request.body.decode('utf-8')
        signature = request.META.get('HTTP_STRIPE_SIGNATURE', '')

        from .services.card_service import card_service
        from .models import CardTransaction

        # Process webhook
        result = card_service.process_webhook(payload, signature)

        if result['success']:
            event_type = result['event_type']
            payment_intent_id = result['payment_intent_id']
            status = result['status']

            try:
                card_tx = CardTransaction.objects.get(
                    payment_intent_id=payment_intent_id
                )

                # Handle different event types
                if event_type == 'payment_intent.succeeded':
                    transaction = card_tx.transaction
                    transaction.mark_completed()
                    logger.info(f"Card payment {payment_intent_id} succeeded")

                elif event_type == 'payment_intent.payment_failed':
                    transaction = card_tx.transaction
                    transaction.mark_failed("Card payment failed")
                    logger.error(f"Card payment {payment_intent_id} failed")

            except CardTransaction.DoesNotExist:
                logger.error(f"Card transaction not found: {payment_intent_id}")

        return JsonResponse({'status': 'success'})

    except Exception as e:
        logger.error(f"Error processing card webhook: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def bank_callback(request):
    """Handle bank transfer callback"""
    try:
        callback_data = json.loads(request.body)
        logger.info(f"Bank Transfer Callback: {callback_data}")

        from .services.bank_service import bank_service
        from .models import BankTransaction

        # Process callback data
        result = bank_service.process_callback(callback_data)

        if result['success']:
            order_tracking_id = result['order_tracking_id']
            status = result['status']

            try:
                bank_tx = BankTransaction.objects.get(
                    order_tracking_id=order_tracking_id
                )

                # Update main transaction based on status
                transaction = bank_tx.transaction
                if status == 'COMPLETED':
                    transaction.mark_completed()
                    logger.info(f"Bank transfer {order_tracking_id} completed successfully")
                elif status == 'FAILED':
                    transaction.mark_failed("Bank transfer failed")
                    logger.error(f"Bank transfer {order_tracking_id} failed")

            except BankTransaction.DoesNotExist:
                logger.error(f"Bank transaction not found: {order_tracking_id}")

        return JsonResponse({'status': 'success'})

    except Exception as e:
        logger.error(f"Error processing bank callback: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)


@login_required
def download_deposit_receipt(request, deposit_id):
    """Download PDF receipt for a deposit"""
    try:
        from .models import Deposit
        from .services.receipt_service import receipt_service
        from django.http import HttpResponse

        deposit = get_object_or_404(Deposit, id=deposit_id, user=request.user)

        # Generate receipt if not already generated
        if not deposit.receipt_generated:
            deposit.generate_receipt()

        # Generate PDF
        pdf_buffer = receipt_service.generate_deposit_receipt(deposit)

        # Create HTTP response
        response = HttpResponse(pdf_buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="deposit_receipt_{deposit.reference}.pdf"'

        return response

    except Exception as e:
        logger.error(f"Error downloading deposit receipt: {str(e)}")
        return JsonResponse({'error': 'Failed to generate receipt'}, status=500)


@login_required
def download_withdrawal_receipt(request, withdrawal_id):
    """Download PDF receipt for a withdrawal"""
    try:
        from .models import Withdrawal
        from .services.receipt_service import receipt_service
        from django.http import HttpResponse

        withdrawal = get_object_or_404(Withdrawal, id=withdrawal_id, user=request.user)

        # Generate receipt if not already generated
        if not withdrawal.receipt_generated:
            withdrawal.generate_receipt()

        # Generate PDF
        pdf_buffer = receipt_service.generate_withdrawal_receipt(withdrawal)

        # Create HTTP response
        response = HttpResponse(pdf_buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="withdrawal_receipt_{withdrawal.reference}.pdf"'

        return response

    except Exception as e:
        logger.error(f"Error downloading withdrawal receipt: {str(e)}")
        return JsonResponse({'error': 'Failed to generate receipt'}, status=500)


@login_required
def notifications(request):
    """Display user notifications"""
    notifications = PaymentNotification.objects.filter(
        user=request.user
    ).order_by('-created_at')[:50]  # Show last 50 notifications

    # Mark notifications as read when viewed
    unread_notifications = notifications.filter(is_read=False)
    if unread_notifications.exists():
        unread_notifications.update(is_read=True)

    context = {
        'notifications': notifications,
        'unread_count': 0,  # All are now read
    }

    return render(request, 'payments/notifications.html', context)


@login_required
def mark_notification_read(request, notification_id):
    """Mark a specific notification as read"""
    try:
        notification = get_object_or_404(
            PaymentNotification,
            id=notification_id,
            user=request.user
        )
        notification.is_read = True
        notification.save()

        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


@login_required
def get_unread_notifications_count(request):
    """Get count of unread notifications for AJAX requests"""
    count = PaymentNotification.objects.filter(
        user=request.user,
        is_read=False
    ).count()

    return JsonResponse({'count': count})


@login_required
def update_low_balance_threshold(request):
    """Update user's low balance threshold"""
    if request.method == 'POST':
        try:
            threshold = request.POST.get('threshold')
            if threshold:
                wallet, created = Wallet.objects.get_or_create(user=request.user)
                wallet.low_balance_threshold = Decimal(str(threshold))
                wallet.low_balance_alert_sent = False  # Reset alert flag
                wallet.save()

                return JsonResponse({
                    'status': 'success',
                    'message': f'Low balance threshold updated to KES {threshold}'
                })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Error updating threshold: {str(e)}'
            })

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})


@login_required
def get_wallet_balance(request):
    """Get current wallet balance for real-time updates"""
    try:
        wallet = Wallet.objects.get(user=request.user)
        return JsonResponse({
            'status': 'success',
            'balance': float(wallet.balance),
            'formatted_balance': f"KES {wallet.balance:,.2f}",
            'last_updated': wallet.last_updated.isoformat() if wallet.last_updated else None
        })
    except Wallet.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Wallet not found'
        })


@login_required
def get_wallet_stats(request):
    """Get wallet statistics for real-time dashboard updates"""
    try:
        wallet = Wallet.objects.get(user=request.user)

        # Get today's stats
        today = date.today()
        user_stats, created = UserPaymentStats.objects.get_or_create(
            user=request.user,
            date=today,
            defaults={
                'total_deposits': 0,
                'deposit_count': 0,
                'total_withdrawals': 0,
                'withdrawal_count': 0
            }
        )

        # Get recent transactions count
        recent_transactions_count = Transaction.objects.filter(
            user=request.user,
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).count()

        return JsonResponse({
            'status': 'success',
            'balance': float(wallet.balance),
            'formatted_balance': f"KES {wallet.balance:,.2f}",
            'today_stats': {
                'deposit_count': user_stats.deposit_count,
                'total_deposits': float(user_stats.total_deposits),
                'formatted_deposits': f"KES {user_stats.total_deposits:,.2f}",
                'withdrawal_count': user_stats.withdrawal_count,
                'total_withdrawals': float(user_stats.total_withdrawals),
                'formatted_withdrawals': f"KES {user_stats.total_withdrawals:,.2f}",
            },
            'recent_transactions_count': recent_transactions_count,
            'last_updated': wallet.last_updated.isoformat() if wallet.last_updated else None
        })
    except Wallet.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Wallet not found'
        })


@login_required
def deposit_history(request):
    """Deposit history page with filtering and pagination"""
    deposits = Deposit.objects.filter(user=request.user).order_by('-created_at')

    # Apply filters
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    status = request.GET.get('status')
    payment_method = request.GET.get('payment_method')
    search = request.GET.get('search')

    if date_from:
        deposits = deposits.filter(created_at__date__gte=date_from)
    if date_to:
        deposits = deposits.filter(created_at__date__lte=date_to)
    if status:
        deposits = deposits.filter(status=status)
    if payment_method:
        deposits = deposits.filter(payment_method__code=payment_method)
    if search:
        deposits = deposits.filter(
            Q(id__icontains=search) |
            Q(external_reference__icontains=search)
        )

    # Pagination
    paginator = Paginator(deposits, 20)  # 20 deposits per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get payment methods for filter dropdown
    payment_methods = PaymentMethod.objects.filter(is_active=True)

    context = {
        'deposits': page_obj,
        'payment_methods': payment_methods,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'payments/deposit_history.html', context)


@login_required
def withdrawal_history(request):
    """Withdrawal history page with filtering and pagination"""
    withdrawals = Withdrawal.objects.filter(user=request.user).order_by('-created_at')

    # Apply filters
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    status = request.GET.get('status')
    payment_method = request.GET.get('payment_method')
    search = request.GET.get('search')

    if date_from:
        withdrawals = withdrawals.filter(created_at__date__gte=date_from)
    if date_to:
        withdrawals = withdrawals.filter(created_at__date__lte=date_to)
    if status:
        withdrawals = withdrawals.filter(status=status)
    if payment_method:
        withdrawals = withdrawals.filter(payment_method__code=payment_method)
    if search:
        withdrawals = withdrawals.filter(
            Q(id__icontains=search) |
            Q(external_reference__icontains=search)
        )

    # Pagination
    paginator = Paginator(withdrawals, 20)  # 20 withdrawals per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get payment methods for filter dropdown
    payment_methods = PaymentMethod.objects.filter(is_active=True)

    context = {
        'withdrawals': page_obj,
        'payment_methods': payment_methods,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'payments/withdrawal_history.html', context)


@login_required
def deposit_details(request, deposit_id):
    """Get deposit details for AJAX modal"""
    try:
        deposit = get_object_or_404(Deposit, id=deposit_id, user=request.user)

        return JsonResponse({
            'status': 'success',
            'deposit': {
                'id': str(deposit.id),
                'amount': str(deposit.amount),
                'status': deposit.get_status_display(),
                'payment_method': deposit.payment_method.name,
                'external_reference': deposit.external_reference,
                'phone_number': deposit.phone_number,
                'description': deposit.description,
                'created_at': deposit.created_at.strftime('%B %d, %Y at %I:%M %p'),
                'updated_at': deposit.updated_at.strftime('%B %d, %Y at %I:%M %p'),
            }
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })


@login_required
def withdrawal_details(request, withdrawal_id):
    """Get withdrawal details for AJAX modal"""
    try:
        withdrawal = get_object_or_404(Withdrawal, id=withdrawal_id, user=request.user)

        return JsonResponse({
            'status': 'success',
            'withdrawal': {
                'id': str(withdrawal.id),
                'amount': str(withdrawal.amount),
                'status': withdrawal.get_status_display(),
                'payment_method': withdrawal.payment_method.name,
                'external_reference': withdrawal.external_reference,
                'phone_number': withdrawal.phone_number,
                'admin_notes': getattr(withdrawal, 'admin_notes', ''),
                'rejection_reason': getattr(withdrawal, 'rejection_reason', ''),
                'created_at': withdrawal.created_at.strftime('%B %d, %Y at %I:%M %p'),
                'updated_at': withdrawal.updated_at.strftime('%B %d, %Y at %I:%M %p'),
            }
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })


@login_required
def get_payment_limits(request, method_code):
    """Get payment limits for a specific payment method"""
    try:
        payment_method = PaymentMethod.objects.get(code=method_code, is_active=True)

        try:
            min_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MIN_WITHDRAWAL')
            max_limit = PaymentLimit.objects.get(payment_method=payment_method, limit_type='MAX_WITHDRAWAL')

            return JsonResponse({
                'status': 'success',
                'limits': {
                    'min_withdrawal': str(min_limit.amount),
                    'max_withdrawal': str(max_limit.amount)
                }
            })
        except PaymentLimit.DoesNotExist:
            return JsonResponse({
                'status': 'success',
                'limits': {
                    'min_withdrawal': '1',
                    'max_withdrawal': '100000'
                }
            })

    except PaymentMethod.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Payment method not found'
        })


@login_required
def get_recent_withdrawals(request):
    """Get recent withdrawals for the user"""
    try:
        withdrawals = Withdrawal.objects.filter(user=request.user).order_by('-created_at')[:5]

        withdrawal_data = []
        for withdrawal in withdrawals:
            withdrawal_data.append({
                'id': str(withdrawal.id),
                'amount': str(withdrawal.amount),
                'status': withdrawal.get_status_display(),
                'created_at': withdrawal.created_at.strftime('%b %d, %Y at %I:%M %p'),
                'payment_method': withdrawal.payment_method.name
            })

        return JsonResponse({
            'status': 'success',
            'withdrawals': withdrawal_data
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })


# Admin approval views
from django.contrib.admin.views.decorators import staff_member_required

@staff_member_required
def admin_approve_withdrawal(request, withdrawal_id):
    """Admin view to approve a withdrawal"""
    withdrawal = get_object_or_404(Withdrawal, id=withdrawal_id)

    if request.method == 'POST':
        notes = request.POST.get('notes', '')
        try:
            withdrawal.approve(request.user, notes)
            messages.success(request, f'Withdrawal {withdrawal.reference} approved successfully.')
        except ValueError as e:
            messages.error(request, str(e))
        return redirect('admin:payments_withdrawal_changelist')

    context = {
        'withdrawal': withdrawal,
        'title': f'Approve Withdrawal {withdrawal.reference}',
    }
    return render(request, 'admin/payments/withdrawal_approve.html', context)

@staff_member_required
def admin_reject_withdrawal(request, withdrawal_id):
    """Admin view to reject a withdrawal"""
    withdrawal = get_object_or_404(Withdrawal, id=withdrawal_id)

    if request.method == 'POST':
        reason = request.POST.get('reason', '')
        notes = request.POST.get('notes', '')

        if not reason:
            messages.error(request, 'Rejection reason is required.')
            return render(request, 'admin/payments/withdrawal_reject.html', {
                'withdrawal': withdrawal,
                'title': f'Reject Withdrawal {withdrawal.reference}',
            })

        try:
            withdrawal.reject(request.user, reason, notes)
            messages.success(request, f'Withdrawal {withdrawal.reference} rejected successfully.')
        except ValueError as e:
            messages.error(request, str(e))
        return redirect('admin:payments_withdrawal_changelist')

    context = {
        'withdrawal': withdrawal,
        'title': f'Reject Withdrawal {withdrawal.reference}',
    }
    return render(request, 'admin/payments/withdrawal_reject.html', context)

@staff_member_required
def admin_withdrawal_dashboard(request):
    """Admin dashboard for withdrawal management"""
    from django.db.models import Count, Sum, Q
    from datetime import timedelta

    # Get statistics
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)

    stats = {
        'pending_count': Withdrawal.objects.filter(status='PENDING').count(),
        'pending_amount': Withdrawal.objects.filter(status='PENDING').aggregate(
            total=Sum('amount'))['total'] or 0,
        'today_count': Withdrawal.objects.filter(created_at__date=today).count(),
        'today_amount': Withdrawal.objects.filter(created_at__date=today).aggregate(
            total=Sum('amount'))['total'] or 0,
        'week_count': Withdrawal.objects.filter(created_at__date__gte=week_ago).count(),
        'week_amount': Withdrawal.objects.filter(created_at__date__gte=week_ago).aggregate(
            total=Sum('amount'))['total'] or 0,
    }

    # Get pending withdrawals requiring approval
    pending_withdrawals = Withdrawal.objects.filter(
        status__in=['PENDING', 'PROCESSING'],
        approval_required=True
    ).select_related('user', 'payment_method').order_by('-created_at')[:20]

    # Get recent activity
    recent_activity = Withdrawal.objects.filter(
        Q(approved_at__isnull=False) | Q(rejected_at__isnull=False)
    ).select_related('user', 'approved_by', 'rejected_by').order_by('-updated_at')[:10]

    context = {
        'stats': stats,
        'pending_withdrawals': pending_withdrawals,
        'recent_activity': recent_activity,
        'title': 'Withdrawal Management Dashboard',
    }
    return render(request, 'admin/payments/withdrawal_dashboard.html', context)


def mobile_mpesa_deposit(request):
    """Mobile-optimized M-Pesa deposit page"""
    return render(request, 'payments/mobile_mpesa_deposit.html')
