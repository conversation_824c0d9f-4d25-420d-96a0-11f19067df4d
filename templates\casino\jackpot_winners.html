{% extends 'base.html' %}
{% load static %}

{% block title %}Jackpot Winners - ZBet Casino{% endblock %}

{% block extra_css %}
<style>
.winners-container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.winners-header {
    background: #1e1e2e;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
    border: 1px solid #333;
}

.winners-title {
    font-size: 3rem;
    color: #ffd700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.winners-subtitle {
    color: #aaa;
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto;
}

.winners-grid {
    display: grid;
    gap: 20px;
}

.winner-card {
    background: #1e1e2e;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #333;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.winner-card:hover {
    border-color: #ffd700;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.1);
}

.winner-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.05), transparent);
    animation: shimmer 4s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.winner-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.winner-info {
    flex: 1;
}

.winner-name {
    color: #ffd700;
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.winner-date {
    color: #aaa;
    font-size: 0.9rem;
}

.winner-amount {
    text-align: right;
    flex-shrink: 0;
}

.amount-value {
    color: #00ff88;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.amount-currency {
    color: #aaa;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.winner-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    background: #2a2a3e;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #444;
}

.detail-label {
    color: #aaa;
    font-size: 0.8rem;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.detail-value {
    color: white;
    font-weight: bold;
}

.jackpot-badge {
    display: inline-block;
    background: linear-gradient(45deg, #ff9500, #ffb347);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-top: 10px;
}

.game-info {
    background: #2a2a3e;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #444;
    margin-top: 15px;
}

.game-name {
    color: #ffd700;
    font-weight: bold;
    margin-bottom: 5px;
}

.game-provider {
    color: #aaa;
    font-size: 0.9rem;
}

.no-winners {
    text-align: center;
    padding: 60px 20px;
    color: #aaa;
}

.no-winners-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #555;
}

.breadcrumb {
    background: rgba(30, 30, 46, 0.8);
    padding: 15px 0;
    margin-bottom: 20px;
}

.breadcrumb-nav {
    color: #aaa;
    font-size: 0.9rem;
}

.breadcrumb-nav a {
    color: #ffd700;
    text-decoration: none;
}

.stats-bar {
    background: #1e1e2e;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    color: #ffd700;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #aaa;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.winner-rank {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #ffd700;
    color: #1a1a2e;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.winner-rank.rank-1 {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
}

.winner-rank.rank-2 {
    background: linear-gradient(45deg, #c0c0c0, #e8e8e8);
}

.winner-rank.rank-3 {
    background: linear-gradient(45deg, #cd7f32, #daa520);
}

@media (max-width: 768px) {
    .winners-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .winner-header {
        flex-direction: column;
        text-align: center;
    }
    
    .winner-amount {
        text-align: center;
    }
    
    .winner-details {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="winners-container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <div class="container">
            <nav class="breadcrumb-nav">
                <a href="{% url 'casino:home' %}">Casino</a> 
                <span class="mx-2">›</span> 
                <span>Jackpot Winners</span>
            </nav>
        </div>
    </div>

    <div class="container">
        <!-- Winners Header -->
        <div class="winners-header">
            <h1 class="winners-title">
                🏆 Jackpot Winners 🏆
            </h1>
            <p class="winners-subtitle">
                Congratulations to our recent jackpot winners! Your big win could be next!
            </p>
        </div>

        <!-- Statistics Bar -->
        <div class="stats-bar">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">{{ recent_wins|length }}</div>
                    <div class="stat-label">Recent Winners</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">
                        {% widthratio recent_wins|length 1 100 %}{% if recent_wins|length > 0 %}+{% endif %}
                    </div>
                    <div class="stat-label">This Month</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">KES</div>
                    <div class="stat-label">Currency</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">24/7</div>
                    <div class="stat-label">Jackpot Active</div>
                </div>
            </div>
        </div>

        <!-- Winners Grid -->
        {% if recent_wins %}
        <div class="winners-grid">
            {% for win in recent_wins %}
            <div class="winner-card">
                <div class="winner-rank rank-{{ forloop.counter }}">{{ forloop.counter }}</div>
                
                <div class="winner-header">
                    <div class="winner-info">
                        <div class="winner-name">{{ win.winner.username|title }}</div>
                        <div class="winner-date">{{ win.won_at|date:"F j, Y \a\t g:i A" }}</div>
                    </div>
                    <div class="winner-amount">
                        <div class="amount-value">{{ win.amount|floatformat:2 }}</div>
                        <div class="amount-currency">KES</div>
                    </div>
                </div>

                <div class="winner-details">
                    <div class="detail-item">
                        <div class="detail-label">Jackpot</div>
                        <div class="detail-value">{{ win.jackpot.name }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Type</div>
                        <div class="detail-value">{{ win.jackpot.get_jackpot_type_display }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Bet Amount</div>
                        <div class="detail-value">KES {{ win.bet_amount|floatformat:2 }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Status</div>
                        <div class="detail-value">
                            {% if win.is_verified %}
                                {% if win.is_paid %}
                                    <span style="color: #00ff88;">✓ Paid</span>
                                {% else %}
                                    <span style="color: #ffd700;">⏳ Processing</span>
                                {% endif %}
                            {% else %}
                                <span style="color: #ff6b6b;">⚠ Pending</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                {% if win.game %}
                <div class="game-info">
                    <div class="game-name">🎰 {{ win.game.name }}</div>
                    <div class="game-provider">by {{ win.game.provider.name }}</div>
                </div>
                {% endif %}

                <div class="jackpot-badge">
                    {{ win.jackpot.get_jackpot_type_display }} Jackpot
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-winners">
            <div class="no-winners-icon">🏆</div>
            <h3>No Recent Winners</h3>
            <p>Be the first to win our amazing jackpots!</p>
            <a href="{% url 'casino:home' %}" class="btn-play btn-play-real" style="display: inline-block; margin-top: 20px; padding: 15px 30px; background: linear-gradient(45deg, #ffd700, #ffed4e); color: #1a1a2e; text-decoration: none; border-radius: 10px; font-weight: bold;">
                Play Jackpot Games
            </a>
        </div>
        {% endif %}

        <!-- Call to Action -->
        <div class="winners-header" style="margin-top: 40px;">
            <h2 style="color: #ffd700; font-size: 2rem; margin-bottom: 15px;">🎰 Ready to Win Big?</h2>
            <p class="winners-subtitle">
                Join thousands of players and try your luck at our progressive jackpots!
            </p>
            <div style="margin-top: 25px;">
                <a href="{% url 'casino:games_by_category' 'jackpots' %}" 
                   style="display: inline-block; padding: 15px 30px; background: linear-gradient(45deg, #ffd700, #ffed4e); color: #1a1a2e; text-decoration: none; border-radius: 10px; font-weight: bold; margin-right: 15px;">
                    🏆 Play Jackpot Games
                </a>
                <a href="{% url 'casino:home' %}" 
                   style="display: inline-block; padding: 15px 30px; background: transparent; color: #ffd700; text-decoration: none; border-radius: 10px; font-weight: bold; border: 2px solid #ffd700;">
                    🎮 Browse All Games
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
