{% extends 'base.html' %}

{% block title %}Setup Authenticator App - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Setup Authenticator App</h3>
                <p class="mb-0">Configure your TOTP authenticator</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                    <h5>Scan QR Code</h5>
                    <p class="text-muted">
                        Use your authenticator app to scan the QR code below.
                    </p>
                </div>
                
                <!-- Step-by-step instructions -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-list-ol"></i> Setup Instructions:</h6>
                    <ol class="mb-0">
                        <li>Download an authenticator app (Google Authenticator, Authy, etc.)</li>
                        <li>Open the app and tap "Add Account" or "+"</li>
                        <li>Scan the QR code below with your phone camera</li>
                        <li>Enter the 6-digit code from your app to verify</li>
                    </ol>
                </div>
                
                <!-- QR Code -->
                <div class="text-center mb-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <img src="{{ qr_code }}" alt="QR Code" class="img-fluid" style="max-width: 200px;">
                        </div>
                    </div>
                </div>
                
                <!-- Manual Entry -->
                <div class="alert alert-secondary">
                    <h6><i class="fas fa-keyboard"></i> Can't scan? Enter manually:</h6>
                    <p class="mb-1"><strong>Account:</strong> {{ user.email }}</p>
                    <p class="mb-1"><strong>Secret Key:</strong></p>
                    <div class="input-group">
                        <input type="text" class="form-control font-monospace" value="{{ secret }}" readonly id="secretKey">
                        <button class="btn btn-outline-secondary" type="button" onclick="copySecret()">
                            Copy
                        </button>
                    </div>
                </div>
                
                <!-- Verification Form -->
                <form method="post" id="totpSetupForm">
                    {% csrf_token %}
                    
                    <div class="form-floating mb-3">
                        {{ form.verification_code }}
                        <label for="{{ form.verification_code.id_for_label }}">Verification Code</label>
                        <div class="form-text">{{ form.verification_code.help_text }}</div>
                        {% if form.verification_code.errors %}
                            <div class="text-danger small">{{ form.verification_code.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Verify & Complete Setup
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:two_factor_settings' %}" class="btn btn-outline-secondary">
                        Skip for Now
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Recommended Apps -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-download"></i> Recommended Authenticator Apps</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <i class="fab fa-google fa-2x text-primary mb-2"></i>
                        <h6>Google Authenticator</h6>
                        <small class="text-muted">Free • iOS & Android</small>
                    </div>
                    <div class="col-md-4">
                        <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                        <h6>Authy</h6>
                        <small class="text-muted">Free • Multi-device sync</small>
                    </div>
                    <div class="col-md-4">
                        <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                        <h6>Microsoft Authenticator</h6>
                        <small class="text-muted">Free • Cloud backup</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on verification code input
    $('#{{ form.verification_code.id_for_label }}').focus();
    
    // Format verification code input
    $('#{{ form.verification_code.id_for_label }}').on('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
        if (this.value.length === 6) {
            $('#totpSetupForm').submit();
        }
    });
});

function copySecret() {
    const secretInput = document.getElementById('secretKey');
    secretInput.select();
    secretInput.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        alert('Secret key copied to clipboard!');
    } catch (err) {
        alert('Failed to copy secret key. Please copy manually.');
    }
}
</script>
{% endblock %}
