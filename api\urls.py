from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView

from . import views

app_name = 'api'

# Create router for viewsets (without API root)
class CustomRouter(DefaultRouter):
    include_root_view = False

router = CustomRouter()

urlpatterns = [
    # API Documentation
    path('schema/', SpectacularAPIView.as_view(), name='schema'),
    path('docs/', SpectacularSwaggerView.as_view(url_name='api:schema'), name='swagger-ui'),
    path('redoc/', SpectacularRedocView.as_view(url_name='api:schema'), name='redoc'),
    
    # Authentication endpoints
    path('auth/register/', views.UserRegistrationView.as_view(), name='register'),
    path('auth/login/', views.UserLoginView.as_view(), name='login'),
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/profile/', views.UserProfileView.as_view(), name='profile'),
    
    # Sports endpoints
    path('sports/', views.SportListView.as_view(), name='sports-list'),
    path('leagues/', views.LeagueListView.as_view(), name='leagues-list'),
    path('teams/', views.TeamListView.as_view(), name='teams-list'),
    path('matches/', views.MatchListView.as_view(), name='matches-list'),
    path('matches/<int:pk>/', views.MatchDetailView.as_view(), name='match-detail'),
    path('matches/<int:match_id>/selections/', views.SelectionListView.as_view(), name='match-selections'),
    
    # Betting endpoints
    path('bets/', views.UserBetsView.as_view(), name='user-bets'),
    path('bets/place/', views.PlaceBetView.as_view(), name='place-bet'),
    
    # Wallet & Payment endpoints
    path('wallet/balance/', views.WalletBalanceView.as_view(), name='wallet-balance'),
    path('wallet/transactions/', views.TransactionHistoryView.as_view(), name='transaction-history'),
    
    # Deposit endpoints
    path('deposits/', views.DepositHistoryView.as_view(), name='deposit-history'),
    path('deposits/create/', views.CreateDepositView.as_view(), name='create-deposit'),
    path('deposits/<int:deposit_id>/status/', views.DepositStatusView.as_view(), name='deposit-status'),
    
    # Withdrawal endpoints
    path('withdrawals/', views.WithdrawalHistoryView.as_view(), name='withdrawal-history'),
    path('withdrawals/create/', views.CreateWithdrawalView.as_view(), name='create-withdrawal'),
    
    # Promotion endpoints
    path('promotions/', views.PromotionListView.as_view(), name='promotions-list'),
    path('bonuses/', views.UserBonusesView.as_view(), name='user-bonuses'),
    path('loyalty/', views.UserLoyaltyStatusView.as_view(), name='loyalty-status'),
    
    # Casino endpoints
    path('games/', views.GameListView.as_view(), name='games-list'),
    path('game-sessions/', views.UserGameSessionsView.as_view(), name='game-sessions'),
    
    # Utility endpoints
    path('dashboard/', views.user_dashboard_data, name='dashboard'),
    path('config/', views.app_config, name='app-config'),
    
    # Custom API root
    path('', views.api_root, name='api-root'),

    # Include router URLs (without root)
    path('', include(router.urls)),
]
