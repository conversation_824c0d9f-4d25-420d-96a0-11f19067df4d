{% extends 'base.html' %}
{% load static %}

{% block title %}My Wallet - ZBet{% endblock %}

{% block extra_css %}
<style>
    .wallet-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .wallet-header {
        background: linear-gradient(135deg, #1a1a2e, #2d3748);
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .wallet-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(66, 153, 225, 0.1), rgba(72, 187, 120, 0.1));
        z-index: 1;
    }
    
    .wallet-content {
        position: relative;
        z-index: 2;
    }
    
    .balance-display {
        font-size: 48px;
        font-weight: bold;
        color: #4299e1;
        margin-bottom: 10px;
    }
    
    .balance-label {
        color: #a0aec0;
        font-size: 18px;
        margin-bottom: 20px;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .action-btn {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .action-btn:hover {
        background: linear-gradient(135deg, #3182ce, #2c5282);
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }
    
    .action-btn.secondary {
        background: linear-gradient(135deg, #48bb78, #38a169);
    }
    
    .action-btn.secondary:hover {
        background: linear-gradient(135deg, #38a169, #2f855a);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
    }
    
    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #4299e1;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #a0aec0;
        font-size: 14px;
    }
    
    .section-title {
        color: white;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .transactions-section {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
    }
    
    .transaction-item {
        display: flex;
        align-items: center;
        justify-content: between;
        padding: 15px 0;
        border-bottom: 1px solid #2d3748;
    }
    
    .transaction-item:last-child {
        border-bottom: none;
    }
    
    .transaction-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-weight: bold;
        color: white;
    }
    
    .transaction-icon.deposit {
        background: #48bb78;
    }
    
    .transaction-icon.withdrawal {
        background: #f56565;
    }
    
    .transaction-icon.bet {
        background: #ed8936;
    }
    
    .transaction-icon.winning {
        background: #38b2ac;
    }
    
    .transaction-details {
        flex: 1;
    }
    
    .transaction-title {
        color: white;
        font-weight: 600;
        margin-bottom: 2px;
    }
    
    .transaction-meta {
        color: #a0aec0;
        font-size: 12px;
    }
    
    .transaction-amount {
        font-weight: bold;
        font-size: 16px;
    }
    
    .transaction-amount.positive {
        color: #48bb78;
    }
    
    .transaction-amount.negative {
        color: #f56565;
    }
    
    .transaction-status {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        margin-left: 10px;
    }
    
    .status-completed {
        background: rgba(72, 187, 120, 0.2);
        color: #48bb78;
    }
    
    .status-pending {
        background: rgba(237, 137, 54, 0.2);
        color: #ed8936;
    }
    
    .status-failed {
        background: rgba(245, 101, 101, 0.2);
        color: #f56565;
    }
    
    .notifications-section {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 25px;
    }
    
    .notification-item {
        display: flex;
        align-items: flex-start;
        padding: 15px 0;
        border-bottom: 1px solid #2d3748;
    }
    
    .notification-item:last-child {
        border-bottom: none;
    }
    
    .notification-icon {
        width: 8px;
        height: 8px;
        background: #4299e1;
        border-radius: 50%;
        margin-top: 6px;
        margin-right: 15px;
        flex-shrink: 0;
    }
    
    .notification-content {
        flex: 1;
    }
    
    .notification-title {
        color: white;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .notification-message {
        color: #a0aec0;
        font-size: 14px;
        line-height: 1.4;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #a0aec0;
    }
    
    .empty-icon {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .balance-display {
            font-size: 36px;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .action-btn {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="wallet-container">
    <!-- Wallet Header -->
    <div class="wallet-header">
        <div class="wallet-content">
            <div class="balance-label">Current Balance</div>
            <div class="balance-display" id="wallet-balance">KES {{ wallet.balance|floatformat:2 }}</div>
            <div class="last-updated" id="last-updated" style="font-size: 12px; color: #a0aec0; margin-top: 5px;">
                Last updated: <span id="update-time">Just now</span>
            </div>
            <div class="action-buttons">
                <a href="{% url 'payments:deposit' %}" class="action-btn">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Deposit
                </a>
                <a href="{% url 'payments:withdrawal_request' %}" class="action-btn secondary">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                    </svg>
                    Withdraw
                </a>
                <a href="{% url 'payments:deposit_history' %}" class="action-btn" style="background: linear-gradient(135deg, #805ad5, #6b46c1);">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Deposits
                </a>
                <a href="{% url 'payments:withdrawal_history' %}" class="action-btn" style="background: linear-gradient(135deg, #f56565, #e53e3e);">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Withdrawals
                </a>
            </div>
        </div>
    </div>
    
    <!-- Today's Stats -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value" id="deposit-count">{{ today_stats.deposit_count }}</div>
            <div class="stat-label">Deposits Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="total-deposits">KES {{ today_stats.total_deposits|floatformat:2 }}</div>
            <div class="stat-label">Total Deposited Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="withdrawal-count">{{ today_stats.withdrawal_count }}</div>
            <div class="stat-label">Withdrawals Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="total-withdrawals">KES {{ today_stats.total_withdrawals|floatformat:2 }}</div>
            <div class="stat-label">Total Withdrawn Today</div>
        </div>
    </div>
    
    <!-- Recent Transactions -->
    <div class="transactions-section">
        <h2 class="section-title">
            <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
            Recent Transactions
        </h2>
        
        {% if recent_transactions %}
            {% for transaction in recent_transactions %}
            <div class="transaction-item">
                <div class="transaction-icon {{ transaction.transaction_type|lower }}">
                    {% if transaction.transaction_type == 'DEPOSIT' %}
                        ↓
                    {% elif transaction.transaction_type == 'WITHDRAWAL' %}
                        ↑
                    {% elif transaction.transaction_type == 'BET_PLACEMENT' %}
                        🎯
                    {% elif transaction.transaction_type == 'BET_WINNING' %}
                        🏆
                    {% else %}
                        💰
                    {% endif %}
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">{{ transaction.get_transaction_type_display }}</div>
                    <div class="transaction-meta">
                        {{ transaction.created_at|date:"M d, Y H:i" }} • {{ transaction.payment_method.name }}
                        {% if transaction.external_reference %}
                            • {{ transaction.external_reference }}
                        {% endif %}
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="transaction-amount {% if transaction.transaction_type == 'DEPOSIT' or transaction.transaction_type == 'BET_WINNING' %}positive{% else %}negative{% endif %}">
                        {% if transaction.transaction_type == 'DEPOSIT' or transaction.transaction_type == 'BET_WINNING' %}+{% else %}-{% endif %}KES {{ transaction.amount|floatformat:2 }}
                    </div>
                    <div class="transaction-status status-{{ transaction.status|lower }}">
                        {{ transaction.get_status_display }}
                    </div>
                </div>
            </div>
            {% endfor %}
            
            <div class="text-center mt-4">
                <a href="{% url 'payments:transaction_history' %}" class="text-blue-400 hover:text-blue-300 font-medium">
                    View All Transactions →
                </a>
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">💳</div>
                <div>No transactions yet</div>
                <div class="mt-2">
                    <a href="{% url 'payments:deposit' %}" class="text-blue-400 hover:text-blue-300">Make your first deposit</a>
                </div>
            </div>
        {% endif %}
    </div>
    
    <!-- Notifications -->
    {% if unread_notifications %}
    <div class="notifications-section">
        <h2 class="section-title">
            <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
            </svg>
            Recent Notifications
        </h2>
        
        {% for notification in unread_notifications %}
        <div class="notification-item">
            <div class="notification-icon"></div>
            <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.message }}</div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
class WalletUpdater {
    constructor() {
        this.updateInterval = 10000; // 10 seconds
        this.intervalId = null;
        this.lastBalance = null;
        this.isUpdating = false;

        this.init();
    }

    init() {
        // Start real-time updates
        this.startUpdates();

        // Update on page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopUpdates();
            } else {
                this.startUpdates();
            }
        });

        // Update when user returns to tab
        window.addEventListener('focus', () => {
            this.updateWalletStats();
        });
    }

    startUpdates() {
        if (this.intervalId) return;

        // Initial update
        this.updateWalletStats();

        // Set up periodic updates
        this.intervalId = setInterval(() => {
            this.updateWalletStats();
        }, this.updateInterval);
    }

    stopUpdates() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    async updateWalletStats() {
        if (this.isUpdating) return;

        this.isUpdating = true;

        try {
            const response = await fetch('{% url "payments:wallet_stats" %}');
            const data = await response.json();

            if (data.status === 'success') {
                this.updateUI(data);
                this.updateTimestamp();
            }
        } catch (error) {
            console.error('Error updating wallet stats:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    updateUI(data) {
        // Update balance with animation if changed
        const balanceElement = document.getElementById('wallet-balance');
        const currentBalance = parseFloat(data.balance);

        if (this.lastBalance !== null && this.lastBalance !== currentBalance) {
            // Animate balance change
            balanceElement.style.transform = 'scale(1.05)';
            balanceElement.style.color = currentBalance > this.lastBalance ? '#48bb78' : '#f56565';

            setTimeout(() => {
                balanceElement.style.transform = 'scale(1)';
                balanceElement.style.color = '#4299e1';
            }, 500);
        }

        balanceElement.textContent = data.formatted_balance;
        this.lastBalance = currentBalance;

        // Update stats
        document.getElementById('deposit-count').textContent = data.today_stats.deposit_count;
        document.getElementById('total-deposits').textContent = data.today_stats.formatted_deposits;
        document.getElementById('withdrawal-count').textContent = data.today_stats.withdrawal_count;
        document.getElementById('total-withdrawals').textContent = data.today_stats.formatted_withdrawals;
    }

    updateTimestamp() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('update-time').textContent = timeString;
    }

    // Manual refresh method
    refresh() {
        this.updateWalletStats();
    }
}

// Initialize wallet updater when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.walletUpdater = new WalletUpdater();

    // Add refresh button functionality if needed
    const refreshBtn = document.getElementById('refresh-wallet');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            window.walletUpdater.refresh();
        });
    }
});

// Add visual indicator for real-time updates
const style = document.createElement('style');
style.textContent = `
    .balance-display {
        transition: all 0.3s ease;
    }

    .updating {
        opacity: 0.7;
    }

    .balance-updated {
        animation: pulse 0.5s ease-in-out;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .last-updated {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
