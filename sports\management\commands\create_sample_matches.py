from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from sports.models import Sport, League, Team, Match
from betting.models import BetType, Market, Selection
from decimal import Decimal


class Command(BaseCommand):
    help = 'Create sample matches and betting data for API testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample sports data...')
        
        # Get or create Football sport
        football, created = Sport.objects.get_or_create(
            name='Football',
            defaults={
                'slug': 'football',
                'icon': 'futbol',
                'is_active': True
            }
        )
        
        # Get or create Premier League
        premier_league, created = League.objects.get_or_create(
            name='Premier League',
            sport=football,
            defaults={
                'slug': 'premier-league',
                'country': 'England',
                'is_active': True
            }
        )
        
        # Create teams
        teams_data = [
            {'name': 'Manchester United', 'slug': 'manchester-united'},
            {'name': 'Liverpool', 'slug': 'liverpool'},
            {'name': 'Chelsea', 'slug': 'chelsea'},
            {'name': 'Arsenal', 'slug': 'arsenal'},
            {'name': 'Manchester City', 'slug': 'manchester-city'},
            {'name': 'Tottenham', 'slug': 'tottenham'},
        ]
        
        teams = []
        for team_data in teams_data:
            team, created = Team.objects.get_or_create(
                name=team_data['name'],
                sport=football,
                defaults={
                    'slug': team_data['slug'],
                    'country': 'England',
                    'is_active': True
                }
            )
            teams.append(team)
        
        # Create matches
        now = timezone.now()
        matches_data = [
            {
                'home_team': teams[0],  # Manchester United
                'away_team': teams[1],  # Liverpool
                'start_time': now + timedelta(days=1),
                'status': 'scheduled'
            },
            {
                'home_team': teams[2],  # Chelsea
                'away_team': teams[3],  # Arsenal
                'start_time': now + timedelta(days=2),
                'status': 'scheduled'
            },
            {
                'home_team': teams[4],  # Manchester City
                'away_team': teams[5],  # Tottenham
                'start_time': now + timedelta(days=3),
                'status': 'scheduled'
            },
            {
                'home_team': teams[1],  # Liverpool
                'away_team': teams[2],  # Chelsea
                'start_time': now - timedelta(hours=2),
                'status': 'live',
                'home_score': 1,
                'away_score': 0,
                'minute': 65
            },
            {
                'home_team': teams[3],  # Arsenal
                'away_team': teams[0],  # Manchester United
                'start_time': now - timedelta(days=1),
                'status': 'finished',
                'home_score': 2,
                'away_score': 1
            }
        ]
        
        created_matches = []
        for match_data in matches_data:
            match, created = Match.objects.get_or_create(
                home_team=match_data['home_team'],
                away_team=match_data['away_team'],
                league=premier_league,
                start_time=match_data['start_time'],
                defaults={
                    'status': match_data['status'],
                    'home_score': match_data.get('home_score'),
                    'away_score': match_data.get('away_score'),
                    'minute': match_data.get('minute'),
                    'venue': f"{match_data['home_team'].name} Stadium",
                    'betting_enabled': True,
                    'live_betting_enabled': match_data['status'] == 'live'
                }
            )
            created_matches.append(match)
            if created:
                self.stdout.write(f'Created match: {match}')
        
        # Create bet types and markets
        bet_types_data = [
            {'name': '1X2', 'slug': '1x2', 'description': 'Match Result'},
            {'name': 'Over/Under 2.5', 'slug': 'over-under-2-5', 'description': 'Total Goals Over/Under 2.5'},
            {'name': 'Both Teams to Score', 'slug': 'both-teams-score', 'description': 'Both Teams to Score'},
        ]
        
        bet_types = []
        for bt_data in bet_types_data:
            bet_type, created = BetType.objects.get_or_create(
                name=bt_data['name'],
                defaults={
                    'slug': bt_data['slug'],
                    'description': bt_data['description'],
                    'is_active': True
                }
            )
            bet_types.append(bet_type)
        
        # Create markets and selections for each match
        for match in created_matches:
            if match.status in ['scheduled', 'live']:
                # 1X2 Market
                market_1x2, created = Market.objects.get_or_create(
                    match=match,
                    bet_type=bet_types[0],  # 1X2
                    defaults={
                        'name': f'{match.home_team.name} vs {match.away_team.name} - Match Result',
                        'status': 'active' if match.status == 'scheduled' else 'live'
                    }
                )
                
                if created or not market_1x2.selections.exists():
                    # Create selections for 1X2
                    selections_1x2 = [
                        {'name': f'{match.home_team.name} Win', 'decimal_odds': Decimal('2.10')},
                        {'name': 'Draw', 'decimal_odds': Decimal('3.20')},
                        {'name': f'{match.away_team.name} Win', 'decimal_odds': Decimal('3.50')},
                    ]
                    
                    for sel_data in selections_1x2:
                        Selection.objects.get_or_create(
                            market=market_1x2,
                            name=sel_data['name'],
                            defaults={
                                'decimal_odds': sel_data['decimal_odds'],
                                'status': 'active'
                            }
                        )
                
                # Over/Under Market
                market_ou, created = Market.objects.get_or_create(
                    match=match,
                    bet_type=bet_types[1],  # Over/Under
                    defaults={
                        'name': f'{match.home_team.name} vs {match.away_team.name} - Over/Under 2.5 Goals',
                        'status': 'active' if match.status == 'scheduled' else 'live'
                    }
                )
                
                if created or not market_ou.selections.exists():
                    # Create selections for Over/Under
                    selections_ou = [
                        {'name': 'Over 2.5', 'decimal_odds': Decimal('1.85')},
                        {'name': 'Under 2.5', 'decimal_odds': Decimal('1.95')},
                    ]
                    
                    for sel_data in selections_ou:
                        Selection.objects.get_or_create(
                            market=market_ou,
                            name=sel_data['name'],
                            defaults={
                                'decimal_odds': sel_data['decimal_odds'],
                                'status': 'active'
                            }
                        )
        
        # Create Basketball data
        basketball, created = Sport.objects.get_or_create(
            name='Basketball',
            defaults={
                'slug': 'basketball',
                'icon': 'basketball-ball',
                'is_active': True
            }
        )
        
        nba, created = League.objects.get_or_create(
            name='NBA',
            sport=basketball,
            defaults={
                'slug': 'nba',
                'country': 'USA',
                'is_active': True
            }
        )
        
        # Create NBA teams
        nba_teams_data = [
            {'name': 'Los Angeles Lakers', 'slug': 'los-angeles-lakers'},
            {'name': 'Boston Celtics', 'slug': 'boston-celtics'},
            {'name': 'Golden State Warriors', 'slug': 'golden-state-warriors'},
            {'name': 'Miami Heat', 'slug': 'miami-heat'},
        ]
        
        nba_teams = []
        for team_data in nba_teams_data:
            team, created = Team.objects.get_or_create(
                name=team_data['name'],
                sport=basketball,
                defaults={
                    'slug': team_data['slug'],
                    'country': 'USA',
                    'is_active': True
                }
            )
            nba_teams.append(team)
        
        # Create NBA match
        nba_match, created = Match.objects.get_or_create(
            home_team=nba_teams[0],  # Lakers
            away_team=nba_teams[1],  # Celtics
            league=nba,
            start_time=now + timedelta(days=1, hours=2),
            defaults={
                'status': 'scheduled',
                'venue': 'Crypto.com Arena',
                'betting_enabled': True
            }
        )
        
        if created:
            self.stdout.write(f'Created NBA match: {nba_match}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample data:\n'
                f'- {Sport.objects.count()} sports\n'
                f'- {League.objects.count()} leagues\n'
                f'- {Team.objects.count()} teams\n'
                f'- {Match.objects.count()} matches\n'
                f'- {Market.objects.count()} markets\n'
                f'- {Selection.objects.count()} selections'
            )
        )
