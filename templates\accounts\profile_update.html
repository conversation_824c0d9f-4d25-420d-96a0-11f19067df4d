{% extends 'base.html' %}

{% block title %}Update Profile - ZBet{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card">
                <div class="card-header zbet-primary text-white">
                    <h6 class="mb-0">Account Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'accounts:profile_update' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-user"></i> Profile
                    </a>
                    <a href="{% url 'accounts:change_email' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-at"></i> Change Email
                    </a>
                    <a href="{% url 'accounts:change_phone' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-mobile-alt"></i> Change Phone
                    </a>
                    <a href="{% url 'accounts:two_factor_settings' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> Two-Factor Authentication
                    </a>
                    <a href="{% url 'accounts:social_connections' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-link"></i> Social Connections
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-history"></i> Betting History
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-wallet"></i> Wallet
                    </a>
                    <a href="{% url 'accounts:notification_preferences' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Update Profile</h2>
                    <p class="text-muted">Manage your personal information and preferences</p>
                </div>
                <div>
                    <span class="badge bg-{% if completion_score >= 80 %}success{% elif completion_score >= 50 %}warning{% else %}danger{% endif %} fs-6">
                        {{ completion_score }}% Complete
                    </span>
                </div>
            </div>

            <!-- Profile Picture Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-camera"></i> Profile Picture</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            {% if profile.avatar %}
                                <img src="{{ profile.avatar.url }}" alt="Profile" class="rounded-circle" width="80" height="80">
                            {% else %}
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="fas fa-user text-white fa-2x"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col">
                            <h6>Profile Picture</h6>
                            <p class="text-muted mb-0">Upload a profile picture to personalize your account</p>
                        </div>
                        <div class="col-auto">
                            <a href="{% url 'accounts:profile_picture_upload' %}" class="btn btn-outline-primary">
                                <i class="fas fa-upload"></i> Upload Picture
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Forms -->
            <form method="post" id="profileUpdateForm">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user"></i> Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ user_form.first_name }}
                                    <label for="{{ user_form.first_name.id_for_label }}">First Name</label>
                                    {% if user_form.first_name.errors %}
                                        <div class="text-danger small">{{ user_form.first_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ user_form.last_name }}
                                    <label for="{{ user_form.last_name.id_for_label }}">Last Name</label>
                                    {% if user_form.last_name.errors %}
                                        <div class="text-danger small">{{ user_form.last_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ user_form.date_of_birth }}
                                    <label for="{{ user_form.date_of_birth.id_for_label }}">Date of Birth</label>
                                    {% if user_form.date_of_birth.errors %}
                                        <div class="text-danger small">{{ user_form.date_of_birth.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ profile_form.gender }}
                                    <label for="{{ profile_form.gender.id_for_label }}">Gender</label>
                                    {% if profile_form.gender.errors %}
                                        <div class="text-danger small">{{ profile_form.gender.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ user_form.country }}
                                    <label for="{{ user_form.country.id_for_label }}">Country</label>
                                    {% if user_form.country.errors %}
                                        <div class="text-danger small">{{ user_form.country.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ user_form.city }}
                                    <label for="{{ user_form.city.id_for_label }}">City</label>
                                    {% if user_form.city.errors %}
                                        <div class="text-danger small">{{ user_form.city.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ profile_form.occupation }}
                            <label for="{{ profile_form.occupation.id_for_label }}">Occupation</label>
                            {% if profile_form.occupation.errors %}
                                <div class="text-danger small">{{ profile_form.occupation.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Address Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-floating mb-3">
                            {{ profile_form.address_line_1 }}
                            <label for="{{ profile_form.address_line_1.id_for_label }}">Address Line 1</label>
                            {% if profile_form.address_line_1.errors %}
                                <div class="text-danger small">{{ profile_form.address_line_1.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ profile_form.address_line_2 }}
                            <label for="{{ profile_form.address_line_2.id_for_label }}">Address Line 2 (Optional)</label>
                            {% if profile_form.address_line_2.errors %}
                                <div class="text-danger small">{{ profile_form.address_line_2.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ profile_form.postal_code }}
                            <label for="{{ profile_form.postal_code.id_for_label }}">Postal Code</label>
                            {% if profile_form.postal_code.errors %}
                                <div class="text-danger small">{{ profile_form.postal_code.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-phone"></i> Emergency Contact</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ profile_form.emergency_contact_name }}
                                    <label for="{{ profile_form.emergency_contact_name.id_for_label }}">Emergency Contact Name</label>
                                    {% if profile_form.emergency_contact_name.errors %}
                                        <div class="text-danger small">{{ profile_form.emergency_contact_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ profile_form.emergency_contact_phone }}
                                    <label for="{{ profile_form.emergency_contact_phone.id_for_label }}">Emergency Contact Phone</label>
                                    {% if profile_form.emergency_contact_phone.errors %}
                                        <div class="text-danger small">{{ profile_form.emergency_contact_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Betting Preferences -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-futbol"></i> Betting Preferences</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ profile_form.betting_experience }}
                                    <label for="{{ profile_form.betting_experience.id_for_label }}">Betting Experience</label>
                                    {% if profile_form.betting_experience.errors %}
                                        <div class="text-danger small">{{ profile_form.betting_experience.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Favorite Sports</label>
                                    {{ profile_form.favorite_sports }}
                                    {% if profile_form.favorite_sports.errors %}
                                        <div class="text-danger small">{{ profile_form.favorite_sports.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            {{ profile_form.bio }}
                            <label for="{{ profile_form.bio.id_for_label }}">Bio</label>
                            {% if profile_form.bio.errors %}
                                <div class="text-danger small">{{ profile_form.bio.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                    <button type="submit" class="btn btn-zbet btn-lg">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form validation
    $('#profileUpdateForm').on('submit', function(e) {
        const firstName = $('#{{ user_form.first_name.id_for_label }}').val().trim();
        const lastName = $('#{{ user_form.last_name.id_for_label }}').val().trim();
        
        if (!firstName) {
            e.preventDefault();
            alert('Please enter your first name.');
            $('#{{ user_form.first_name.id_for_label }}').focus();
            return false;
        }
        
        if (!lastName) {
            e.preventDefault();
            alert('Please enter your last name.');
            $('#{{ user_form.last_name.id_for_label }}').focus();
            return false;
        }
    });
});
</script>
{% endblock %}
