from django.contrib import admin
from django.utils import timezone
from .models import (
    Sport, Country, League, Team, Match, MatchEvent,
    MatchStatistics, PlayerStatistics, TeamSeasonStatistics
)


@admin.register(Sport)
class SportAdmin(admin.ModelAdmin):
    """
    Sport admin interface
    """
    list_display = [
        'name', 'is_active', 'is_featured', 'display_order',
        'active_leagues_count', 'upcoming_matches_count'
    ]
    list_filter = ['is_active', 'is_featured']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'icon')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'is_featured', 'display_order')
        }),
        ('Betting Settings', {
            'fields': ('min_bet_amount', 'max_bet_amount')
        }),
    )


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    """
    Country admin interface
    """
    list_display = ['name', 'code', 'flag_emoji', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(League)
class LeagueAdmin(admin.ModelAdmin):
    """
    League admin interface
    """
    list_display = [
        'name', 'sport', 'country', 'season', 'league_type',
        'is_active', 'is_featured', 'active_teams_count'
    ]
    list_filter = ['sport', 'country', 'league_type', 'is_active', 'is_featured']
    search_fields = ['name', 'short_name', 'season']
    prepopulated_fields = {'slug': ('name',)}

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'short_name', 'description')
        }),
        ('Classification', {
            'fields': ('sport', 'country', 'league_type', 'season')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'is_featured', 'display_order')
        }),
        ('External Data', {
            'fields': ('external_id',)
        }),
    )


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    """
    Team admin interface
    """
    list_display = [
        'name', 'sport', 'country', 'city', 'is_active'
    ]
    list_filter = ['sport', 'country', 'is_active']
    search_fields = ['name', 'short_name', 'city']
    prepopulated_fields = {'slug': ('name',)}
    filter_horizontal = ['leagues']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'short_name')
        }),
        ('Classification', {
            'fields': ('sport', 'country', 'leagues')
        }),
        ('Details', {
            'fields': ('founded_year', 'stadium', 'city')
        }),
        ('Media', {
            'fields': ('logo',)
        }),
        ('Settings', {
            'fields': ('is_active',)
        }),
        ('External Data', {
            'fields': ('external_id',)
        }),
    )


class MatchEventInline(admin.TabularInline):
    """
    Inline admin for match events
    """
    model = MatchEvent
    extra = 0
    fields = ['event_type', 'team', 'minute', 'period', 'player_name', 'description']
    readonly_fields = ['created_at']


class MatchStatisticsInline(admin.StackedInline):
    """
    Inline admin for match statistics
    """
    model = MatchStatistics
    extra = 0
    fieldsets = (
        ('General Statistics', {
            'fields': ('total_goals', 'total_corners', 'total_cards', 'total_fouls', 'total_offsides')
        }),
        ('Home Team Statistics', {
            'fields': (
                'home_possession', 'home_shots', 'home_shots_on_target', 'home_corners',
                'home_fouls', 'home_yellow_cards', 'home_red_cards', 'home_offsides',
                'home_passes', 'home_pass_accuracy'
            )
        }),
        ('Away Team Statistics', {
            'fields': (
                'away_possession', 'away_shots', 'away_shots_on_target', 'away_corners',
                'away_fouls', 'away_yellow_cards', 'away_red_cards', 'away_offsides',
                'away_passes', 'away_pass_accuracy'
            )
        }),
        ('Advanced Statistics', {
            'fields': ('advanced_stats',),
            'classes': ('collapse',)
        }),
    )


class PlayerStatisticsInline(admin.TabularInline):
    """
    Inline admin for player statistics
    """
    model = PlayerStatistics
    extra = 0
    fields = [
        'player_name', 'jersey_number', 'team', 'minutes_played',
        'goals', 'assists', 'shots', 'yellow_cards', 'red_cards', 'rating'
    ]


@admin.register(Match)
class MatchAdmin(admin.ModelAdmin):
    """
    Match admin interface
    """
    list_display = [
        'home_team', 'away_team', 'league', 'start_time',
        'status', 'result_display', 'betting_enabled'
    ]
    list_filter = [
        'status', 'league__sport', 'league', 'betting_enabled',
        'live_betting_enabled', 'start_time'
    ]
    search_fields = [
        'home_team__name', 'away_team__name', 'league__name', 'venue'
    ]
    date_hierarchy = 'start_time'
    inlines = [MatchEventInline, MatchStatisticsInline, PlayerStatisticsInline]

    fieldsets = (
        ('Match Details', {
            'fields': ('home_team', 'away_team', 'league', 'start_time', 'venue')
        }),
        ('Status & Score', {
            'fields': ('status', 'home_score', 'away_score', 'round_number')
        }),
        ('Live Data', {
            'fields': ('minute', 'period', 'score_details', 'statistics')
        }),
        ('Betting Settings', {
            'fields': ('betting_enabled', 'live_betting_enabled')
        }),
        ('External Data', {
            'fields': ('external_id',)
        }),
    )

    actions = ['enable_betting', 'disable_betting', 'mark_as_finished']

    def enable_betting(self, request, queryset):
        """Enable betting for selected matches"""
        count = queryset.update(betting_enabled=True)
        self.message_user(request, f'{count} matches enabled for betting.')
    enable_betting.short_description = 'Enable betting for selected matches'

    def disable_betting(self, request, queryset):
        """Disable betting for selected matches"""
        count = queryset.update(betting_enabled=False)
        self.message_user(request, f'{count} matches disabled for betting.')
    disable_betting.short_description = 'Disable betting for selected matches'

    def mark_as_finished(self, request, queryset):
        """Mark selected matches as finished"""
        count = queryset.filter(status__in=['live', 'halftime']).update(status='finished')
        self.message_user(request, f'{count} matches marked as finished.')
    mark_as_finished.short_description = 'Mark selected matches as finished'


@admin.register(MatchEvent)
class MatchEventAdmin(admin.ModelAdmin):
    """
    Match Event admin interface
    """
    list_display = [
        'match', 'team', 'event_type', 'minute', 'period',
        'player_name', 'created_at'
    ]
    list_filter = ['event_type', 'period', 'match__league__sport']
    search_fields = [
        'match__home_team__name', 'match__away_team__name',
        'team__name', 'player_name', 'description'
    ]
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Event Details', {
            'fields': ('match', 'team', 'event_type', 'minute', 'period')
        }),
        ('Player Information', {
            'fields': ('player_name', 'player_2_name')
        }),
        ('Description', {
            'fields': ('description', 'metadata')
        }),
    )





@admin.register(MatchStatistics)
class MatchStatisticsAdmin(admin.ModelAdmin):
    """
    Match Statistics admin interface
    """
    list_display = [
        'match', 'total_goals', 'total_shots', 'total_corners', 'total_cards'
    ]
    list_filter = ['match__league__sport', 'match__status']
    search_fields = ['match__home_team__name', 'match__away_team__name']

    fieldsets = (
        ('Match', {
            'fields': ('match',)
        }),
        ('General Statistics', {
            'fields': ('total_goals', 'total_corners', 'total_cards', 'total_fouls', 'total_offsides')
        }),
        ('Home Team Statistics', {
            'fields': (
                'home_possession', 'home_shots', 'home_shots_on_target', 'home_corners',
                'home_fouls', 'home_yellow_cards', 'home_red_cards', 'home_offsides',
                'home_passes', 'home_pass_accuracy'
            )
        }),
        ('Away Team Statistics', {
            'fields': (
                'away_possession', 'away_shots', 'away_shots_on_target', 'away_corners',
                'away_fouls', 'away_yellow_cards', 'away_red_cards', 'away_offsides',
                'away_passes', 'away_pass_accuracy'
            )
        }),
        ('Advanced Statistics', {
            'fields': ('advanced_stats',)
        }),
    )

    actions = ['update_from_events']

    def update_from_events(self, request, queryset):
        """Update statistics from match events"""
        count = 0
        for stats in queryset:
            stats.update_from_events()
            count += 1

        self.message_user(request, f'{count} statistics updated from events.')
    update_from_events.short_description = 'Update statistics from match events'


@admin.register(PlayerStatistics)
class PlayerStatisticsAdmin(admin.ModelAdmin):
    """
    Player Statistics admin interface
    """
    list_display = [
        'player_name', 'team', 'match', 'minutes_played', 'goals', 'assists', 'rating'
    ]
    list_filter = ['team', 'match__league', 'is_starter']
    search_fields = ['player_name', 'team__name', 'match__home_team__name', 'match__away_team__name']

    fieldsets = (
        ('Player Details', {
            'fields': ('match', 'team', 'player_name', 'jersey_number', 'position')
        }),
        ('Playing Time', {
            'fields': ('minutes_played', 'is_starter', 'substituted_in', 'substituted_out')
        }),
        ('Performance Statistics', {
            'fields': (
                'goals', 'assists', 'shots', 'shots_on_target', 'passes', 'pass_accuracy',
                'tackles', 'interceptions', 'fouls_committed', 'fouls_suffered'
            )
        }),
        ('Disciplinary', {
            'fields': ('yellow_cards', 'red_cards')
        }),
        ('Rating & Advanced Stats', {
            'fields': ('rating', 'advanced_stats')
        }),
    )


@admin.register(TeamSeasonStatistics)
class TeamSeasonStatisticsAdmin(admin.ModelAdmin):
    """
    Team Season Statistics admin interface
    """
    list_display = [
        'team', 'league', 'season', 'position', 'points', 'matches_played',
        'wins', 'draws', 'losses', 'goal_difference', 'win_percentage'
    ]
    list_filter = ['league', 'season']
    search_fields = ['team__name', 'league__name']
    ordering = ['league', '-points', '-goals_for']

    fieldsets = (
        ('Team & Season', {
            'fields': ('team', 'league', 'season')
        }),
        ('Match Statistics', {
            'fields': ('matches_played', 'wins', 'draws', 'losses', 'points', 'position')
        }),
        ('Goal Statistics', {
            'fields': ('goals_for', 'goals_against')
        }),
        ('Performance Metrics', {
            'fields': ('form', 'clean_sheets', 'failed_to_score', 'average_possession')
        }),
    )

    readonly_fields = ['goal_difference', 'win_percentage']
