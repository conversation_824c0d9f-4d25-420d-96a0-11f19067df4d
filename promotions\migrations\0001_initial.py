# Generated by Django 5.2.4 on 2025-07-08 10:06

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BonusType",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("WELCOME", "Welcome Bonus"),
                            ("DEPOSIT", "Deposit Bonus"),
                            ("CASHBACK", "Cashback"),
                            ("REFERRAL", "Referral Bonus"),
                            ("LOYALTY", "Loyalty Bonus"),
                            ("SEASONAL", "Seasonal Promotion"),
                            ("VIP", "VIP Bonus"),
                            ("FREE_BET", "Free Bet"),
                            ("NO_DEPOSIT", "No Deposit Bonus"),
                            ("RELOAD", "Reload Bonus"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "promotions_bonus_types",
                "ordering": ["category", "name"],
            },
        ),
        migrations.CreateModel(
            name="LoyaltyProgram",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "tier",
                    models.CharField(
                        choices=[
                            ("BRONZE", "Bronze"),
                            ("SILVER", "Silver"),
                            ("GOLD", "Gold"),
                            ("PLATINUM", "Platinum"),
                            ("DIAMOND", "Diamond"),
                            ("VIP", "VIP"),
                        ],
                        max_length=20,
                    ),
                ),
                ("min_points_required", models.PositiveIntegerField(default=0)),
                (
                    "min_deposit_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "min_wagering_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "points_multiplier",
                    models.DecimalField(decimal_places=2, default=1.0, max_digits=5),
                ),
                (
                    "cashback_percentage",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "bonus_percentage",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("free_withdrawals_per_month", models.PositiveIntegerField(default=0)),
                ("dedicated_support", models.BooleanField(default=False)),
                ("exclusive_promotions", models.BooleanField(default=False)),
                (
                    "birthday_bonus",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("priority", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "promotions_loyalty_programs",
                "ordering": ["priority", "min_points_required"],
            },
        ),
        migrations.CreateModel(
            name="LoyaltyPoints",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "points_type",
                    models.CharField(
                        choices=[
                            ("EARNED", "Points Earned"),
                            ("REDEEMED", "Points Redeemed"),
                            ("EXPIRED", "Points Expired"),
                            ("BONUS", "Bonus Points"),
                            ("ADJUSTMENT", "Manual Adjustment"),
                        ],
                        max_length=20,
                    ),
                ),
                ("points", models.IntegerField()),
                ("balance_after", models.IntegerField(default=0)),
                ("source_description", models.CharField(max_length=255)),
                (
                    "source_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="loyalty_points",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "promotions_loyalty_points",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Promotion",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("slug", models.SlugField(max_length=200, unique=True)),
                ("description", models.TextField()),
                ("short_description", models.CharField(blank=True, max_length=255)),
                (
                    "promotion_type",
                    models.CharField(
                        choices=[
                            ("PERCENTAGE", "Percentage Bonus"),
                            ("FIXED_AMOUNT", "Fixed Amount"),
                            ("FREE_BETS", "Free Bets"),
                            ("CASHBACK", "Cashback"),
                            ("LOYALTY_POINTS", "Loyalty Points"),
                            ("COMBO", "Combination Bonus"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "percentage_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Percentage bonus (0-100%)",
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "fixed_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Fixed bonus amount in KES",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "max_bonus_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum bonus amount for percentage bonuses",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "min_deposit_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Minimum deposit required to qualify",
                        max_digits=10,
                    ),
                ),
                (
                    "target_audience",
                    models.CharField(
                        choices=[
                            ("ALL", "All Users"),
                            ("NEW", "New Users"),
                            ("EXISTING", "Existing Users"),
                            ("VIP", "VIP Users"),
                            ("INACTIVE", "Inactive Users"),
                            ("HIGH_ROLLERS", "High Rollers"),
                            ("SPECIFIC", "Specific Users"),
                        ],
                        default="ALL",
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                (
                    "is_unlimited",
                    models.BooleanField(default=False, help_text="No usage limit"),
                ),
                (
                    "max_uses_total",
                    models.PositiveIntegerField(
                        blank=True, help_text="Total usage limit", null=True
                    ),
                ),
                (
                    "max_uses_per_user",
                    models.PositiveIntegerField(
                        default=1, help_text="Per user usage limit"
                    ),
                ),
                (
                    "wagering_requirement",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.00"),
                        help_text="Wagering multiplier (e.g., 35.00 = 35x)",
                        max_digits=5,
                    ),
                ),
                (
                    "wagering_contribution_sports",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("100.00"),
                        help_text="Sports betting contribution percentage",
                        max_digits=5,
                    ),
                ),
                (
                    "wagering_contribution_casino",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("100.00"),
                        help_text="Casino games contribution percentage",
                        max_digits=5,
                    ),
                ),
                (
                    "min_odds",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Minimum odds for sports bets",
                        max_digits=5,
                        null=True,
                    ),
                ),
                (
                    "excluded_games",
                    models.JSONField(
                        blank=True, default=list, help_text="Excluded casino games"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("ACTIVE", "Active"),
                            ("PAUSED", "Paused"),
                            ("EXPIRED", "Expired"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="DRAFT",
                        max_length=20,
                    ),
                ),
                ("is_featured", models.BooleanField(default=False)),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=0, help_text="Higher numbers appear first"
                    ),
                ),
                (
                    "banner_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="promotions/banners/"
                    ),
                ),
                (
                    "thumbnail_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="promotions/thumbnails/"
                    ),
                ),
                ("terms_and_conditions", models.TextField(blank=True)),
                ("total_uses", models.PositiveIntegerField(default=0)),
                (
                    "total_bonus_awarded",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "bonus_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promotions",
                        to="promotions.bonustype",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_promotions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "specific_users",
                    models.ManyToManyField(
                        blank=True,
                        related_name="targeted_promotions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "promotions_promotions",
                "ordering": ["-priority", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PromoCode",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("code", models.CharField(max_length=50, unique=True)),
                (
                    "code_type",
                    models.CharField(
                        choices=[
                            ("SINGLE_USE", "Single Use"),
                            ("MULTI_USE", "Multi Use"),
                            ("USER_SPECIFIC", "User Specific"),
                            ("UNLIMITED", "Unlimited"),
                        ],
                        default="MULTI_USE",
                        max_length=20,
                    ),
                ),
                ("max_uses", models.PositiveIntegerField(blank=True, null=True)),
                ("max_uses_per_user", models.PositiveIntegerField(default=1)),
                ("valid_from", models.DateTimeField()),
                ("valid_until", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("INACTIVE", "Inactive"),
                            ("EXPIRED", "Expired"),
                            ("EXHAUSTED", "Exhausted"),
                        ],
                        default="ACTIVE",
                        max_length=20,
                    ),
                ),
                ("total_uses", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_promo_codes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "specific_users",
                    models.ManyToManyField(
                        blank=True,
                        related_name="assigned_promo_codes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "promotion",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promo_codes",
                        to="promotions.promotion",
                    ),
                ),
            ],
            options={
                "db_table": "promotions_promo_codes",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Bonus",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("PROMOTION", "Promotion"),
                            ("WELCOME", "Welcome Bonus"),
                            ("DEPOSIT", "Deposit Bonus"),
                            ("CASHBACK", "Cashback"),
                            ("REFERRAL", "Referral"),
                            ("LOYALTY", "Loyalty Program"),
                            ("MANUAL", "Manual Award"),
                            ("COMPENSATION", "Compensation"),
                        ],
                        max_length=20,
                    ),
                ),
                ("reference", models.CharField(max_length=50, unique=True)),
                ("bonus_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "original_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Deposit/bet amount that triggered bonus",
                        max_digits=10,
                    ),
                ),
                (
                    "remaining_amount",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "wagering_requirement",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "wagered_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "remaining_wagering",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("ACTIVE", "Active"),
                            ("WAGERING", "Wagering in Progress"),
                            ("COMPLETED", "Completed"),
                            ("EXPIRED", "Expired"),
                            ("CANCELLED", "Cancelled"),
                            ("FORFEITED", "Forfeited"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("awarded_at", models.DateTimeField(auto_now_add=True)),
                ("activated_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("terms_accepted", models.BooleanField(default=False)),
                ("notes", models.TextField(blank=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bonuses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "bonus_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bonuses",
                        to="promotions.bonustype",
                    ),
                ),
                (
                    "promotion",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bonus_claims",
                        to="promotions.promotion",
                    ),
                ),
            ],
            options={
                "db_table": "promotions_bonuses",
                "ordering": ["-awarded_at"],
            },
        ),
        migrations.CreateModel(
            name="PromotionalBanner",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                (
                    "position",
                    models.CharField(
                        choices=[
                            ("HEADER", "Header Banner"),
                            ("SIDEBAR", "Sidebar Banner"),
                            ("FOOTER", "Footer Banner"),
                            ("POPUP", "Popup Banner"),
                            ("HOMEPAGE_HERO", "Homepage Hero"),
                            ("CASINO_PAGE", "Casino Page"),
                            ("SPORTS_PAGE", "Sports Page"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("subtitle", models.CharField(blank=True, max_length=255)),
                ("description", models.TextField(blank=True)),
                ("banner_image", models.ImageField(upload_to="banners/")),
                (
                    "mobile_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="banners/mobile/"
                    ),
                ),
                (
                    "call_to_action_text",
                    models.CharField(default="Learn More", max_length=50),
                ),
                ("link_url", models.URLField(blank=True)),
                ("priority", models.PositiveIntegerField(default=0)),
                ("show_from", models.DateTimeField()),
                ("show_until", models.DateTimeField()),
                (
                    "target_audience",
                    models.CharField(
                        choices=[
                            ("ALL", "All Users"),
                            ("NEW", "New Users"),
                            ("EXISTING", "Existing Users"),
                            ("VIP", "VIP Users"),
                            ("INACTIVE", "Inactive Users"),
                            ("HIGH_ROLLERS", "High Rollers"),
                            ("SPECIFIC", "Specific Users"),
                        ],
                        default="ALL",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("INACTIVE", "Inactive"),
                            ("SCHEDULED", "Scheduled"),
                            ("EXPIRED", "Expired"),
                        ],
                        default="ACTIVE",
                        max_length=20,
                    ),
                ),
                ("impressions", models.PositiveIntegerField(default=0)),
                ("clicks", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "promotion",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="banners",
                        to="promotions.promotion",
                    ),
                ),
            ],
            options={
                "db_table": "promotions_banners",
                "ordering": ["-priority", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PromotionalCampaign",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                (
                    "campaign_type",
                    models.CharField(
                        choices=[
                            ("EMAIL", "Email Campaign"),
                            ("SMS", "SMS Campaign"),
                            ("PUSH", "Push Notification"),
                            ("BANNER", "Website Banner"),
                            ("POPUP", "Website Popup"),
                            ("SOCIAL", "Social Media"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "target_audience",
                    models.CharField(
                        choices=[
                            ("ALL", "All Users"),
                            ("NEW", "New Users"),
                            ("EXISTING", "Existing Users"),
                            ("VIP", "VIP Users"),
                            ("INACTIVE", "Inactive Users"),
                            ("HIGH_ROLLERS", "High Rollers"),
                            ("SPECIFIC", "Specific Users"),
                        ],
                        default="ALL",
                        max_length=20,
                    ),
                ),
                ("subject", models.CharField(blank=True, max_length=255)),
                ("message", models.TextField()),
                (
                    "banner_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="campaigns/banners/"
                    ),
                ),
                ("call_to_action", models.CharField(blank=True, max_length=100)),
                ("scheduled_at", models.DateTimeField(blank=True, null=True)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("SCHEDULED", "Scheduled"),
                            ("ACTIVE", "Active"),
                            ("PAUSED", "Paused"),
                            ("COMPLETED", "Completed"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="DRAFT",
                        max_length=20,
                    ),
                ),
                ("total_sent", models.PositiveIntegerField(default=0)),
                ("total_opened", models.PositiveIntegerField(default=0)),
                ("total_clicked", models.PositiveIntegerField(default=0)),
                ("total_conversions", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_campaigns",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "promotion",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="campaigns",
                        to="promotions.promotion",
                    ),
                ),
                (
                    "specific_users",
                    models.ManyToManyField(
                        blank=True,
                        related_name="targeted_campaigns",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "promotions_campaigns",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PromotionAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField()),
                ("total_claims", models.PositiveIntegerField(default=0)),
                ("unique_users", models.PositiveIntegerField(default=0)),
                (
                    "total_bonus_awarded",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_deposits",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_wagering",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("completed_wagering", models.PositiveIntegerField(default=0)),
                (
                    "conversion_rate",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "average_bonus_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "promotion",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="promotions.promotion",
                    ),
                ),
            ],
            options={
                "db_table": "promotions_analytics",
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="ReferralBonus",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("referral_code", models.CharField(max_length=20, unique=True)),
                (
                    "referrer_bonus",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "referred_bonus",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "min_deposit_required",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "min_wagering_required",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("qualification_period_days", models.PositiveIntegerField(default=30)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("QUALIFIED", "Qualified"),
                            ("AWARDED", "Awarded"),
                            ("EXPIRED", "Expired"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                (
                    "referred_user_deposit",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "referred_user_wagering",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("qualified_at", models.DateTimeField(blank=True, null=True)),
                ("awarded_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "referred_bonus_instance",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="referred_bonus_source",
                        to="promotions.bonus",
                    ),
                ),
                (
                    "referred_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="referral_source",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "referrer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="referrals_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "referrer_bonus_instance",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="referrer_bonus_source",
                        to="promotions.bonus",
                    ),
                ),
            ],
            options={
                "db_table": "promotions_referral_bonuses",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserLoyaltyStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_points", models.PositiveIntegerField(default=0)),
                ("available_points", models.PositiveIntegerField(default=0)),
                (
                    "lifetime_deposits",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "lifetime_wagering",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("points_to_next_tier", models.PositiveIntegerField(default=0)),
                ("free_withdrawals_used", models.PositiveIntegerField(default=0)),
                ("last_withdrawal_reset", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "current_program",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="promotions.loyaltyprogram",
                    ),
                ),
                (
                    "next_tier_program",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="next_tier_users",
                        to="promotions.loyaltyprogram",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="loyalty_status",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "promotions_user_loyalty_status",
            },
        ),
        migrations.CreateModel(
            name="Cashback",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "cashback_type",
                    models.CharField(
                        choices=[
                            ("DAILY", "Daily Cashback"),
                            ("WEEKLY", "Weekly Cashback"),
                            ("MONTHLY", "Monthly Cashback"),
                            ("LOSS_BASED", "Loss-based Cashback"),
                            ("GAME_SPECIFIC", "Game-specific Cashback"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "percentage",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("period_start", models.DateTimeField()),
                ("period_end", models.DateTimeField()),
                (
                    "total_losses",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "cashback_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "min_loss_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "max_cashback_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("CALCULATED", "Calculated"),
                            ("AWARDED", "Awarded"),
                            ("EXPIRED", "Expired"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("calculated_at", models.DateTimeField(blank=True, null=True)),
                ("awarded_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bonus",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="cashback_source",
                        to="promotions.bonus",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cashbacks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "promotions_cashbacks",
                "ordering": ["-created_at"],
                "unique_together": {
                    ("user", "cashback_type", "period_start", "period_end")
                },
            },
        ),
        migrations.CreateModel(
            name="PromoCodeUse",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("used_at", models.DateTimeField(auto_now_add=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "bonus",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="promotions.bonus",
                    ),
                ),
                (
                    "promo_code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promo_code_uses",
                        to="promotions.promocode",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promo_code_uses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "promotions_promo_code_uses",
                "ordering": ["-used_at"],
                "unique_together": {("promo_code", "user")},
            },
        ),
        migrations.AddIndex(
            model_name="promotion",
            index=models.Index(
                fields=["status", "start_date", "end_date"],
                name="promotions__status_589bf5_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="promotion",
            index=models.Index(
                fields=["target_audience", "status"],
                name="promotions__target__6cceb2_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="promotion",
            index=models.Index(
                fields=["is_featured", "status"], name="promotions__is_feat_352412_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bonus",
            index=models.Index(
                fields=["user", "status"], name="promotions__user_id_b9e3f6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bonus",
            index=models.Index(
                fields=["status", "expires_at"], name="promotions__status_69449f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bonus",
            index=models.Index(
                fields=["reference"], name="promotions__referen_3be5be_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="promotionanalytics",
            unique_together={("promotion", "date")},
        ),
        migrations.AlterUniqueTogether(
            name="referralbonus",
            unique_together={("referrer", "referred_user")},
        ),
    ]
