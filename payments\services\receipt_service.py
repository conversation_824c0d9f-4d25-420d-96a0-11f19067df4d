"""
Receipt Generation Service for ZBet Platform
Generates PDF receipts for deposits and withdrawals
"""

import os
import logging
from decimal import Decimal
from datetime import datetime
from io import BytesIO

from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.lib import colors

from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)


class ReceiptService:
    """Service for generating payment receipts"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom styles for the receipt"""
        # Header style
        self.header_style = ParagraphStyle(
            'CustomHeader',
            parent=self.styles['Heading1'],
            fontSize=24,
            textColor=HexColor('#1a365d'),
            alignment=TA_CENTER,
            spaceAfter=20,
            fontName='Helvetica-Bold'
        )
        
        # Subheader style
        self.subheader_style = ParagraphStyle(
            'CustomSubHeader',
            parent=self.styles['Heading2'],
            fontSize=16,
            textColor=HexColor('#2d3748'),
            alignment=TA_CENTER,
            spaceAfter=15,
            fontName='Helvetica-Bold'
        )
        
        # Normal text style
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=black,
            alignment=TA_LEFT,
            spaceAfter=8,
            fontName='Helvetica'
        )
        
        # Bold text style
        self.bold_style = ParagraphStyle(
            'CustomBold',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=black,
            alignment=TA_LEFT,
            spaceAfter=8,
            fontName='Helvetica-Bold'
        )
    
    def generate_deposit_receipt(self, deposit):
        """Generate PDF receipt for a deposit transaction"""
        try:
            buffer = BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build the receipt content
            story = []
            
            # Header
            story.append(Paragraph("ZBet Platform", self.header_style))
            story.append(Paragraph("Deposit Receipt", self.subheader_style))
            story.append(Spacer(1, 20))
            
            # Receipt details table
            receipt_data = [
                ['Receipt Number:', str(deposit.reference)],
                ['Date & Time:', deposit.created_at.strftime('%Y-%m-%d %H:%M:%S')],
                ['Customer:', f"{deposit.user.first_name} {deposit.user.last_name}"],
                ['Email:', deposit.user.email],
                ['Phone:', getattr(deposit, 'phone_number', 'N/A')],
                ['', ''],
                ['Transaction Details:', ''],
                ['Payment Method:', deposit.payment_method.name],
                ['Amount Deposited:', f"KES {deposit.amount:,.2f}"],
                ['Transaction Fee:', f"KES {deposit.fee_amount:,.2f}"],
                ['Net Amount Credited:', f"KES {deposit.net_amount:,.2f}"],
                ['Status:', deposit.get_status_display()],
                ['External Reference:', deposit.external_reference or 'N/A'],
            ]
            
            # Create table
            table = Table(receipt_data, colWidths=[2.5*inch, 3.5*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                # Highlight transaction details section
                ('BACKGROUND', (0, 6), (-1, 6), HexColor('#f7fafc')),
                ('FONTNAME', (0, 6), (-1, 6), 'Helvetica-Bold'),
                # Highlight net amount
                ('BACKGROUND', (0, 10), (-1, 10), HexColor('#e6fffa')),
                ('FONTNAME', (0, 10), (-1, 10), 'Helvetica-Bold'),
            ]))
            
            story.append(table)
            story.append(Spacer(1, 30))
            
            # Footer information
            footer_text = """
            <para align="center">
            <b>Important Information:</b><br/>
            • This receipt serves as proof of your deposit transaction<br/>
            • Keep this receipt for your records<br/>
            • For any queries, contact our support team<br/>
            • Transaction processed securely through ZBet Platform<br/><br/>
            
            <b>Contact Information:</b><br/>
            Email: <EMAIL> | Phone: +254 700 000 000<br/>
            Website: www.zbet.com<br/><br/>
            
            <i>Generated on {}</i>
            </para>
            """.format(timezone.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            story.append(Paragraph(footer_text, self.normal_style))
            
            # Build PDF
            doc.build(story)
            buffer.seek(0)
            
            logger.info(f"Generated deposit receipt for deposit {deposit.reference}")
            return buffer
            
        except Exception as e:
            logger.error(f"Error generating deposit receipt: {str(e)}")
            raise
    
    def generate_withdrawal_receipt(self, withdrawal):
        """Generate PDF receipt for a withdrawal transaction"""
        try:
            buffer = BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build the receipt content
            story = []
            
            # Header
            story.append(Paragraph("ZBet Platform", self.header_style))
            story.append(Paragraph("Withdrawal Receipt", self.subheader_style))
            story.append(Spacer(1, 20))
            
            # Receipt details table
            receipt_data = [
                ['Receipt Number:', str(withdrawal.reference)],
                ['Date & Time:', withdrawal.created_at.strftime('%Y-%m-%d %H:%M:%S')],
                ['Customer:', f"{withdrawal.user.first_name} {withdrawal.user.last_name}"],
                ['Email:', withdrawal.user.email],
                ['Phone:', getattr(withdrawal, 'phone_number', 'N/A')],
                ['', ''],
                ['Transaction Details:', ''],
                ['Payment Method:', withdrawal.payment_method.name],
                ['Withdrawal Amount:', f"KES {withdrawal.amount:,.2f}"],
                ['Transaction Fee:', f"KES {withdrawal.fee_amount:,.2f}"],
                ['Net Amount Sent:', f"KES {withdrawal.net_amount:,.2f}"],
                ['Status:', withdrawal.get_status_display()],
                ['External Reference:', withdrawal.external_reference or 'N/A'],
                ['Approval Required:', 'Yes' if withdrawal.approval_required else 'No'],
            ]
            
            if withdrawal.approved_by:
                receipt_data.append(['Approved By:', withdrawal.approved_by.username])
                receipt_data.append(['Approved At:', withdrawal.approved_at.strftime('%Y-%m-%d %H:%M:%S')])
            
            # Create table
            table = Table(receipt_data, colWidths=[2.5*inch, 3.5*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                # Highlight transaction details section
                ('BACKGROUND', (0, 6), (-1, 6), HexColor('#f7fafc')),
                ('FONTNAME', (0, 6), (-1, 6), 'Helvetica-Bold'),
                # Highlight net amount
                ('BACKGROUND', (0, 10), (-1, 10), HexColor('#fff5f5')),
                ('FONTNAME', (0, 10), (-1, 10), 'Helvetica-Bold'),
            ]))
            
            story.append(table)
            story.append(Spacer(1, 30))
            
            # Footer information
            footer_text = """
            <para align="center">
            <b>Important Information:</b><br/>
            • This receipt serves as proof of your withdrawal transaction<br/>
            • Keep this receipt for your records<br/>
            • Processing time may vary depending on payment method<br/>
            • For any queries, contact our support team<br/><br/>
            
            <b>Contact Information:</b><br/>
            Email: <EMAIL> | Phone: +254 700 000 000<br/>
            Website: www.zbet.com<br/><br/>
            
            <i>Generated on {}</i>
            </para>
            """.format(timezone.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            story.append(Paragraph(footer_text, self.normal_style))
            
            # Build PDF
            doc.build(story)
            buffer.seek(0)
            
            logger.info(f"Generated withdrawal receipt for withdrawal {withdrawal.reference}")
            return buffer
            
        except Exception as e:
            logger.error(f"Error generating withdrawal receipt: {str(e)}")
            raise


# Singleton instance
receipt_service = ReceiptService()
