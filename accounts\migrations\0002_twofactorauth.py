# Generated by Django 4.2.7 on 2025-07-04 15:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TwoFactorAuth",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_enabled",
                    models.BooleanField(
                        default=False, help_text="Whether 2FA is enabled for this user"
                    ),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[
                            ("sms", "SMS"),
                            ("totp", "Authenticator App (TOTP)"),
                            ("both", "Both SMS and Authenticator"),
                        ],
                        default="sms",
                        help_text="Preferred 2FA method",
                        max_length=10,
                    ),
                ),
                (
                    "totp_secret",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Base32 encoded secret for TOTP",
                        max_length=32,
                    ),
                ),
                (
                    "totp_verified",
                    models.<PERSON><PERSON><PERSON><PERSON><PERSON>(
                        default=False, help_text="Whether TOTP has been verified"
                    ),
                ),
                (
                    "sms_enabled",
                    models.<PERSON>oleanField(
                        default=True, help_text="Whether SMS 2FA is enabled"
                    ),
                ),
                (
                    "backup_codes",
                    models.JSONField(
                        default=list,
                        help_text="List of backup codes for account recovery",
                    ),
                ),
                (
                    "backup_codes_used",
                    models.JSONField(
                        default=list, help_text="List of used backup codes"
                    ),
                ),
                ("enabled_at", models.DateTimeField(blank=True, null=True)),
                ("last_used_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="two_factor_auth",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Two-Factor Authentication",
                "verbose_name_plural": "Two-Factor Authentication",
                "db_table": "accounts_two_factor_auth",
            },
        ),
    ]
