from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    GameProvider, GameCategory, Game, GameSession, GameResult,
    ProgressiveJackpot, JackpotWin, GameStatistics
)


@admin.register(GameProvider)
class GameProviderAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'status', 'active_games_count', 'is_featured', 'priority', 'created_at']
    list_filter = ['status', 'is_featured', 'created_at']
    search_fields = ['name', 'code']
    readonly_fields = ['id', 'created_at', 'updated_at', 'active_games_count']
    ordering = ['-priority', 'name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'logo', 'website')
        }),
        ('API Configuration', {
            'fields': ('api_endpoint', 'api_key', 'api_secret'),
            'classes': ('collapse',)
        }),
        ('Status & Display', {
            'fields': ('status', 'is_featured', 'priority')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(GameCategory)
class GameCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'icon', 'is_active', 'priority', 'games_count']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'slug']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['id', 'created_at']
    ordering = ['-priority', 'name']

    def games_count(self, obj):
        return obj.games.count()
    games_count.short_description = 'Games Count'


@admin.register(Game)
class GameAdmin(admin.ModelAdmin):
    list_display = ['name', 'provider', 'category', 'game_type', 'status', 'is_featured', 'is_popular', 'play_count', 'rtp_percentage']
    list_filter = ['game_type', 'status', 'provider', 'category', 'is_featured', 'is_popular', 'is_new', 'volatility']
    search_fields = ['name', 'slug', 'provider__name']
    readonly_fields = ['id', 'slug', 'play_count', 'total_bets', 'total_wins', 'actual_rtp', 'created_at', 'updated_at']
    filter_horizontal = []
    ordering = ['-is_featured', '-play_count']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'provider', 'category', 'game_type', 'description')
        }),
        ('Game Assets', {
            'fields': ('thumbnail', 'banner_image', 'background_image')
        }),
        ('Game Configuration', {
            'fields': ('min_bet', 'max_bet', 'rtp_percentage', 'volatility')
        }),
        ('Features', {
            'fields': ('has_free_spins', 'has_bonus_rounds', 'has_progressive_jackpot', 'has_demo_mode')
        }),
        ('Status & Visibility', {
            'fields': ('is_active', 'is_featured', 'is_new', 'is_popular', 'status')
        }),
        ('External Integration', {
            'fields': ('external_game_id', 'game_url', 'demo_url'),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': ('play_count', 'total_bets', 'total_wins', 'actual_rtp'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('tags', 'metadata', 'id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def actual_rtp(self, obj):
        return f"{obj.actual_rtp:.2f}%"
    actual_rtp.short_description = 'Actual RTP'


@admin.register(GameSession)
class GameSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'game', 'status', 'is_demo', 'total_bet_amount', 'total_win_amount', 'net_result', 'rounds_played', 'started_at']
    list_filter = ['status', 'is_demo', 'game__provider', 'started_at']
    search_fields = ['user__username', 'user__email', 'game__name', 'session_token']
    readonly_fields = ['id', 'session_token', 'duration', 'session_rtp', 'started_at', 'ended_at', 'last_activity']
    ordering = ['-started_at']

    fieldsets = (
        ('Session Information', {
            'fields': ('user', 'game', 'session_token', 'status', 'is_demo')
        }),
        ('Financial Summary', {
            'fields': ('initial_balance', 'final_balance', 'total_bet_amount', 'total_win_amount', 'net_result')
        }),
        ('Session Statistics', {
            'fields': ('rounds_played', 'max_win', 'max_bet', 'session_rtp', 'duration')
        }),
        ('Technical Details', {
            'fields': ('ip_address', 'user_agent', 'device_type'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('started_at', 'ended_at', 'last_activity'),
            'classes': ('collapse',)
        })
    )

    def duration(self, obj):
        return obj.duration
    duration.short_description = 'Duration'

    def session_rtp(self, obj):
        return f"{obj.session_rtp:.2f}%"
    session_rtp.short_description = 'Session RTP'


@admin.register(GameResult)
class GameResultAdmin(admin.ModelAdmin):
    list_display = ['user', 'game', 'result_type', 'bet_amount', 'win_amount', 'net_result', 'multiplier', 'played_at']
    list_filter = ['result_type', 'game__provider', 'triggered_bonus', 'is_verified', 'played_at']
    search_fields = ['user__username', 'game__name', 'round_id', 'external_round_id']
    readonly_fields = ['id', 'round_id', 'played_at']
    ordering = ['-played_at']

    fieldsets = (
        ('Round Information', {
            'fields': ('session', 'user', 'game', 'round_id', 'external_round_id', 'result_type')
        }),
        ('Financial Details', {
            'fields': ('bet_amount', 'win_amount', 'net_result', 'multiplier')
        }),
        ('Bonus Features', {
            'fields': ('triggered_bonus', 'free_spins_awarded')
        }),
        ('Verification', {
            'fields': ('is_verified', 'verification_data'),
            'classes': ('collapse',)
        }),
        ('Game Data', {
            'fields': ('game_data',),
            'classes': ('collapse',)
        })
    )


@admin.register(ProgressiveJackpot)
class ProgressiveJackpotAdmin(admin.ModelAdmin):
    list_display = ['name', 'jackpot_type', 'current_amount_display', 'status', 'total_winners', 'last_winner', 'last_won_at']
    list_filter = ['jackpot_type', 'status', 'is_featured', 'created_at']
    search_fields = ['name']
    readonly_fields = ['id', 'current_amount', 'total_winners', 'total_paid_out', 'last_winner', 'last_winning_amount', 'last_won_at', 'created_at', 'updated_at']
    filter_horizontal = ['games']
    ordering = ['-current_amount']
    actions = ['reset_jackpots', 'activate_jackpots', 'deactivate_jackpots']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'jackpot_type', 'games')
        }),
        ('Jackpot Configuration', {
            'fields': ('seed_amount', 'max_amount', 'contribution_percentage', 'trigger_probability', 'min_bet_to_qualify')
        }),
        ('Current Status', {
            'fields': ('current_amount', 'status', 'is_featured')
        }),
        ('Winner Information', {
            'fields': ('last_winner', 'last_winning_amount', 'last_won_at', 'total_winners', 'total_paid_out'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def current_amount_display(self, obj):
        return format_html('<strong>KES {:,.2f}</strong>', obj.current_amount)
    current_amount_display.short_description = 'Current Amount'

    def reset_jackpots(self, request, queryset):
        count = 0
        for jackpot in queryset:
            jackpot.reset_jackpot()
            count += 1
        self.message_user(request, f'Successfully reset {count} jackpots.')
    reset_jackpots.short_description = 'Reset selected jackpots'

    def activate_jackpots(self, request, queryset):
        count = queryset.update(status='ACTIVE')
        self.message_user(request, f'Successfully activated {count} jackpots.')
    activate_jackpots.short_description = 'Activate selected jackpots'

    def deactivate_jackpots(self, request, queryset):
        count = queryset.update(status='INACTIVE')
        self.message_user(request, f'Successfully deactivated {count} jackpots.')
    deactivate_jackpots.short_description = 'Deactivate selected jackpots'


@admin.register(JackpotWin)
class JackpotWinAdmin(admin.ModelAdmin):
    list_display = ['winner', 'jackpot', 'game', 'amount_display', 'bet_amount', 'is_verified', 'is_paid', 'won_at']
    list_filter = ['is_verified', 'is_paid', 'jackpot', 'won_at']
    search_fields = ['winner__username', 'winner__email', 'jackpot__name', 'game__name']
    readonly_fields = ['id', 'won_at']
    ordering = ['-won_at']
    actions = ['verify_wins', 'mark_as_paid']

    fieldsets = (
        ('Win Information', {
            'fields': ('jackpot', 'winner', 'game', 'amount', 'bet_amount')
        }),
        ('Status', {
            'fields': ('is_verified', 'is_paid', 'paid_at')
        }),
        ('Additional Data', {
            'fields': ('win_data',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'won_at'),
            'classes': ('collapse',)
        })
    )

    def amount_display(self, obj):
        return format_html('<strong>KES {:,.2f}</strong>', obj.amount)
    amount_display.short_description = 'Amount Won'

    def verify_wins(self, request, queryset):
        count = queryset.update(is_verified=True)
        self.message_user(request, f'Successfully verified {count} jackpot wins.')
    verify_wins.short_description = 'Verify selected wins'

    def mark_as_paid(self, request, queryset):
        from django.utils import timezone
        count = queryset.update(is_paid=True, paid_at=timezone.now())
        self.message_user(request, f'Successfully marked {count} wins as paid.')
    mark_as_paid.short_description = 'Mark selected wins as paid'


@admin.register(GameStatistics)
class GameStatisticsAdmin(admin.ModelAdmin):
    list_display = ['game', 'date', 'unique_players', 'total_rounds', 'total_bets', 'total_wins', 'gross_gaming_revenue', 'actual_rtp']
    list_filter = ['date', 'game__provider', 'game__category']
    search_fields = ['game__name']
    readonly_fields = ['id', 'created_at']
    ordering = ['-date', 'game__name']
    date_hierarchy = 'date'

    fieldsets = (
        ('Basic Information', {
            'fields': ('game', 'date')
        }),
        ('Player Statistics', {
            'fields': ('unique_players', 'total_sessions', 'total_rounds')
        }),
        ('Financial Statistics', {
            'fields': ('total_bets', 'total_wins', 'gross_gaming_revenue')
        }),
        ('Performance Metrics', {
            'fields': ('average_session_duration', 'average_bet_size', 'actual_rtp')
        }),
        ('Jackpot Statistics', {
            'fields': ('jackpot_contributions', 'jackpots_won', 'jackpot_payouts'),
            'classes': ('collapse',)
        })
    )

    def actual_rtp(self, obj):
        return f"{obj.actual_rtp:.2f}%"
    actual_rtp.short_description = 'Actual RTP'
