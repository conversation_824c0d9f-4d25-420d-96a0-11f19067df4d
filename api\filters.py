import django_filters
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta

from sports.models import Sport, League, Team, Match
from betting.models import Bet, BetSelection, Selection
from payments.models import Transaction, Deposit, Withdrawal
from promotions.models import Promotion, Bonus
from casino.models import Game, GameSession


class MatchFilter(django_filters.FilterSet):
    """Filter for matches"""
    sport = django_filters.ModelChoiceFilter(queryset=Sport.objects.all())
    league = django_filters.ModelChoiceFilter(queryset=League.objects.all())
    date_from = django_filters.DateTimeFilter(field_name='start_time', lookup_expr='gte')
    date_to = django_filters.DateTimeFilter(field_name='start_time', lookup_expr='lte')
    today = django_filters.BooleanFilter(method='filter_today')
    upcoming = django_filters.BooleanFilter(method='filter_upcoming')
    live = django_filters.BooleanFilter(method='filter_live')
    has_selections = django_filters.BooleanFilter(method='filter_has_selections')

    class Meta:
        model = Match
        fields = ['sport', 'league', 'status']

    def filter_today(self, queryset, name, value):
        if value:
            today = timezone.now().date()
            return queryset.filter(start_time__date=today)
        return queryset

    def filter_upcoming(self, queryset, name, value):
        if value:
            return queryset.filter(start_time__gte=timezone.now())
        return queryset

    def filter_live(self, queryset, name, value):
        if value:
            return queryset.filter(status='live')
        return queryset

    def filter_has_selections(self, queryset, name, value):
        if value:
            return queryset.filter(markets__selections__isnull=False).distinct()
        return queryset


class SelectionFilter(django_filters.FilterSet):
    """Filter for selections"""
    market_type = django_filters.CharFilter(field_name='market__bet_type__slug')
    min_odds = django_filters.NumberFilter(field_name='decimal_odds', lookup_expr='gte')
    max_odds = django_filters.NumberFilter(field_name='decimal_odds', lookup_expr='lte')

    class Meta:
        model = Selection
        fields = ['market__bet_type', 'status']


class BetFilter(django_filters.FilterSet):
    """Filter for bets"""
    date_from = django_filters.DateTimeFilter(field_name='placed_at', lookup_expr='gte')
    date_to = django_filters.DateTimeFilter(field_name='placed_at', lookup_expr='lte')
    min_stake = django_filters.NumberFilter(field_name='stake_amount', lookup_expr='gte')
    max_stake = django_filters.NumberFilter(field_name='stake_amount', lookup_expr='lte')
    sport = django_filters.ModelChoiceFilter(
        queryset=Sport.objects.all(),
        method='filter_by_sport'
    )
    
    class Meta:
        model = Bet
        fields = ['status', 'bet_type']
    
    def filter_by_sport(self, queryset, name, value):
        return queryset.filter(selections__event__sport=value).distinct()


class TransactionFilter(django_filters.FilterSet):
    """Filter for transactions"""
    date_from = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    date_to = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    min_amount = django_filters.NumberFilter(field_name='amount', lookup_expr='gte')
    max_amount = django_filters.NumberFilter(field_name='amount', lookup_expr='lte')
    
    class Meta:
        model = Transaction
        fields = ['transaction_type', 'status']


class DepositFilter(django_filters.FilterSet):
    """Filter for deposits"""
    date_from = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    date_to = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    min_amount = django_filters.NumberFilter(field_name='amount', lookup_expr='gte')
    max_amount = django_filters.NumberFilter(field_name='amount', lookup_expr='lte')
    
    class Meta:
        model = Deposit
        fields = ['status', 'payment_method']


class WithdrawalFilter(django_filters.FilterSet):
    """Filter for withdrawals"""
    date_from = django_filters.DateTimeFilter(field_name='requested_at', lookup_expr='gte')
    date_to = django_filters.DateTimeFilter(field_name='requested_at', lookup_expr='lte')
    min_amount = django_filters.NumberFilter(field_name='amount', lookup_expr='gte')
    max_amount = django_filters.NumberFilter(field_name='amount', lookup_expr='lte')
    
    class Meta:
        model = Withdrawal
        fields = ['status']


class PromotionFilter(django_filters.FilterSet):
    """Filter for promotions"""
    active = django_filters.BooleanFilter(method='filter_active')
    featured = django_filters.BooleanFilter(field_name='is_featured')
    target_audience = django_filters.CharFilter()
    
    class Meta:
        model = Promotion
        fields = ['promotion_type', 'bonus_type']
    
    def filter_active(self, queryset, name, value):
        if value:
            now = timezone.now()
            return queryset.filter(
                status='ACTIVE',
                start_date__lte=now,
                end_date__gte=now
            )
        return queryset


class BonusFilter(django_filters.FilterSet):
    """Filter for bonuses"""
    date_from = django_filters.DateTimeFilter(field_name='awarded_at', lookup_expr='gte')
    date_to = django_filters.DateTimeFilter(field_name='awarded_at', lookup_expr='lte')
    active = django_filters.BooleanFilter(method='filter_active')
    
    class Meta:
        model = Bonus
        fields = ['status', 'bonus_type']
    
    def filter_active(self, queryset, name, value):
        if value:
            return queryset.filter(status__in=['ACTIVE', 'WAGERING'])
        return queryset


class GameFilter(django_filters.FilterSet):
    """Filter for casino games"""
    provider = django_filters.CharFilter(field_name='provider__name', lookup_expr='icontains')
    min_bet = django_filters.NumberFilter(field_name='min_bet', lookup_expr='gte')
    max_bet = django_filters.NumberFilter(field_name='max_bet', lookup_expr='lte')
    min_rtp = django_filters.NumberFilter(field_name='rtp', lookup_expr='gte')
    popular = django_filters.BooleanFilter(method='filter_popular')
    
    class Meta:
        model = Game
        fields = ['game_type', 'is_active']
    
    def filter_popular(self, queryset, name, value):
        if value:
            # Filter games with high session count
            return queryset.filter(sessions__isnull=False).distinct()
        return queryset


class GameSessionFilter(django_filters.FilterSet):
    """Filter for game sessions"""
    date_from = django_filters.DateTimeFilter(field_name='started_at', lookup_expr='gte')
    date_to = django_filters.DateTimeFilter(field_name='started_at', lookup_expr='lte')
    game_type = django_filters.CharFilter(field_name='game__game_type')
    min_bet = django_filters.NumberFilter(field_name='bet_amount', lookup_expr='gte')
    max_bet = django_filters.NumberFilter(field_name='bet_amount', lookup_expr='lte')
    
    class Meta:
        model = GameSession
        fields = ['game', 'status']


# Custom filter methods
class DateRangeFilter(django_filters.FilterSet):
    """Base filter for date ranges"""
    PERIOD_CHOICES = [
        ('today', 'Today'),
        ('yesterday', 'Yesterday'),
        ('this_week', 'This Week'),
        ('last_week', 'Last Week'),
        ('this_month', 'This Month'),
        ('last_month', 'Last Month'),
        ('this_year', 'This Year'),
    ]
    
    period = django_filters.ChoiceFilter(
        choices=PERIOD_CHOICES,
        method='filter_by_period'
    )
    
    def filter_by_period(self, queryset, name, value):
        now = timezone.now()
        
        if value == 'today':
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=1)
        elif value == 'yesterday':
            start = now.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1)
            end = start + timedelta(days=1)
        elif value == 'this_week':
            start = now - timedelta(days=now.weekday())
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=7)
        elif value == 'last_week':
            start = now - timedelta(days=now.weekday() + 7)
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=7)
        elif value == 'this_month':
            start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if start.month == 12:
                end = start.replace(year=start.year + 1, month=1)
            else:
                end = start.replace(month=start.month + 1)
        elif value == 'last_month':
            if now.month == 1:
                start = now.replace(year=now.year - 1, month=12, day=1, hour=0, minute=0, second=0, microsecond=0)
                end = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            else:
                start = now.replace(month=now.month - 1, day=1, hour=0, minute=0, second=0, microsecond=0)
                end = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif value == 'this_year':
            start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            end = start.replace(year=start.year + 1)
        else:
            return queryset
        
        # Determine the date field to filter on
        date_field = getattr(self.Meta, 'date_field', 'created_at')
        filter_kwargs = {
            f'{date_field}__gte': start,
            f'{date_field}__lt': end
        }
        
        return queryset.filter(**filter_kwargs)


# Search filters
class GlobalSearchFilter(django_filters.FilterSet):
    """Global search across multiple models"""
    q = django_filters.CharFilter(method='global_search')
    
    def global_search(self, queryset, name, value):
        if not value:
            return queryset
        
        # This would be implemented based on the specific model
        # For now, return the original queryset
        return queryset
