from rest_framework import permissions
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the object.
        return obj.user == request.user


class IsOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to access it.
    """
    def has_object_permission(self, request, view, obj):
        return obj.user == request.user


class IsVerifiedUser(permissions.BasePermission):
    """
    Custom permission to only allow verified users.
    """
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            return request.user.userprofile.is_verified
        except:
            return False


class CanPlaceBets(permissions.BasePermission):
    """
    Custom permission for betting operations.
    """
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user has sufficient balance for betting
        try:
            wallet = request.user.wallet
            return wallet.balance > 0
        except:
            return False


class CanMakePayments(permissions.BasePermission):
    """
    Custom permission for payment operations.
    """
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Additional payment-specific checks can be added here
        return True


# Custom throttling classes
class BettingRateThrottle(UserRateThrottle):
    """
    Custom throttle for betting operations.
    """
    scope = 'betting'


class PaymentRateThrottle(UserRateThrottle):
    """
    Custom throttle for payment operations.
    """
    scope = 'payments'


class RegistrationRateThrottle(AnonRateThrottle):
    """
    Custom throttle for user registration.
    """
    scope = 'registration'


class LoginRateThrottle(AnonRateThrottle):
    """
    Custom throttle for login attempts.
    """
    scope = 'login'


# Permission mixins
class OwnerPermissionMixin:
    """
    Mixin to add owner-only permissions to views.
    """
    def get_permissions(self):
        permissions = super().get_permissions()
        permissions.append(IsOwner())
        return permissions


class VerifiedUserPermissionMixin:
    """
    Mixin to add verified user permissions to views.
    """
    def get_permissions(self):
        permissions = super().get_permissions()
        permissions.append(IsVerifiedUser())
        return permissions


class BettingPermissionMixin:
    """
    Mixin to add betting permissions to views.
    """
    def get_permissions(self):
        permissions = super().get_permissions()
        permissions.append(CanPlaceBets())
        return permissions
    
    def get_throttles(self):
        throttles = super().get_throttles()
        throttles.append(BettingRateThrottle())
        return throttles


class PaymentPermissionMixin:
    """
    Mixin to add payment permissions to views.
    """
    def get_permissions(self):
        permissions = super().get_permissions()
        permissions.append(CanMakePayments())
        return permissions
    
    def get_throttles(self):
        throttles = super().get_throttles()
        throttles.append(PaymentRateThrottle())
        return throttles
