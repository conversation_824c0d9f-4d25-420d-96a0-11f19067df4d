from django.shortcuts import render, redirect
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.cache import cache
from .forms import (
    UserRegistrationForm,
    EmailVerificationForm,
    PhoneVerificationForm,
    UserLoginForm,
    CustomPasswordResetForm,
    CustomSetPasswordForm,
    EmailChangeForm,
    RequestEmailVerificationForm,
    PhoneChangeForm,
    RequestSMSVerificationForm,
    SMSVerificationForm,
    Enable2FAForm,
    Verify2FASetupForm,
    TwoFactorLoginForm,
    Disable2FAForm,
    RegenerateBackupCodesForm,
    UserProfileUpdateForm,
    UserProfileDetailsForm,
    ProfilePictureForm,
    NotificationPreferencesForm,
    ResponsibleGamblingForm,
    SelfExclusionForm,
    AccountClosureForm,
    KYCDocumentUploadForm,
    KYCVerificationForm
)
from .models import (
    User, <PERSON>Log, UserSession, BetHistory, Transaction,
    ResponsibleGambling, KYCDocument, UserDevice, LoginAttempt, SuspiciousActivity
)
from .sms_service import sms_service
from .two_factor_service import two_factor_service
from .security_utils import (
    get_client_ip, track_user_device, check_rate_limit,
    log_login_attempt, detect_suspicious_login, is_account_locked,
    require_additional_verification
)
from django.db import models
import random
import string


def generate_verification_code():
    """Generate a 6-digit verification code"""
    return ''.join(random.choices(string.digits, k=6))


def send_email_verification(user, code):
    """Send email verification code"""
    subject = 'ZBet - Email Verification Code'
    message = f"""
    Welcome to ZBet!

    Your email verification code is: {code}

    This code will expire in 10 minutes.

    If you didn't request this code, please ignore this email.

    Best regards,
    ZBet Team
    """

    try:
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"Email sending failed: {e}")
        return False


def send_sms_verification(user, code=None):
    """Send SMS verification code using SMS service"""
    result = sms_service.send_verification_code(user, purpose='verification')
    return result['success']


def calculate_profile_completion(user):
    """Calculate user profile completion percentage"""
    total_fields = 15
    completed_fields = 0

    # Basic user fields
    if user.first_name:
        completed_fields += 1
    if user.last_name:
        completed_fields += 1
    if user.email:
        completed_fields += 1
    if user.phone_number:
        completed_fields += 1
    if user.date_of_birth:
        completed_fields += 1
    if user.country:
        completed_fields += 1
    if user.city:
        completed_fields += 1

    # Profile fields
    try:
        profile = user.profile
        if profile.avatar:
            completed_fields += 1
        if profile.gender:
            completed_fields += 1
        if profile.occupation:
            completed_fields += 1
        if profile.address_line_1:
            completed_fields += 1
        if profile.postal_code:
            completed_fields += 1
        if profile.emergency_contact_name:
            completed_fields += 1
        if profile.emergency_contact_phone:
            completed_fields += 1
        if profile.betting_experience:
            completed_fields += 1
    except:
        pass

    return int((completed_fields / total_fields) * 100)


def calculate_win_rate(profile):
    """Calculate user's betting win rate"""
    if not profile or profile.total_bets_placed == 0:
        return 0
    return int((profile.total_bets_won / profile.total_bets_placed) * 100)


def get_recent_user_activities(user):
    """Get recent user activities"""
    activities = []

    # Get recent security logs
    recent_logs = SecurityLog.objects.filter(
        user=user
    ).order_by('-created_at')[:5]

    for log in recent_logs:
        activity = {
            'type': log.event_type,
            'description': get_activity_description(log.event_type),
            'timestamp': log.created_at,
            'icon': get_activity_icon(log.event_type),
            'color': get_activity_color(log.event_type)
        }
        activities.append(activity)

    return activities


def get_activity_description(event_type):
    """Get human-readable description for activity type"""
    descriptions = {
        'login': 'Logged into account',
        'logout': 'Logged out of account',
        'password_change': 'Changed password',
        'email_verification': 'Verified email address',
        'phone_verification': 'Verified phone number',
        '2fa_enabled': 'Enabled two-factor authentication',
        '2fa_disabled': 'Disabled two-factor authentication',
        'social_login': 'Logged in with social account',
        'profile_update': 'Updated profile information',
        'kyc_submitted': 'Submitted KYC documents',
    }
    return descriptions.get(event_type, 'Account activity')


def get_activity_icon(event_type):
    """Get icon for activity type"""
    icons = {
        'login': 'fas fa-sign-in-alt',
        'logout': 'fas fa-sign-out-alt',
        'password_change': 'fas fa-key',
        'email_verification': 'fas fa-envelope-check',
        'phone_verification': 'fas fa-mobile-alt',
        '2fa_enabled': 'fas fa-shield-alt',
        '2fa_disabled': 'fas fa-shield-alt',
        'social_login': 'fas fa-link',
        'profile_update': 'fas fa-user-edit',
        'kyc_submitted': 'fas fa-id-card',
    }
    return icons.get(event_type, 'fas fa-info-circle')


def get_activity_color(event_type):
    """Get color for activity type"""
    colors = {
        'login': 'success',
        'logout': 'secondary',
        'password_change': 'warning',
        'email_verification': 'success',
        'phone_verification': 'success',
        '2fa_enabled': 'success',
        '2fa_disabled': 'warning',
        'social_login': 'info',
        'profile_update': 'primary',
        'kyc_submitted': 'info',
    }
    return colors.get(event_type, 'secondary')


def get_connected_social_accounts_count(user):
    """Get number of connected social accounts"""
    try:
        from allauth.socialaccount.models import SocialAccount
        return SocialAccount.objects.filter(user=user).count()
    except:
        return 0


def get_dashboard_quick_actions(user, security_status, completion_score):
    """Get quick actions for dashboard based on user status"""
    actions = []

    # Profile completion
    if completion_score < 80:
        actions.append({
            'title': 'Complete Your Profile',
            'description': f'Your profile is {completion_score}% complete',
            'url': 'accounts:profile_update',
            'icon': 'fas fa-user-edit',
            'color': 'warning',
            'priority': 1
        })

    # Email verification
    if not security_status['email_verified']:
        actions.append({
            'title': 'Verify Email',
            'description': 'Verify your email address for security',
            'url': 'accounts:request_email_verification',
            'icon': 'fas fa-envelope-check',
            'color': 'danger',
            'priority': 3
        })

    # Phone verification
    if not security_status['phone_verified']:
        actions.append({
            'title': 'Verify Phone',
            'description': 'Verify your phone for M-Pesa transactions',
            'url': 'accounts:request_sms_verification',
            'icon': 'fas fa-mobile-alt',
            'color': 'warning',
            'priority': 2
        })

    # 2FA setup
    if not security_status['two_factor_enabled']:
        actions.append({
            'title': 'Enable 2FA',
            'description': 'Add extra security to your account',
            'url': 'accounts:enable_2fa',
            'icon': 'fas fa-shield-alt',
            'color': 'info',
            'priority': 4
        })

    # KYC verification
    if not security_status['kyc_verified']:
        actions.append({
            'title': 'Complete KYC',
            'description': 'Verify your identity for higher limits',
            'url': '#',  # TODO: Add KYC URL when implemented
            'icon': 'fas fa-id-card',
            'color': 'primary',
            'priority': 5
        })

    # Sort by priority
    actions.sort(key=lambda x: x['priority'])

    return actions[:4]  # Return top 4 actions


def log_security_event(user, event_type, request, details=None):
    """Log security events"""
    SecurityLog.objects.create(
        user=user,
        event_type=event_type,
        ip_address=request.META.get('REMOTE_ADDR', ''),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        details=details or {}
    )


def register_view(request):
    """
    User registration view
    """
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()

            # Log registration event
            log_security_event(
                user,
                'registration',
                request,
                {'referral_code': form.cleaned_data.get('referral_code_used')}
            )

            # Generate and store verification codes
            email_code = generate_verification_code()
            phone_code = generate_verification_code()

            # Store codes in cache (expire in 10 minutes)
            cache.set(f'email_verification_{user.id}', email_code, 600)
            cache.set(f'phone_verification_{user.id}', phone_code, 600)

            # Send verification emails/SMS
            email_sent = send_email_verification(user, email_code)
            send_sms_verification(user, phone_code)

            if email_sent:
                messages.success(
                    request,
                    'Registration successful! Please check your email for verification code.'
                )
            else:
                messages.warning(
                    request,
                    'Registration successful, but email verification failed. Please try resending.'
                )

            # Store user ID in session for verification process
            request.session['pending_verification_user_id'] = str(user.id)

            return redirect('accounts:verify_email')
    else:
        form = UserRegistrationForm()

    return render(request, 'accounts/register.html', {'form': form})


def verify_email_view(request):
    """
    Email verification view
    """
    user_id = request.session.get('pending_verification_user_id')
    if not user_id:
        messages.error(request, 'No pending verification found.')
        return redirect('accounts:register')

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        messages.error(request, 'Invalid verification session.')
        return redirect('accounts:register')

    if user.is_verified:
        messages.info(request, 'Email already verified.')
        return redirect('accounts:verify_phone')

    if request.method == 'POST':
        form = EmailVerificationForm(request.POST)
        if form.is_valid():
            entered_code = form.cleaned_data['verification_code']
            stored_code = cache.get(f'email_verification_{user.id}')

            if stored_code and entered_code == stored_code:
                # Mark email as verified
                user.is_verified = True
                user.email_verified_at = timezone.now()
                user.save()

                # Clear the verification code
                cache.delete(f'email_verification_{user.id}')

                # Log verification event
                log_security_event(user, 'email_verification', request)

                messages.success(request, 'Email verified successfully!')
                return redirect('accounts:verify_phone')
            else:
                messages.error(request, 'Invalid or expired verification code.')
    else:
        form = EmailVerificationForm()

    return render(request, 'accounts/verify_email.html', {
        'form': form,
        'user': user
    })


def verify_phone_view(request):
    """
    Phone verification view
    """
    user_id = request.session.get('pending_verification_user_id')
    if not user_id:
        messages.error(request, 'No pending verification found.')
        return redirect('accounts:register')

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        messages.error(request, 'Invalid verification session.')
        return redirect('accounts:register')

    if not user.is_verified:
        messages.error(request, 'Please verify your email first.')
        return redirect('accounts:verify_email')

    if user.phone_verified_at:
        messages.info(request, 'Phone already verified.')
        return redirect('accounts:registration_complete')

    if request.method == 'POST':
        form = PhoneVerificationForm(request.POST)
        if form.is_valid():
            entered_code = form.cleaned_data['verification_code']
            stored_code = cache.get(f'phone_verification_{user.id}')

            if stored_code and entered_code == stored_code:
                # Mark phone as verified
                user.phone_verified_at = timezone.now()
                user.save()

                # Clear the verification code
                cache.delete(f'phone_verification_{user.id}')

                # Log verification event
                log_security_event(user, 'phone_verification', request)

                messages.success(request, 'Phone verified successfully!')
                return redirect('accounts:registration_complete')
            else:
                messages.error(request, 'Invalid or expired verification code.')
    else:
        form = PhoneVerificationForm()

    return render(request, 'accounts/verify_phone.html', {
        'form': form,
        'user': user
    })


def registration_complete_view(request):
    """
    Registration completion view
    """
    user_id = request.session.get('pending_verification_user_id')
    if not user_id:
        messages.error(request, 'No pending verification found.')
        return redirect('accounts:register')

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        messages.error(request, 'Invalid verification session.')
        return redirect('accounts:register')

    if not (user.is_verified and user.phone_verified_at):
        messages.error(request, 'Please complete all verification steps.')
        return redirect('accounts:verify_email')

    # Clear the session
    del request.session['pending_verification_user_id']

    # Log the user in
    login(request, user)

    # Log login event
    log_security_event(user, 'login', request, {'registration_login': True})

    messages.success(
        request,
        f'Welcome to ZBet, {user.first_name}! Your account has been created successfully.'
    )

    return render(request, 'accounts/registration_complete.html', {'user': user})


@require_http_methods(["POST"])
def resend_verification_view(request):
    """
    Resend verification code (AJAX endpoint)
    """
    user_id = request.session.get('pending_verification_user_id')
    if not user_id:
        return JsonResponse({'success': False, 'error': 'No pending verification found.'})

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Invalid verification session.'})

    verification_type = request.POST.get('verification_type')

    if verification_type == 'email':
        # Generate new email verification code
        email_code = generate_verification_code()
        cache.set(f'email_verification_{user.id}', email_code, 600)

        if send_email_verification(user, email_code):
            return JsonResponse({
                'success': True,
                'message': 'Email verification code sent successfully.'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Failed to send email verification code.'
            })

    elif verification_type == 'phone':
        # Generate new phone verification code
        phone_code = generate_verification_code()
        cache.set(f'phone_verification_{user.id}', phone_code, 600)

        if send_sms_verification(user, phone_code):
            return JsonResponse({
                'success': True,
                'message': 'SMS verification code sent successfully.'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Failed to send SMS verification code.'
            })

    else:
        return JsonResponse({'success': False, 'error': 'Invalid verification type.'})


def check_username_availability(request):
    """
    Check username availability (AJAX endpoint)
    """
    username = request.GET.get('username', '').strip()

    if not username:
        return JsonResponse({'available': False, 'message': 'Username is required.'})

    if len(username) < 3:
        return JsonResponse({'available': False, 'message': 'Username must be at least 3 characters long.'})

    if User.objects.filter(username=username).exists():
        return JsonResponse({'available': False, 'message': 'Username is already taken.'})

    # Check for inappropriate usernames
    inappropriate_words = ['admin', 'root', 'test', 'bet', 'casino', 'support']
    if any(word in username.lower() for word in inappropriate_words):
        return JsonResponse({'available': False, 'message': 'This username is not allowed.'})

    return JsonResponse({'available': True, 'message': 'Username is available.'})


def check_email_availability(request):
    """
    Check email availability (AJAX endpoint)
    """
    email = request.GET.get('email', '').strip()

    if not email:
        return JsonResponse({'available': False, 'message': 'Email is required.'})

    if User.objects.filter(email=email).exists():
        return JsonResponse({'available': False, 'message': 'Email is already registered.'})

    return JsonResponse({'available': True, 'message': 'Email is available.'})


def validate_referral_code(request):
    """
    Validate referral code (AJAX endpoint)
    """
    referral_code = request.GET.get('referral_code', '').strip()

    if not referral_code:
        return JsonResponse({'valid': True, 'message': 'Referral code is optional.'})

    try:
        referrer = User.objects.get(referral_code=referral_code)
        return JsonResponse({
            'valid': True,
            'message': f'Valid referral code from {referrer.first_name} {referrer.last_name}.'
        })
    except User.DoesNotExist:
        return JsonResponse({'valid': False, 'message': 'Invalid referral code.'})


def login_view(request):
    """
    User login view
    """
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = UserLoginForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()

            # Check remember me option
            remember_me = form.cleaned_data.get('remember_me', False)
            if remember_me:
                # Set session to expire in 30 days
                request.session.set_expiry(30 * 24 * 60 * 60)
            else:
                # Set session to expire when browser closes
                request.session.set_expiry(0)

            # Check if 2FA is required
            if two_factor_service.is_2fa_enabled(user):
                # Store user ID in session for 2FA verification
                request.session['pending_2fa_user_id'] = str(user.id)
                request.session['remember_me'] = remember_me

                # Send SMS code if SMS 2FA is enabled
                methods = two_factor_service.get_2fa_methods(user)
                if 'sms' in methods:
                    result = two_factor_service.send_sms_2fa_code(user)
                    if not result['success']:
                        messages.warning(request, 'Failed to send SMS code. You can still use your authenticator app.')

                messages.info(request, 'Please enter your 2FA code to complete login.')
                return redirect('accounts:two_factor_login')

            # Log the user in (no 2FA required)
            login(request, user)

            # Create user session record
            UserSession.objects.create(
                user=user,
                session_key=request.session.session_key,
                ip_address=request.META.get('REMOTE_ADDR', ''),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                device_info={
                    'remember_me': remember_me,
                    'login_method': 'form'
                }
            )

            # Log security event
            log_security_event(user, 'login', request, {
                'remember_me': remember_me,
                'login_method': 'form'
            })

            # Update last login IP
            user.last_login_ip = request.META.get('REMOTE_ADDR', '')
            user.save(update_fields=['last_login_ip'])

            messages.success(request, f'Welcome back, {user.first_name}!')

            # Redirect to next page or dashboard
            next_url = request.GET.get('next', 'accounts:dashboard')
            return redirect(next_url)
        else:
            # Log failed login attempt
            username = request.POST.get('username', '')
            log_security_event(
                None,
                'failed_login',
                request,
                {'attempted_username': username}
            )
    else:
        form = UserLoginForm()

    return render(request, 'accounts/login.html', {'form': form})


@login_required
def logout_view(request):
    """
    User logout view
    """
    user = request.user

    # End user session
    try:
        session = UserSession.objects.get(
            user=user,
            session_key=request.session.session_key,
            is_active=True
        )
        session.is_active = False
        session.ended_at = timezone.now()
        session.save()
    except UserSession.DoesNotExist:
        pass

    # Log security event
    log_security_event(user, 'logout', request)

    # Logout user
    logout(request)

    messages.success(request, 'You have been logged out successfully.')
    return redirect('accounts:login')


@login_required
def dashboard_view(request):
    """
    Enhanced user dashboard view with comprehensive statistics
    """
    user = request.user

    # Get or create user profile
    try:
        profile = user.profile
    except:
        from .models import UserProfile
        profile = UserProfile.objects.create(user=user)

    # Get user's recent sessions
    recent_sessions = UserSession.objects.filter(
        user=user
    ).order_by('-created_at')[:5]

    # Get 2FA status
    two_factor_status = two_factor_service.get_2fa_status(user)

    # Calculate account completion percentage
    completion_score = calculate_profile_completion(user)

    # Get recent activity (placeholder for now)
    recent_activities = get_recent_user_activities(user)

    # Account statistics
    account_stats = {
        'total_bets': profile.total_bets_placed,
        'total_wins': profile.total_bets_won,
        'win_rate': calculate_win_rate(profile),
        'total_deposits': profile.total_deposits,
        'total_withdrawals': profile.total_withdrawals,
        'net_position': profile.total_deposits - profile.total_withdrawals,
    }

    # Security status
    security_status = {
        'email_verified': user.is_verified,
        'phone_verified': bool(user.phone_verified_at),
        'kyc_verified': user.is_kyc_verified,
        'two_factor_enabled': two_factor_status['enabled'],
        'social_accounts_connected': get_connected_social_accounts_count(user),
    }

    # Quick actions based on account status
    quick_actions = get_dashboard_quick_actions(user, security_status, completion_score)

    context = {
        'user': user,
        'profile': profile,
        'recent_sessions': recent_sessions,
        'completion_score': completion_score,
        'account_stats': account_stats,
        'security_status': security_status,
        'two_factor_status': two_factor_status,
        'recent_activities': recent_activities,
        'quick_actions': quick_actions,
    }

    return render(request, 'accounts/dashboard.html', context)


def password_reset_view(request):
    """
    Password reset request view
    """
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = CustomPasswordResetForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']

            try:
                user = User.objects.get(email=email)

                # Generate password reset token
                from django.contrib.auth.tokens import default_token_generator
                from django.utils.http import urlsafe_base64_encode
                from django.utils.encoding import force_bytes

                token = default_token_generator.make_token(user)
                uid = urlsafe_base64_encode(force_bytes(user.pk))

                # Store token in cache for 1 hour
                cache.set(f'password_reset_{uid}_{token}', user.id, 3600)

                # Send password reset email
                reset_link = request.build_absolute_uri(
                    f"/accounts/password-reset-confirm/{uid}/{token}/"
                )

                subject = 'ZBet - Password Reset Request'
                message = f"""
                Hello {user.first_name},

                You have requested to reset your password for your ZBet account.

                Click the link below to reset your password:
                {reset_link}

                This link will expire in 1 hour.

                If you didn't request this password reset, please ignore this email.

                Best regards,
                ZBet Team
                """

                try:
                    send_mail(
                        subject,
                        message,
                        settings.DEFAULT_FROM_EMAIL,
                        [user.email],
                        fail_silently=False,
                    )

                    # Log security event
                    log_security_event(user, 'password_reset', request)

                    messages.success(
                        request,
                        'Password reset instructions have been sent to your email.'
                    )

                except Exception as e:
                    messages.error(
                        request,
                        'Failed to send password reset email. Please try again.'
                    )

            except User.DoesNotExist:
                # Don't reveal that the email doesn't exist
                messages.success(
                    request,
                    'If an account with that email exists, password reset instructions have been sent.'
                )

            return redirect('accounts:password_reset_done')
    else:
        form = CustomPasswordResetForm()

    return render(request, 'accounts/password_reset.html', {'form': form})


def password_reset_done_view(request):
    """
    Password reset done view
    """
    return render(request, 'accounts/password_reset_done.html')


def password_reset_confirm_view(request, uidb64, token):
    """
    Password reset confirmation view
    """
    from django.contrib.auth.tokens import default_token_generator
    from django.utils.http import urlsafe_base64_decode
    from django.utils.encoding import force_str

    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    # Check if token is valid and not expired
    valid_token = (
        user is not None and
        default_token_generator.check_token(user, token) and
        cache.get(f'password_reset_{uidb64}_{token}') == user.id
    )

    if valid_token:
        if request.method == 'POST':
            form = CustomSetPasswordForm(user, request.POST)
            if form.is_valid():
                form.save()

                # Clear the reset token
                cache.delete(f'password_reset_{uidb64}_{token}')

                # Log security event
                log_security_event(user, 'password_change', request, {
                    'method': 'reset'
                })

                messages.success(
                    request,
                    'Your password has been reset successfully. You can now log in.'
                )

                return redirect('accounts:login')
        else:
            form = CustomSetPasswordForm(user)

        return render(request, 'accounts/password_reset_confirm.html', {
            'form': form,
            'validlink': True
        })
    else:
        return render(request, 'accounts/password_reset_confirm.html', {
            'validlink': False
        })


@login_required
def request_email_verification_view(request):
    """
    Request email verification for current user
    """
    user = request.user

    if user.is_verified:
        messages.info(request, 'Your email is already verified.')
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = RequestEmailVerificationForm(user, request.POST)
        if form.is_valid():
            # Generate verification code
            email_code = generate_verification_code()

            # Store code in cache (expire in 10 minutes)
            cache.set(f'email_verification_{user.id}', email_code, 600)

            # Send verification email
            if send_email_verification(user, email_code):
                messages.success(
                    request,
                    'Verification code sent to your email address.'
                )
                return redirect('accounts:verify_email_standalone')
            else:
                messages.error(
                    request,
                    'Failed to send verification email. Please try again.'
                )
    else:
        form = RequestEmailVerificationForm(user)

    return render(request, 'accounts/request_email_verification.html', {
        'form': form,
        'user': user
    })


@login_required
def verify_email_standalone_view(request):
    """
    Standalone email verification view for logged-in users
    """
    user = request.user

    if user.is_verified:
        messages.info(request, 'Your email is already verified.')
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = EmailVerificationForm(request.POST)
        if form.is_valid():
            entered_code = form.cleaned_data['verification_code']
            stored_code = cache.get(f'email_verification_{user.id}')

            if stored_code and entered_code == stored_code:
                # Mark email as verified
                user.is_verified = True
                user.email_verified_at = timezone.now()
                user.save()

                # Clear the verification code
                cache.delete(f'email_verification_{user.id}')

                # Log verification event
                log_security_event(user, 'email_verification', request, {
                    'method': 'standalone'
                })

                messages.success(request, 'Email verified successfully!')
                return redirect('accounts:dashboard')
            else:
                messages.error(request, 'Invalid or expired verification code.')
    else:
        form = EmailVerificationForm()

    return render(request, 'accounts/verify_email_standalone.html', {
        'form': form,
        'user': user
    })


@login_required
def change_email_view(request):
    """
    Change user email address
    """
    user = request.user

    if request.method == 'POST':
        form = EmailChangeForm(user, request.POST)
        if form.is_valid():
            new_email = form.cleaned_data['new_email']

            # Store the new email temporarily
            cache.set(f'new_email_{user.id}', new_email, 3600)  # 1 hour

            # Generate verification code for new email
            email_code = generate_verification_code()
            cache.set(f'email_change_verification_{user.id}', email_code, 600)  # 10 minutes

            # Send verification email to new address
            subject = 'ZBet - Verify Your New Email Address'
            message = f"""
            Hello {user.first_name},

            You have requested to change your email address on ZBet.

            Your verification code is: {email_code}

            This code will expire in 10 minutes.

            If you didn't request this change, please ignore this email.

            Best regards,
            ZBet Team
            """

            try:
                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL,
                    [new_email],
                    fail_silently=False,
                )

                # Log security event
                log_security_event(user, 'email_change', request, {
                    'old_email': user.email,
                    'new_email': new_email
                })

                messages.success(
                    request,
                    f'Verification code sent to {new_email}. Please verify to complete the change.'
                )

                return redirect('accounts:verify_email_change')

            except Exception as e:
                messages.error(
                    request,
                    'Failed to send verification email. Please try again.'
                )
    else:
        form = EmailChangeForm(user)

    return render(request, 'accounts/change_email.html', {
        'form': form,
        'user': user
    })


@login_required
def verify_email_change_view(request):
    """
    Verify email change with verification code
    """
    user = request.user
    new_email = cache.get(f'new_email_{user.id}')

    if not new_email:
        messages.error(request, 'No pending email change found.')
        return redirect('accounts:change_email')

    if request.method == 'POST':
        form = EmailVerificationForm(request.POST)
        if form.is_valid():
            entered_code = form.cleaned_data['verification_code']
            stored_code = cache.get(f'email_change_verification_{user.id}')

            if stored_code and entered_code == stored_code:
                # Update user email
                old_email = user.email
                user.email = new_email
                user.is_verified = True
                user.email_verified_at = timezone.now()
                user.save()

                # Clear cache
                cache.delete(f'new_email_{user.id}')
                cache.delete(f'email_change_verification_{user.id}')

                # Log security event
                log_security_event(user, 'email_change', request, {
                    'old_email': old_email,
                    'new_email': new_email,
                    'verified': True
                })

                messages.success(
                    request,
                    f'Email address successfully changed to {new_email}.'
                )

                return redirect('accounts:dashboard')
            else:
                messages.error(request, 'Invalid or expired verification code.')
    else:
        form = EmailVerificationForm()

    return render(request, 'accounts/verify_email_change.html', {
        'form': form,
        'user': user,
        'new_email': new_email
    })


@login_required
def request_sms_verification_view(request):
    """
    Request SMS verification for current user
    """
    user = request.user

    if user.phone_verified_at:
        messages.info(request, 'Your phone number is already verified.')
        return redirect('accounts:dashboard')

    if not user.phone_number:
        messages.error(request, 'No phone number found. Please update your profile.')
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = RequestSMSVerificationForm(user, request.POST)
        if form.is_valid():
            # Send verification code using SMS service
            result = sms_service.send_verification_code(user, purpose='verification')

            if result['success']:
                messages.success(
                    request,
                    'Verification code sent to your phone number.'
                )
                return redirect('accounts:verify_sms_standalone')
            else:
                messages.error(
                    request,
                    f"Failed to send SMS: {result['message']}"
                )
    else:
        form = RequestSMSVerificationForm(user)

    return render(request, 'accounts/request_sms_verification.html', {
        'form': form,
        'user': user
    })


@login_required
def verify_sms_standalone_view(request):
    """
    Standalone SMS verification view for logged-in users
    """
    user = request.user

    if user.phone_verified_at:
        messages.info(request, 'Your phone number is already verified.')
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = SMSVerificationForm(request.POST)
        if form.is_valid():
            entered_code = form.cleaned_data['verification_code']

            # Verify code using SMS service
            if sms_service.verify_code(user, entered_code, purpose='verification'):
                # Mark phone as verified
                user.phone_verified_at = timezone.now()
                user.save()

                # Log verification event
                log_security_event(user, 'phone_verification', request, {
                    'method': 'standalone'
                })

                messages.success(request, 'Phone number verified successfully!')
                return redirect('accounts:dashboard')
            else:
                messages.error(request, 'Invalid or expired verification code.')
    else:
        form = SMSVerificationForm()

    return render(request, 'accounts/verify_sms_standalone.html', {
        'form': form,
        'user': user
    })


@login_required
def change_phone_view(request):
    """
    Change user phone number
    """
    user = request.user

    if request.method == 'POST':
        form = PhoneChangeForm(user, request.POST)
        if form.is_valid():
            new_phone = form.cleaned_data['new_phone']

            # Store the new phone temporarily
            cache.set(f'new_phone_{user.id}', str(new_phone), 3600)  # 1 hour

            # Send verification code to new phone
            result = sms_service.send_verification_code(
                user,
                phone_number=new_phone,
                purpose='change'
            )

            if result['success']:
                # Log security event
                log_security_event(user, 'phone_change', request, {
                    'old_phone': str(user.phone_number),
                    'new_phone': str(new_phone)
                })

                messages.success(
                    request,
                    f'Verification code sent to {new_phone}. Please verify to complete the change.'
                )

                return redirect('accounts:verify_phone_change')
            else:
                messages.error(
                    request,
                    f"Failed to send verification code: {result['message']}"
                )
    else:
        form = PhoneChangeForm(user)

    return render(request, 'accounts/change_phone.html', {
        'form': form,
        'user': user
    })


@login_required
def verify_phone_change_view(request):
    """
    Verify phone change with verification code
    """
    user = request.user
    new_phone = cache.get(f'new_phone_{user.id}')

    if not new_phone:
        messages.error(request, 'No pending phone change found.')
        return redirect('accounts:change_phone')

    if request.method == 'POST':
        form = SMSVerificationForm(request.POST)
        if form.is_valid():
            entered_code = form.cleaned_data['verification_code']

            # Verify code using SMS service
            if sms_service.verify_code(user, entered_code, purpose='change'):
                # Update user phone
                old_phone = user.phone_number
                user.phone_number = new_phone
                user.phone_verified_at = timezone.now()
                user.save()

                # Clear cache
                cache.delete(f'new_phone_{user.id}')

                # Log security event
                log_security_event(user, 'phone_change', request, {
                    'old_phone': str(old_phone),
                    'new_phone': str(new_phone),
                    'verified': True
                })

                messages.success(
                    request,
                    f'Phone number successfully changed to {new_phone}.'
                )

                return redirect('accounts:dashboard')
            else:
                messages.error(request, 'Invalid or expired verification code.')
    else:
        form = SMSVerificationForm()

    return render(request, 'accounts/verify_phone_change.html', {
        'form': form,
        'user': user,
        'new_phone': new_phone
    })


@require_http_methods(["POST"])
def resend_sms_verification_view(request):
    """
    Resend SMS verification code (AJAX endpoint)
    """
    user_id = request.session.get('pending_verification_user_id')
    if not user_id:
        if request.user.is_authenticated:
            user = request.user
        else:
            return JsonResponse({'success': False, 'error': 'No user found.'})
    else:
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Invalid user.'})

    verification_type = request.POST.get('verification_type', 'verification')

    # Use SMS service to resend code
    result = sms_service.resend_verification_code(user, purpose=verification_type)

    if result['success']:
        return JsonResponse({
            'success': True,
            'message': 'SMS verification code sent successfully.'
        })
    else:
        return JsonResponse({
            'success': False,
            'error': result['message']
        })


def two_factor_login_view(request):
    """
    2FA verification during login
    """
    user_id = request.session.get('pending_2fa_user_id')
    if not user_id:
        messages.error(request, 'No pending 2FA verification found.')
        return redirect('accounts:login')

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        messages.error(request, 'Invalid 2FA session.')
        return redirect('accounts:login')

    # Get available 2FA methods
    methods = two_factor_service.get_2fa_methods(user)
    if not methods:
        messages.error(request, '2FA not properly configured.')
        return redirect('accounts:login')

    if request.method == 'POST':
        form = TwoFactorLoginForm(request.POST)
        if form.is_valid():
            code = form.cleaned_data['verification_code']

            if two_factor_service.verify_2fa_code(user, code):
                # Clear 2FA session data
                remember_me = request.session.get('remember_me', False)
                del request.session['pending_2fa_user_id']
                if 'remember_me' in request.session:
                    del request.session['remember_me']

                # Set session expiry
                if remember_me:
                    request.session.set_expiry(30 * 24 * 60 * 60)
                else:
                    request.session.set_expiry(0)

                # Log the user in
                login(request, user)

                # Create user session record
                UserSession.objects.create(
                    user=user,
                    session_key=request.session.session_key,
                    ip_address=request.META.get('REMOTE_ADDR', ''),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    device_info={
                        'remember_me': remember_me,
                        'login_method': '2fa'
                    }
                )

                # Log security event
                log_security_event(user, 'login', request, {
                    'remember_me': remember_me,
                    'login_method': '2fa',
                    'code_type': 'backup' if len(code) == 8 else '2fa'
                })

                # Update last login IP
                user.last_login_ip = request.META.get('REMOTE_ADDR', '')
                user.save(update_fields=['last_login_ip'])

                messages.success(request, f'Welcome back, {user.first_name}!')

                # Redirect to next page or dashboard
                next_url = request.GET.get('next', 'accounts:dashboard')
                return redirect(next_url)
            else:
                messages.error(request, 'Invalid verification code.')
    else:
        form = TwoFactorLoginForm()

    return render(request, 'accounts/two_factor_login.html', {
        'form': form,
        'user': user,
        'methods': methods
    })


@login_required
def enable_2fa_view(request):
    """
    Enable 2FA for user
    """
    user = request.user

    # Check if 2FA is already enabled
    if two_factor_service.is_2fa_enabled(user):
        messages.info(request, '2FA is already enabled for your account.')
        return redirect('accounts:two_factor_settings')

    if request.method == 'POST':
        form = Enable2FAForm(user, request.POST)
        if form.is_valid():
            method = form.cleaned_data['method']

            # Enable 2FA
            two_factor = two_factor_service.enable_2fa(user, method)

            # Log security event
            log_security_event(user, '2fa_enabled', request, {
                'method': method
            })

            messages.success(request, '2FA has been enabled for your account.')

            # Redirect to setup verification
            if method in ['totp', 'both']:
                return redirect('accounts:setup_totp')
            else:
                return redirect('accounts:two_factor_settings')
    else:
        form = Enable2FAForm(user)

    return render(request, 'accounts/enable_2fa.html', {
        'form': form,
        'user': user
    })


@login_required
def setup_totp_view(request):
    """
    Setup TOTP authenticator
    """
    user = request.user

    # Check if user has 2FA enabled
    if not two_factor_service.is_2fa_enabled(user):
        messages.error(request, 'Please enable 2FA first.')
        return redirect('accounts:enable_2fa')

    # Generate QR code
    qr_data = two_factor_service.generate_qr_code(user)

    if request.method == 'POST':
        form = Verify2FASetupForm(request.POST)
        if form.is_valid():
            code = form.cleaned_data['verification_code']

            if two_factor_service.verify_totp_setup(user, code):
                messages.success(request, 'Authenticator app has been successfully configured!')
                return redirect('accounts:two_factor_settings')
            else:
                messages.error(request, 'Invalid verification code. Please try again.')
    else:
        form = Verify2FASetupForm()

    return render(request, 'accounts/setup_totp.html', {
        'form': form,
        'user': user,
        'qr_code': qr_data['qr_code'],
        'secret': qr_data['secret']
    })


@login_required
def two_factor_settings_view(request):
    """
    2FA settings and management
    """
    user = request.user
    status = two_factor_service.get_2fa_status(user)
    backup_info = two_factor_service.get_backup_codes_info(user)

    return render(request, 'accounts/two_factor_settings.html', {
        'user': user,
        'status': status,
        'backup_info': backup_info
    })


@login_required
def disable_2fa_view(request):
    """
    Disable 2FA for user
    """
    user = request.user

    # Check if 2FA is enabled
    if not two_factor_service.is_2fa_enabled(user):
        messages.info(request, '2FA is not enabled for your account.')
        return redirect('accounts:two_factor_settings')

    if request.method == 'POST':
        form = Disable2FAForm(user, request.POST)
        if form.is_valid():
            # Disable 2FA
            two_factor_service.disable_2fa(user)

            # Log security event
            log_security_event(user, '2fa_disabled', request)

            messages.success(request, '2FA has been disabled for your account.')
            return redirect('accounts:two_factor_settings')
    else:
        form = Disable2FAForm(user)

    return render(request, 'accounts/disable_2fa.html', {
        'form': form,
        'user': user
    })


@login_required
def regenerate_backup_codes_view(request):
    """
    Regenerate backup codes
    """
    user = request.user

    # Check if 2FA is enabled
    if not two_factor_service.is_2fa_enabled(user):
        messages.error(request, '2FA must be enabled to generate backup codes.')
        return redirect('accounts:enable_2fa')

    if request.method == 'POST':
        form = RegenerateBackupCodesForm(user, request.POST)
        if form.is_valid():
            # Regenerate backup codes
            codes = two_factor_service.regenerate_backup_codes(user)

            # Log security event
            log_security_event(user, 'backup_codes_regenerated', request)

            messages.success(request, 'New backup codes have been generated.')

            return render(request, 'accounts/backup_codes.html', {
                'user': user,
                'codes': codes,
                'regenerated': True
            })
    else:
        form = RegenerateBackupCodesForm(user)

    return render(request, 'accounts/regenerate_backup_codes.html', {
        'form': form,
        'user': user
    })


@login_required
def view_backup_codes_view(request):
    """
    View current backup codes
    """
    user = request.user

    # Check if 2FA is enabled
    if not two_factor_service.is_2fa_enabled(user):
        messages.error(request, '2FA must be enabled to view backup codes.')
        return redirect('accounts:enable_2fa')

    backup_info = two_factor_service.get_backup_codes_info(user)

    return render(request, 'accounts/backup_codes.html', {
        'user': user,
        'codes': backup_info['codes'] if backup_info else [],
        'backup_info': backup_info,
        'regenerated': False
    })


@require_http_methods(["POST"])
def resend_2fa_sms_view(request):
    """
    Resend 2FA SMS code (AJAX endpoint)
    """
    user_id = request.session.get('pending_2fa_user_id')
    if not user_id:
        return JsonResponse({'success': False, 'error': 'No pending 2FA verification found.'})

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Invalid user.'})

    # Send SMS 2FA code
    result = two_factor_service.send_sms_2fa_code(user)

    if result['success']:
        return JsonResponse({
            'success': True,
            'message': '2FA code sent successfully.'
        })
    else:
        return JsonResponse({
            'success': False,
            'error': result['message']
        })


@login_required
def social_connections_view(request):
    """
    Manage social account connections
    """
    return render(request, 'accounts/social_connections.html', {
        'user': request.user
    })


@login_required
def profile_update_view(request):
    """
    Update user profile information
    """
    user = request.user

    # Get or create user profile
    try:
        profile = user.profile
    except:
        from .models import UserProfile
        profile = UserProfile.objects.create(user=user)

    if request.method == 'POST':
        user_form = UserProfileUpdateForm(request.POST, instance=user)
        profile_form = UserProfileDetailsForm(request.POST, instance=profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()

            # Log profile update
            log_security_event(user, 'profile_update', request)

            messages.success(request, 'Profile updated successfully!')
            return redirect('accounts:profile_update')
    else:
        user_form = UserProfileUpdateForm(instance=user)
        profile_form = UserProfileDetailsForm(instance=profile)

    # Calculate completion score
    completion_score = calculate_profile_completion(user)

    context = {
        'user': user,
        'profile': profile,
        'user_form': user_form,
        'profile_form': profile_form,
        'completion_score': completion_score,
    }

    return render(request, 'accounts/profile_update.html', context)


@login_required
def profile_picture_upload_view(request):
    """
    Upload or remove profile picture
    """
    user = request.user

    # Get or create user profile
    try:
        profile = user.profile
    except:
        from .models import UserProfile
        profile = UserProfile.objects.create(user=user)

    if request.method == 'POST':
        # Check if removing avatar
        if request.POST.get('remove_avatar'):
            if profile.avatar:
                # Delete the file
                profile.avatar.delete()
                profile.save()

                # Log profile update
                log_security_event(user, 'profile_update', request, {
                    'action': 'avatar_removed'
                })

                messages.success(request, 'Profile picture removed successfully!')
            else:
                messages.info(request, 'No profile picture to remove.')

            return redirect('accounts:profile_picture_upload')

        # Handle avatar upload
        form = ProfilePictureForm(request.POST, request.FILES, instance=profile)

        if form.is_valid():
            # Delete old avatar if exists
            if profile.avatar:
                profile.avatar.delete()

            form.save()

            # Log profile update
            log_security_event(user, 'profile_update', request, {
                'action': 'avatar_upload'
            })

            messages.success(request, 'Profile picture updated successfully!')
            return redirect('accounts:profile_update')
    else:
        form = ProfilePictureForm(instance=profile)

    context = {
        'user': user,
        'profile': profile,
        'form': form,
    }

    return render(request, 'accounts/profile_picture_upload.html', context)


@login_required
def notification_preferences_view(request):
    """
    Manage notification preferences
    """
    user = request.user

    # Get or create user profile
    try:
        profile = user.profile
    except:
        from .models import UserProfile
        profile = UserProfile.objects.create(user=user)

    if request.method == 'POST':
        form = NotificationPreferencesForm(request.POST, instance=profile)

        if form.is_valid():
            form.save()

            # Log preference update
            log_security_event(user, 'profile_update', request, {
                'action': 'notification_preferences'
            })

            messages.success(request, 'Notification preferences updated successfully!')
            return redirect('accounts:notification_preferences')
    else:
        form = NotificationPreferencesForm(instance=profile)

    context = {
        'user': user,
        'profile': profile,
        'form': form,
    }

    return render(request, 'accounts/notification_preferences.html', context)


@login_required
def betting_history_view(request):
    """
    Display user's betting history
    """
    user = request.user

    # Get betting history with pagination
    from django.core.paginator import Paginator

    # Filter parameters
    status_filter = request.GET.get('status', '')
    bet_type_filter = request.GET.get('bet_type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Build queryset
    bets = BetHistory.objects.filter(user=user)

    if status_filter:
        bets = bets.filter(status=status_filter)
    if bet_type_filter:
        bets = bets.filter(bet_type=bet_type_filter)
    if date_from:
        from datetime import datetime
        bets = bets.filter(placed_at__date__gte=datetime.strptime(date_from, '%Y-%m-%d').date())
    if date_to:
        from datetime import datetime
        bets = bets.filter(placed_at__date__lte=datetime.strptime(date_to, '%Y-%m-%d').date())

    # Pagination
    paginator = Paginator(bets, 20)  # 20 bets per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate statistics
    total_bets = BetHistory.objects.filter(user=user).count()
    total_stakes = BetHistory.objects.filter(user=user).aggregate(
        total=models.Sum('stake_amount')
    )['total'] or 0
    total_winnings = BetHistory.objects.filter(user=user, status='won').aggregate(
        total=models.Sum('actual_win')
    )['total'] or 0

    won_bets = BetHistory.objects.filter(user=user, status='won').count()
    win_rate = (won_bets / total_bets * 100) if total_bets > 0 else 0

    context = {
        'user': user,
        'page_obj': page_obj,
        'status_filter': status_filter,
        'bet_type_filter': bet_type_filter,
        'date_from': date_from,
        'date_to': date_to,
        'stats': {
            'total_bets': total_bets,
            'total_stakes': total_stakes,
            'total_winnings': total_winnings,
            'net_profit': total_winnings - total_stakes,
            'win_rate': round(win_rate, 1),
        },
        'bet_status_choices': BetHistory.BET_STATUS_CHOICES,
        'bet_type_choices': BetHistory.BET_TYPE_CHOICES,
    }

    return render(request, 'accounts/betting_history.html', context)


@login_required
def bet_detail_view(request, bet_id):
    """
    Display detailed view of a specific bet
    """
    user = request.user

    try:
        bet = BetHistory.objects.get(bet_id=bet_id, user=user)
    except BetHistory.DoesNotExist:
        messages.error(request, 'Bet not found.')
        return redirect('accounts:betting_history')

    context = {
        'user': user,
        'bet': bet,
    }

    return render(request, 'accounts/bet_detail.html', context)


@login_required
def transaction_history_view(request):
    """
    Display user's transaction history
    """
    user = request.user

    # Get transaction history with pagination
    from django.core.paginator import Paginator

    # Filter parameters
    transaction_type_filter = request.GET.get('transaction_type', '')
    status_filter = request.GET.get('status', '')
    payment_method_filter = request.GET.get('payment_method', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Build queryset
    transactions = Transaction.objects.filter(user=user)

    if transaction_type_filter:
        transactions = transactions.filter(transaction_type=transaction_type_filter)
    if status_filter:
        transactions = transactions.filter(status=status_filter)
    if payment_method_filter:
        transactions = transactions.filter(payment_method=payment_method_filter)
    if date_from:
        from datetime import datetime
        transactions = transactions.filter(created_at__date__gte=datetime.strptime(date_from, '%Y-%m-%d').date())
    if date_to:
        from datetime import datetime
        transactions = transactions.filter(created_at__date__lte=datetime.strptime(date_to, '%Y-%m-%d').date())

    # Pagination
    paginator = Paginator(transactions, 25)  # 25 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate statistics
    total_deposits = Transaction.objects.filter(
        user=user,
        transaction_type='deposit',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    total_withdrawals = Transaction.objects.filter(
        user=user,
        transaction_type='withdrawal',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    total_bets = Transaction.objects.filter(
        user=user,
        transaction_type='bet_stake',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    total_winnings = Transaction.objects.filter(
        user=user,
        transaction_type='bet_win',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    # Get current balance (this would normally come from a wallet model)
    current_balance = total_deposits + total_winnings - total_withdrawals - total_bets

    context = {
        'user': user,
        'page_obj': page_obj,
        'transaction_type_filter': transaction_type_filter,
        'status_filter': status_filter,
        'payment_method_filter': payment_method_filter,
        'date_from': date_from,
        'date_to': date_to,
        'stats': {
            'total_deposits': total_deposits,
            'total_withdrawals': total_withdrawals,
            'total_bets': total_bets,
            'total_winnings': total_winnings,
            'current_balance': current_balance,
            'net_deposits': total_deposits - total_withdrawals,
        },
        'transaction_type_choices': Transaction.TRANSACTION_TYPE_CHOICES,
        'status_choices': Transaction.TRANSACTION_STATUS_CHOICES,
        'payment_method_choices': Transaction.PAYMENT_METHOD_CHOICES,
    }

    return render(request, 'accounts/transaction_history.html', context)


@login_required
def account_balance_view(request):
    """
    Display user's account balance and wallet information
    """
    user = request.user

    # Calculate current balance
    total_deposits = Transaction.objects.filter(
        user=user,
        transaction_type='deposit',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    total_withdrawals = Transaction.objects.filter(
        user=user,
        transaction_type='withdrawal',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    total_bets = Transaction.objects.filter(
        user=user,
        transaction_type='bet_stake',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    total_winnings = Transaction.objects.filter(
        user=user,
        transaction_type='bet_win',
        status='completed'
    ).aggregate(total=models.Sum('amount'))['total'] or 0

    current_balance = total_deposits + total_winnings - total_withdrawals - total_bets

    # Get recent transactions
    recent_transactions = Transaction.objects.filter(user=user)[:10]

    # Get pending transactions
    pending_transactions = Transaction.objects.filter(user=user, status='pending')

    context = {
        'user': user,
        'current_balance': current_balance,
        'total_deposits': total_deposits,
        'total_withdrawals': total_withdrawals,
        'total_bets': total_bets,
        'total_winnings': total_winnings,
        'recent_transactions': recent_transactions,
        'pending_transactions': pending_transactions,
    }

    return render(request, 'accounts/account_balance.html', context)


@login_required
def responsible_gambling_view(request):
    """
    Manage responsible gambling settings
    """
    user = request.user

    # Get or create responsible gambling settings
    try:
        rg_settings = user.responsible_gambling
    except ResponsibleGambling.DoesNotExist:
        rg_settings = ResponsibleGambling.objects.create(user=user)

    if request.method == 'POST':
        form = ResponsibleGamblingForm(request.POST, instance=rg_settings)

        if form.is_valid():
            form.save()

            # Log security event
            log_security_event(user, 'responsible_gambling_updated', request)

            messages.success(request, 'Responsible gambling settings updated successfully!')
            return redirect('accounts:responsible_gambling')
    else:
        form = ResponsibleGamblingForm(instance=rg_settings)

    context = {
        'user': user,
        'rg_settings': rg_settings,
        'form': form,
    }

    return render(request, 'accounts/responsible_gambling.html', context)


@login_required
def self_exclusion_view(request):
    """
    Handle self-exclusion requests
    """
    user = request.user

    # Get or create responsible gambling settings
    try:
        rg_settings = user.responsible_gambling
    except ResponsibleGambling.DoesNotExist:
        rg_settings = ResponsibleGambling.objects.create(user=user)

    # Check if already self-excluded
    if rg_settings.is_currently_excluded:
        context = {
            'user': user,
            'rg_settings': rg_settings,
            'already_excluded': True,
        }
        return render(request, 'accounts/self_exclusion.html', context)

    if request.method == 'POST':
        form = SelfExclusionForm(user, request.POST, instance=rg_settings)

        if form.is_valid():
            # Calculate exclusion end date
            from django.utils import timezone
            from datetime import timedelta

            period = form.cleaned_data['self_exclusion_period']
            start_time = timezone.now()

            # Set exclusion period
            if period == '24h':
                end_time = start_time + timedelta(hours=24)
            elif period == '48h':
                end_time = start_time + timedelta(hours=48)
            elif period == '1w':
                end_time = start_time + timedelta(weeks=1)
            elif period == '1m':
                end_time = start_time + timedelta(days=30)
            elif period == '3m':
                end_time = start_time + timedelta(days=90)
            elif period == '6m':
                end_time = start_time + timedelta(days=180)
            elif period == '1y':
                end_time = start_time + timedelta(days=365)
            else:  # permanent
                end_time = None

            # Update settings
            rg_settings.is_self_excluded = True
            rg_settings.self_exclusion_start = start_time
            rg_settings.self_exclusion_end = end_time
            rg_settings.self_exclusion_reason = form.cleaned_data['self_exclusion_reason']
            rg_settings.save()

            # Log security event
            log_security_event(user, 'self_exclusion_activated', request, {
                'period': period,
                'end_time': end_time.isoformat() if end_time else 'permanent'
            })

            messages.success(request, 'Self-exclusion has been activated successfully.')
            return redirect('accounts:self_exclusion')
    else:
        form = SelfExclusionForm(user, instance=rg_settings)

    context = {
        'user': user,
        'rg_settings': rg_settings,
        'form': form,
        'already_excluded': False,
    }

    return render(request, 'accounts/self_exclusion.html', context)


@login_required
def account_closure_view(request):
    """
    Handle account closure requests
    """
    user = request.user

    if request.method == 'POST':
        form = AccountClosureForm(user, request.POST)

        if form.is_valid():
            # Create closure request (in a real app, this would be reviewed by staff)
            closure_reason = form.cleaned_data['closure_reason']
            additional_comments = form.cleaned_data['additional_comments']

            # Log security event
            log_security_event(user, 'account_closure_requested', request, {
                'reason': closure_reason,
                'comments': additional_comments
            })

            # In a real implementation, you would:
            # 1. Create a closure request record
            # 2. Send notification to staff
            # 3. Set account to pending closure status
            # 4. Send confirmation email to user

            messages.success(request,
                'Account closure request submitted successfully. '
                'Our team will review your request and contact you within 24 hours.'
            )
            return redirect('accounts:dashboard')
    else:
        form = AccountClosureForm(user)

    context = {
        'user': user,
        'form': form,
    }

    return render(request, 'accounts/account_closure.html', context)


@login_required
def kyc_verification_view(request):
    """
    KYC document upload and verification status
    """
    user = request.user

    # Get user's KYC documents
    kyc_documents = KYCDocument.objects.filter(user=user).order_by('-uploaded_at')

    # Check KYC status
    required_documents = ['national_id', 'utility_bill', 'selfie']
    uploaded_documents = {doc.document_type for doc in kyc_documents}
    approved_documents = {doc.document_type for doc in kyc_documents if doc.status == 'approved'}

    missing_documents = set(required_documents) - uploaded_documents
    pending_documents = {doc.document_type for doc in kyc_documents if doc.status == 'pending'}
    rejected_documents = {doc.document_type for doc in kyc_documents if doc.status == 'rejected'}

    # Calculate KYC completion status
    kyc_complete = len(approved_documents) >= len(required_documents)
    kyc_pending = len(pending_documents) > 0

    context = {
        'user': user,
        'kyc_documents': kyc_documents,
        'required_documents': required_documents,
        'missing_documents': missing_documents,
        'pending_documents': pending_documents,
        'rejected_documents': rejected_documents,
        'approved_documents': approved_documents,
        'kyc_complete': kyc_complete,
        'kyc_pending': kyc_pending,
    }

    return render(request, 'accounts/kyc_verification.html', context)


@login_required
def kyc_document_upload_view(request, document_type=None):
    """
    Upload KYC document
    """
    user = request.user

    # Check if document type is valid
    valid_types = [choice[0] for choice in KYCDocument.DOCUMENT_TYPES]
    if document_type and document_type not in valid_types:
        messages.error(request, 'Invalid document type.')
        return redirect('accounts:kyc_verification')

    # Check if document already exists
    existing_document = None
    if document_type:
        try:
            existing_document = KYCDocument.objects.get(user=user, document_type=document_type)
            if existing_document.status == 'approved':
                messages.info(request, 'This document has already been approved.')
                return redirect('accounts:kyc_verification')
        except KYCDocument.DoesNotExist:
            pass

    if request.method == 'POST':
        if existing_document:
            # Update existing document
            form = KYCDocumentUploadForm(request.POST, request.FILES, instance=existing_document)
        else:
            # Create new document
            form = KYCDocumentUploadForm(request.POST, request.FILES)

        if form.is_valid():
            document = form.save(commit=False)
            document.user = user
            document.status = 'pending'  # Reset status to pending
            document.verified_by = None
            document.verified_at = None
            document.verification_notes = ''

            if document_type:
                document.document_type = document_type

            document.save()

            # Log security event
            log_security_event(user, 'kyc_document_uploaded', request, {
                'document_type': document.document_type,
                'document_id': document.id
            })

            messages.success(request, f'{document.get_document_type_display()} uploaded successfully!')
            return redirect('accounts:kyc_verification')
    else:
        if existing_document:
            form = KYCDocumentUploadForm(instance=existing_document)
        else:
            form = KYCDocumentUploadForm()
            if document_type:
                form.initial['document_type'] = document_type

    context = {
        'user': user,
        'form': form,
        'document_type': document_type,
        'existing_document': existing_document,
        'document_types': KYCDocument.DOCUMENT_TYPES,
    }

    return render(request, 'accounts/kyc_document_upload.html', context)


@login_required
def kyc_document_delete_view(request, document_id):
    """
    Delete KYC document (only if not approved)
    """
    user = request.user

    try:
        document = KYCDocument.objects.get(id=document_id, user=user)

        if document.status == 'approved':
            messages.error(request, 'Cannot delete approved documents.')
        else:
            document_type = document.get_document_type_display()
            document.delete()

            # Log security event
            log_security_event(user, 'kyc_document_deleted', request, {
                'document_type': document.document_type,
                'document_id': document_id
            })

            messages.success(request, f'{document_type} deleted successfully.')

    except KYCDocument.DoesNotExist:
        messages.error(request, 'Document not found.')

    return redirect('accounts:kyc_verification')


@login_required
def betting_history_view(request):
    """
    Display user's betting history
    """
    user = request.user

    # Get betting history with pagination
    from django.core.paginator import Paginator
    from .models import BetHistory

    # Filter parameters
    status_filter = request.GET.get('status', '')
    bet_type_filter = request.GET.get('bet_type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Build queryset
    bets = BetHistory.objects.filter(user=user)

    if status_filter:
        bets = bets.filter(status=status_filter)
    if bet_type_filter:
        bets = bets.filter(bet_type=bet_type_filter)
    if date_from:
        from datetime import datetime
        bets = bets.filter(placed_at__date__gte=datetime.strptime(date_from, '%Y-%m-%d').date())
    if date_to:
        from datetime import datetime
        bets = bets.filter(placed_at__date__lte=datetime.strptime(date_to, '%Y-%m-%d').date())

    # Pagination
    paginator = Paginator(bets, 20)  # 20 bets per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate statistics
    total_bets = BetHistory.objects.filter(user=user).count()
    total_stakes = BetHistory.objects.filter(user=user).aggregate(
        total=models.Sum('stake_amount')
    )['total'] or 0
    total_winnings = BetHistory.objects.filter(user=user, status='won').aggregate(
        total=models.Sum('actual_win')
    )['total'] or 0

    won_bets = BetHistory.objects.filter(user=user, status='won').count()
    win_rate = (won_bets / total_bets * 100) if total_bets > 0 else 0

    context = {
        'user': user,
        'page_obj': page_obj,
        'status_filter': status_filter,
        'bet_type_filter': bet_type_filter,
        'date_from': date_from,
        'date_to': date_to,
        'stats': {
            'total_bets': total_bets,
            'total_stakes': total_stakes,
            'total_winnings': total_winnings,
            'net_profit': total_winnings - total_stakes,
            'win_rate': round(win_rate, 1),
        },
        'bet_status_choices': BetHistory.BET_STATUS_CHOICES,
        'bet_type_choices': BetHistory.BET_TYPE_CHOICES,
    }

    return render(request, 'accounts/betting_history.html', context)


@login_required
def bet_detail_view(request, bet_id):
    """
    Display detailed view of a specific bet
    """
    user = request.user

    try:
        from .models import BetHistory
        bet = BetHistory.objects.get(bet_id=bet_id, user=user)
    except BetHistory.DoesNotExist:
        messages.error(request, 'Bet not found.')
        return redirect('accounts:betting_history')

    context = {
        'user': user,
        'bet': bet,
    }

    return render(request, 'accounts/bet_detail.html', context)


@login_required
def device_management_view(request):
    """
    Manage user devices and sessions
    """
    user = request.user

    # Get user devices
    devices = UserDevice.objects.filter(user=user).order_by('-last_seen')

    # Get active sessions
    active_sessions = UserSession.objects.filter(user=user, is_active=True)

    # Get current device
    current_device = track_user_device(user, request)

    context = {
        'user': user,
        'devices': devices,
        'active_sessions': active_sessions,
        'current_device': current_device,
    }

    return render(request, 'accounts/device_management.html', context)


@login_required
def device_trust_toggle_view(request, device_id):
    """
    Toggle device trust status
    """
    user = request.user

    try:
        device = UserDevice.objects.get(id=device_id, user=user)
        device.is_trusted = not device.is_trusted
        device.save()

        # Log security event
        log_security_event(user, 'device_trust_changed', request, {
            'device_id': device_id,
            'device_name': device.device_name,
            'is_trusted': device.is_trusted
        })

        status = 'trusted' if device.is_trusted else 'untrusted'
        messages.success(request, f'Device marked as {status}.')

    except UserDevice.DoesNotExist:
        messages.error(request, 'Device not found.')

    return redirect('accounts:device_management')


@login_required
def session_terminate_view(request, session_id):
    """
    Terminate a specific session
    """
    user = request.user

    try:
        session = UserSession.objects.get(id=session_id, user=user)

        # Don't allow terminating current session
        if session.session_key == request.session.session_key:
            messages.error(request, 'Cannot terminate the current session.')
            return redirect('accounts:device_management')

        # Terminate session
        session.is_active = False
        session.save()

        # Delete Django session
        from django.contrib.sessions.models import Session
        try:
            Session.objects.get(session_key=session.session_key).delete()
        except Session.DoesNotExist:
            pass

        # Log security event
        log_security_event(user, 'session_terminated', request, {
            'terminated_session_id': session_id,
            'ip_address': session.ip_address
        })

        messages.success(request, 'Session terminated successfully.')

    except UserSession.DoesNotExist:
        messages.error(request, 'Session not found.')

    return redirect('accounts:device_management')
