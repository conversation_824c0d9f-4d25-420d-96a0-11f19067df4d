{% extends 'base.html' %}
{% load static %}

{% block title %}{{ game.name }} - ZBet Casino{% endblock %}

{% block extra_css %}
<style>
.game-detail-container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.breadcrumb {
    background: rgba(30, 30, 46, 0.8);
    padding: 15px 0;
    margin-bottom: 20px;
}

.breadcrumb-nav {
    color: #aaa;
    font-size: 0.9rem;
}

.breadcrumb-nav a {
    color: #ffd700;
    text-decoration: none;
}

.game-hero {
    background: #1e1e2e;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.game-hero-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    align-items: start;
}

.game-image-container {
    position: relative;
}

.game-image {
    width: 100%;
    height: 200px;
    border-radius: 15px;
    object-fit: cover;
    border: 2px solid #333;
}

.game-placeholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffd700;
    font-size: 4rem;
    border: 2px solid #333;
}

.game-badges {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.badge-featured { background: #ffd700; color: #1a1a2e; }
.badge-new { background: #00ff88; color: #1a1a2e; }
.badge-popular { background: #ff6b6b; color: white; }
.badge-jackpot { background: #ff9500; color: white; }

.game-info {
    color: white;
}

.game-title {
    font-size: 2.5rem;
    color: #ffd700;
    margin-bottom: 10px;
    font-weight: bold;
}

.game-provider {
    font-size: 1.1rem;
    color: #aaa;
    margin-bottom: 15px;
}

.game-description {
    font-size: 1rem;
    line-height: 1.6;
    color: #ccc;
    margin-bottom: 20px;
}

.game-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-card {
    background: #2a2a3e;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #444;
}

.stat-label {
    color: #aaa;
    font-size: 0.8rem;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.stat-value {
    color: #ffd700;
    font-size: 1.2rem;
    font-weight: bold;
}

.game-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 25px;
}

.feature-tag {
    background: rgba(255, 215, 0, 0.1);
    color: #ffd700;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid #ffd700;
}

.play-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.btn-play {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    min-width: 150px;
}

.btn-play-real {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
}

.btn-play-real:hover {
    background: linear-gradient(45deg, #ffed4e, #fff59d);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.btn-play-demo {
    background: transparent;
    color: #ffd700;
    border: 2px solid #ffd700;
}

.btn-play-demo:hover {
    background: #ffd700;
    color: #1a1a2e;
    transform: translateY(-3px);
}

.jackpots-section {
    background: #1e1e2e;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.section-title {
    color: #ffd700;
    font-size: 1.5rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.jackpot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.jackpot-card {
    background: #2a2a3e;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #444;
    position: relative;
    overflow: hidden;
}

.jackpot-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.jackpot-name {
    color: #ffd700;
    font-weight: bold;
    margin-bottom: 10px;
}

.jackpot-amount {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.jackpot-type {
    color: #aaa;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.related-games {
    background: #1e1e2e;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #333;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.related-game-card {
    background: #2a2a3e;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease;
    border: 1px solid #444;
}

.related-game-card:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
}

.related-game-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.related-game-placeholder {
    width: 100%;
    height: 120px;
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffd700;
    font-size: 2rem;
}

.related-game-info {
    padding: 15px;
}

.related-game-title {
    color: white;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.related-game-provider {
    color: #aaa;
    font-size: 0.8rem;
    margin-bottom: 10px;
}

.related-game-rtp {
    color: #ffd700;
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .game-hero-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    .play-buttons {
        flex-direction: column;
    }
    
    .btn-play {
        width: 100%;
    }
    
    .game-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="game-detail-container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <div class="container">
            <nav class="breadcrumb-nav">
                <a href="{% url 'casino:home' %}">Casino</a> 
                <span class="mx-2">›</span> 
                <a href="{% url 'casino:games_by_category' game.category.slug %}">{{ game.category.name }}</a>
                <span class="mx-2">›</span> 
                <span>{{ game.name }}</span>
            </nav>
        </div>
    </div>

    <div class="container">
        <!-- Game Hero Section -->
        <div class="game-hero">
            <div class="game-hero-content">
                <div class="game-image-container">
                    {% if game.thumbnail %}
                    <img src="{{ game.thumbnail.url }}" alt="{{ game.name }}" class="game-image">
                    {% else %}
                    <div class="game-placeholder">
                        {% if game.game_type == 'SLOT' %}🎰
                        {% elif game.game_type == 'BLACKJACK' %}🃏
                        {% elif game.game_type == 'ROULETTE' %}🎯
                        {% elif game.game_type == 'AVIATOR' %}✈️
                        {% else %}🎮
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <div class="game-badges">
                        {% if game.is_featured %}
                        <span class="badge badge-featured">Featured</span>
                        {% endif %}
                        {% if game.is_new %}
                        <span class="badge badge-new">New</span>
                        {% endif %}
                        {% if game.is_popular %}
                        <span class="badge badge-popular">Popular</span>
                        {% endif %}
                        {% if game.has_progressive_jackpot %}
                        <span class="badge badge-jackpot">Jackpot</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="game-info">
                    <h1 class="game-title">{{ game.name }}</h1>
                    <div class="game-provider">by {{ game.provider.name }}</div>
                    
                    {% if game.description %}
                    <div class="game-description">{{ game.description }}</div>
                    {% endif %}
                    
                    <!-- Game Statistics -->
                    <div class="game-stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">RTP</div>
                            <div class="stat-value">{{ game.rtp_percentage }}%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Volatility</div>
                            <div class="stat-value">{{ game.volatility }}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Min Bet</div>
                            <div class="stat-value">KES {{ game.min_bet }}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Max Bet</div>
                            <div class="stat-value">KES {{ game.max_bet }}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Plays</div>
                            <div class="stat-value">{{ game.play_count }}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Type</div>
                            <div class="stat-value">{{ game.get_game_type_display }}</div>
                        </div>
                    </div>
                    
                    <!-- Game Features -->
                    <div class="game-features">
                        {% if game.has_free_spins %}
                        <span class="feature-tag">🎁 Free Spins</span>
                        {% endif %}
                        {% if game.has_bonus_rounds %}
                        <span class="feature-tag">🎯 Bonus Rounds</span>
                        {% endif %}
                        {% if game.has_progressive_jackpot %}
                        <span class="feature-tag">🏆 Progressive Jackpot</span>
                        {% endif %}
                        {% if game.has_demo_mode %}
                        <span class="feature-tag">🎮 Demo Mode</span>
                        {% endif %}
                    </div>
                    
                    <!-- Play Buttons -->
                    <div class="play-buttons">
                        <a href="{% url 'casino:launch_game' game.slug %}" class="btn-play btn-play-real">
                            🎰 Play for Real Money
                        </a>
                        {% if game.has_demo_mode %}
                        <a href="{% url 'casino:launch_game' game.slug %}?demo=true" class="btn-play btn-play-demo">
                            🎮 Try Demo
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Jackpots Section -->
        {% if jackpots %}
        <div class="jackpots-section">
            <h2 class="section-title">🏆 Available Jackpots</h2>
            <div class="jackpot-grid">
                {% for jackpot in jackpots %}
                <div class="jackpot-card">
                    <div class="jackpot-name">{{ jackpot.name }}</div>
                    <div class="jackpot-amount">KES {{ jackpot.current_amount|floatformat:2 }}</div>
                    <div class="jackpot-type">{{ jackpot.get_jackpot_type_display }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Related Games -->
        {% if related_games %}
        <div class="related-games">
            <h2 class="section-title">🎮 More {{ game.category.name }} Games</h2>
            <div class="related-grid">
                {% for related_game in related_games %}
                <a href="{% url 'casino:game_detail' related_game.slug %}" class="related-game-card">
                    {% if related_game.thumbnail %}
                    <img src="{{ related_game.thumbnail.url }}" alt="{{ related_game.name }}" class="related-game-image">
                    {% else %}
                    <div class="related-game-placeholder">
                        {% if related_game.game_type == 'SLOT' %}🎰
                        {% elif related_game.game_type == 'BLACKJACK' %}🃏
                        {% elif related_game.game_type == 'ROULETTE' %}🎯
                        {% elif related_game.game_type == 'AVIATOR' %}✈️
                        {% else %}🎮
                        {% endif %}
                    </div>
                    {% endif %}
                    <div class="related-game-info">
                        <div class="related-game-title">{{ related_game.name }}</div>
                        <div class="related-game-provider">{{ related_game.provider.name }}</div>
                        <div class="related-game-rtp">RTP: {{ related_game.rtp_percentage }}%</div>
                    </div>
                </a>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
