{% extends 'base.html' %}

{% block title %}Verify SMS - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Enter SMS Code</h3>
                <p class="mb-0">Check your phone for the verification code</p>
            </div>
            
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="fas fa-sms fa-3x text-primary mb-3"></i>
                    <h5>Check Your SMS</h5>
                    <p class="text-muted">
                        We've sent a 6-digit verification code to:<br>
                        <strong>{{ user.phone_number }}</strong>
                    </p>
                </div>
                
                <form method="post" id="smsVerificationForm">
                    {% csrf_token %}
                    
                    <div class="form-floating mb-3">
                        {{ form.verification_code }}
                        <label for="{{ form.verification_code.id_for_label }}">Verification Code</label>
                        <div class="form-text">{{ form.verification_code.help_text }}</div>
                        {% if form.verification_code.errors %}
                            <div class="text-danger small">{{ form.verification_code.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Verify Phone
                        </button>
                    </div>
                </form>
                
                <div class="text-center">
                    <p class="text-muted">Didn't receive the code?</p>
                    <button type="button" class="btn btn-outline-secondary" id="resendSMSBtn">
                        Resend Code
                    </button>
                </div>
                
                <div class="mt-4">
                    <small class="text-muted">
                        The verification code will expire in 10 minutes.<br>
                        SMS charges may apply according to your mobile plan.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on verification code input
    $('#{{ form.verification_code.id_for_label }}').focus();
    
    // Format verification code input
    $('#{{ form.verification_code.id_for_label }}').on('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
        if (this.value.length === 6) {
            $('#smsVerificationForm').submit();
        }
    });
    
    // Resend SMS verification code
    $('#resendSMSBtn').on('click', function() {
        const btn = $(this);
        btn.prop('disabled', true).text('Sending...');
        
        $.post('{% url "accounts:resend_sms_verification" %}', {
            verification_type: 'verification',
            csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
        }, function(data) {
            if (data.success) {
                alert('Verification code sent successfully!');
                // Start countdown
                let countdown = 60;
                const interval = setInterval(function() {
                    btn.text('Resend Code (' + countdown + 's)');
                    countdown--;
                    if (countdown < 0) {
                        clearInterval(interval);
                        btn.prop('disabled', false).text('Resend Code');
                    }
                }, 1000);
            } else {
                alert('Failed to send verification code: ' + data.error);
                btn.prop('disabled', false).text('Resend Code');
            }
        }).fail(function() {
            alert('An error occurred. Please try again.');
            btn.prop('disabled', false).text('Resend Code');
        });
    });
});
</script>
{% endblock %}
