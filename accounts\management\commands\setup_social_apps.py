"""
Management command to set up social applications for OAuth2
"""

from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site
from allauth.socialaccount.models import SocialApp
from django.conf import settings


class Command(BaseCommand):
    help = 'Set up social applications for OAuth2 authentication'

    def handle(self, *args, **options):
        """
        Create or update social applications
        """
        # Get the default site
        try:
            site = Site.objects.get(pk=1)
        except Site.DoesNotExist:
            site = Site.objects.create(
                pk=1,
                domain='localhost:8000',
                name='ZBet Development'
            )
            self.stdout.write(
                self.style.SUCCESS(f'Created default site: {site.domain}')
            )

        # Set up Google OAuth2
        if hasattr(settings, 'GOOGLE_OAUTH2_CLIENT_ID') and settings.GOOGLE_OAUTH2_CLIENT_ID:
            google_app, created = SocialApp.objects.get_or_create(
                provider='google',
                defaults={
                    'name': 'Google OAuth2',
                    'client_id': settings.GOOGLE_OAUTH2_CLIENT_ID,
                    'secret': settings.GOOGLE_OAUTH2_CLIENT_SECRET,
                }
            )
            
            if not created:
                # Update existing app
                google_app.client_id = settings.GOOGLE_OAUTH2_CLIENT_ID
                google_app.secret = settings.GOOGLE_OAUTH2_CLIENT_SECRET
                google_app.save()
            
            # Add site to the app
            google_app.sites.add(site)
            
            action = 'Created' if created else 'Updated'
            self.stdout.write(
                self.style.SUCCESS(f'{action} Google OAuth2 application')
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Google OAuth2 credentials not found in settings. '
                    'Set GOOGLE_OAUTH2_CLIENT_ID and GOOGLE_OAUTH2_CLIENT_SECRET.'
                )
            )

        # Set up Facebook OAuth2
        if hasattr(settings, 'FACEBOOK_APP_ID') and settings.FACEBOOK_APP_ID:
            facebook_app, created = SocialApp.objects.get_or_create(
                provider='facebook',
                defaults={
                    'name': 'Facebook OAuth2',
                    'client_id': settings.FACEBOOK_APP_ID,
                    'secret': settings.FACEBOOK_APP_SECRET,
                }
            )
            
            if not created:
                # Update existing app
                facebook_app.client_id = settings.FACEBOOK_APP_ID
                facebook_app.secret = settings.FACEBOOK_APP_SECRET
                facebook_app.save()
            
            # Add site to the app
            facebook_app.sites.add(site)
            
            action = 'Created' if created else 'Updated'
            self.stdout.write(
                self.style.SUCCESS(f'{action} Facebook OAuth2 application')
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Facebook OAuth2 credentials not found in settings. '
                    'Set FACEBOOK_APP_ID and FACEBOOK_APP_SECRET.'
                )
            )

        self.stdout.write(
            self.style.SUCCESS('\nSocial applications setup complete!')
        )
        
        self.stdout.write(
            self.style.WARNING(
                '\nTo complete the setup:\n'
                '1. Set up OAuth2 applications in Google and Facebook developer consoles\n'
                '2. Add the credentials to your environment variables\n'
                '3. Run this command again to update the applications\n'
                '4. Make sure your redirect URIs are configured correctly:\n'
                f'   - Google: http://{site.domain}/accounts/google/login/callback/\n'
                f'   - Facebook: http://{site.domain}/accounts/facebook/login/callback/'
            )
        )
