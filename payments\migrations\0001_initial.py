# Generated by Django 5.2.4 on 2025-07-07 09:57

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50)),
                ("code", models.CharField(max_length=20, unique=True)),
                ("is_active", models.BooleanField(default=True)),
                ("priority", models.IntegerField(default=0)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["priority", "name"],
            },
        ),
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("DEPOSIT", "Deposit"),
                            ("WITHDRAWAL", "Withdrawal"),
                            ("BET_PLACEMENT", "Bet Placement"),
                            ("BET_WINNING", "Bet Winning"),
                            ("BONUS", "Bonus"),
                            ("REFUND", "Refund"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("PROCESSING", "Processing"),
                            ("COMPLETED", "Completed"),
                            ("FAILED", "Failed"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("reference", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "fee",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "balance_before",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "balance_after",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "external_reference",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("callback_data", models.JSONField(blank=True, null=True)),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="payments.paymentmethod",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PaymentNotification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("DEPOSIT_SUCCESS", "Deposit Successful"),
                            ("DEPOSIT_FAILED", "Deposit Failed"),
                            ("WITHDRAWAL_SUCCESS", "Withdrawal Successful"),
                            ("WITHDRAWAL_FAILED", "Withdrawal Failed"),
                            ("LOW_BALANCE", "Low Balance Alert"),
                            ("LIMIT_EXCEEDED", "Limit Exceeded"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                ("message", models.TextField()),
                ("is_read", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "transaction",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="payments.transaction",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Wallet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "balance",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="transaction",
            name="wallet",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="transactions",
                to="payments.wallet",
            ),
        ),
        migrations.CreateModel(
            name="PaymentLimit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "limit_type",
                    models.CharField(
                        choices=[
                            ("MIN_DEPOSIT", "Minimum Deposit"),
                            ("MAX_DEPOSIT", "Maximum Deposit"),
                            ("MIN_WITHDRAWAL", "Minimum Withdrawal"),
                            ("MAX_WITHDRAWAL", "Maximum Withdrawal"),
                            ("DAILY_DEPOSIT", "Daily Deposit Limit"),
                            ("DAILY_WITHDRAWAL", "Daily Withdrawal Limit"),
                        ],
                        max_length=20,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=12)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="payments.paymentmethod",
                    ),
                ),
            ],
            options={
                "unique_together": {("payment_method", "limit_type")},
            },
        ),
        migrations.CreateModel(
            name="MPesaTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        help_text="Phone number in format 254XXXXXXXXX", max_length=15
                    ),
                ),
                (
                    "mpesa_receipt_number",
                    models.CharField(blank=True, max_length=30, null=True, unique=True),
                ),
                ("checkout_request_id", models.CharField(max_length=100, unique=True)),
                ("merchant_request_id", models.CharField(max_length=100)),
                ("result_code", models.CharField(blank=True, max_length=5, null=True)),
                ("result_description", models.TextField(blank=True, null=True)),
                ("account_reference", models.CharField(blank=True, max_length=50)),
                ("transaction_desc", models.CharField(blank=True, max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "transaction",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mpesa_transaction",
                        to="payments.transaction",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["checkout_request_id"],
                        name="payments_mp_checkou_a6f836_idx",
                    ),
                    models.Index(
                        fields=["mpesa_receipt_number"],
                        name="payments_mp_mpesa_r_3e414a_idx",
                    ),
                    models.Index(
                        fields=["phone_number", "-created_at"],
                        name="payments_mp_phone_n_f0a6be_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="UserPaymentStats",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                (
                    "total_deposits",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "total_withdrawals",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("deposit_count", models.IntegerField(default=0)),
                ("withdrawal_count", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["user", "date"], name="payments_us_user_id_6e7330_idx"
                    )
                ],
                "unique_together": {("user", "date")},
            },
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["user", "-created_at"], name="payments_tr_user_id_d7ba42_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["status", "-created_at"], name="payments_tr_status_7127e3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="transaction",
            index=models.Index(
                fields=["reference"], name="payments_tr_referen_530efe_idx"
            ),
        ),
    ]
