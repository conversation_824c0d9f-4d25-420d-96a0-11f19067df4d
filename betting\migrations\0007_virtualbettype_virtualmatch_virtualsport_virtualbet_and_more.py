# Generated by Django 5.2.4 on 2025-07-05 21:16

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("betting", "0006_livebetnotificationtemplate_livebetnotification_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="VirtualBetType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "min_odds",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1.01"), max_digits=10
                    ),
                ),
                (
                    "max_odds",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1000.00"), max_digits=10
                    ),
                ),
                ("display_order", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "Virtual Bet Type",
                "verbose_name_plural": "Virtual Bet Types",
                "db_table": "betting_virtual_bet_type",
                "ordering": ["display_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="VirtualMatch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("match_id", models.CharField(max_length=20, unique=True)),
                ("start_time", models.DateTimeField()),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("live", "Live"),
                            ("finished", "Finished"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                ("home_score", models.PositiveIntegerField(blank=True, null=True)),
                ("away_score", models.PositiveIntegerField(blank=True, null=True)),
                ("events", models.JSONField(blank=True, default=list)),
                ("algorithm_seed", models.CharField(blank=True, max_length=50)),
                (
                    "home_win_probability",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "draw_probability",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "away_win_probability",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("betting_enabled", models.BooleanField(default=True)),
                ("total_bets", models.PositiveIntegerField(default=0)),
                (
                    "total_stake",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Virtual Match",
                "verbose_name_plural": "Virtual Matches",
                "db_table": "betting_virtual_match",
                "ordering": ["-start_time"],
            },
        ),
        migrations.CreateModel(
            name="VirtualSport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "sport_type",
                    models.CharField(
                        choices=[
                            ("football", "Virtual Football"),
                            ("horse_racing", "Virtual Horse Racing"),
                            ("greyhound_racing", "Virtual Greyhound Racing"),
                            ("basketball", "Virtual Basketball"),
                            ("tennis", "Virtual Tennis"),
                            ("motor_racing", "Virtual Motor Racing"),
                            ("cycling", "Virtual Cycling"),
                        ],
                        max_length=20,
                        unique=True,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="virtual_sports/logos/"
                    ),
                ),
                (
                    "background_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="virtual_sports/backgrounds/"
                    ),
                ),
                ("match_duration_minutes", models.PositiveIntegerField(default=90)),
                ("matches_per_day", models.PositiveIntegerField(default=48)),
                ("interval_minutes", models.PositiveIntegerField(default=30)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "min_bet_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1.00"), max_digits=10
                    ),
                ),
                (
                    "max_bet_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("10000.00"), max_digits=10
                    ),
                ),
                (
                    "randomness_factor",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.15"), max_digits=5
                    ),
                ),
                (
                    "home_advantage",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.05"), max_digits=5
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Virtual Sport",
                "verbose_name_plural": "Virtual Sports",
                "db_table": "betting_virtual_sport",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="VirtualBet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bet_id", models.CharField(max_length=20, unique=True)),
                (
                    "bet_type",
                    models.CharField(
                        choices=[
                            ("single", "Single"),
                            ("multiple", "Multiple"),
                            ("system", "System"),
                        ],
                        default="single",
                        max_length=20,
                    ),
                ),
                ("stake", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "total_odds",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1.00"), max_digits=10
                    ),
                ),
                (
                    "potential_winnings",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("active", "Active"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("void", "Void"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "actual_winnings",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="virtual_bets",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Virtual Bet",
                "verbose_name_plural": "Virtual Bets",
                "db_table": "betting_virtual_bet",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="VirtualMarket",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("suspended", "Suspended"),
                            ("settled", "Settled"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("parameters", models.JSONField(blank=True, default=dict)),
                ("winning_selection", models.CharField(blank=True, max_length=100)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bet_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="markets",
                        to="betting.virtualbettype",
                    ),
                ),
                (
                    "virtual_match",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="markets",
                        to="betting.virtualmatch",
                    ),
                ),
            ],
            options={
                "verbose_name": "Virtual Market",
                "verbose_name_plural": "Virtual Markets",
                "db_table": "betting_virtual_market",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="VirtualSelection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("selection_key", models.CharField(max_length=100)),
                ("decimal_odds", models.DecimalField(decimal_places=2, max_digits=10)),
                ("fractional_odds", models.CharField(blank=True, max_length=20)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("suspended", "Suspended"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("void", "Void"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("total_bets", models.PositiveIntegerField(default=0)),
                (
                    "total_stake",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "market",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="selections",
                        to="betting.virtualmarket",
                    ),
                ),
            ],
            options={
                "verbose_name": "Virtual Selection",
                "verbose_name_plural": "Virtual Selections",
                "db_table": "betting_virtual_selection",
                "ordering": ["decimal_odds"],
            },
        ),
        migrations.CreateModel(
            name="VirtualBetSelection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "odds_at_placement",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="selections",
                        to="betting.virtualbet",
                    ),
                ),
                (
                    "selection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bet_selections",
                        to="betting.virtualselection",
                    ),
                ),
            ],
            options={
                "verbose_name": "Virtual Bet Selection",
                "verbose_name_plural": "Virtual Bet Selections",
                "db_table": "betting_virtual_bet_selection",
            },
        ),
        migrations.AddField(
            model_name="virtualmatch",
            name="virtual_sport",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="matches",
                to="betting.virtualsport",
            ),
        ),
        migrations.AddField(
            model_name="virtualbettype",
            name="virtual_sport",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="bet_types",
                to="betting.virtualsport",
            ),
        ),
        migrations.CreateModel(
            name="VirtualSportsManager",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("auto_generate_matches", models.BooleanField(default=True)),
                ("auto_generate_odds", models.BooleanField(default=True)),
                ("auto_settle_matches", models.BooleanField(default=True)),
                ("matches_ahead_hours", models.PositiveIntegerField(default=24)),
                (
                    "odds_margin",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.05"), max_digits=5
                    ),
                ),
                (
                    "generation_status",
                    models.CharField(
                        choices=[
                            ("idle", "Idle"),
                            ("generating", "Generating"),
                            ("error", "Error"),
                        ],
                        default="idle",
                        max_length=20,
                    ),
                ),
                ("last_generation_time", models.DateTimeField(blank=True, null=True)),
                ("last_settlement_time", models.DateTimeField(blank=True, null=True)),
                ("last_error_message", models.TextField(blank=True)),
                ("total_matches_generated", models.PositiveIntegerField(default=0)),
                ("total_matches_settled", models.PositiveIntegerField(default=0)),
                ("total_bets_processed", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "virtual_sport",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="manager",
                        to="betting.virtualsport",
                    ),
                ),
            ],
            options={
                "verbose_name": "Virtual Sports Manager",
                "verbose_name_plural": "Virtual Sports Managers",
                "db_table": "betting_virtual_sports_manager",
            },
        ),
        migrations.CreateModel(
            name="VirtualTeam",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("short_name", models.CharField(max_length=10)),
                ("attack_rating", models.PositiveIntegerField(default=75)),
                ("defense_rating", models.PositiveIntegerField(default=75)),
                ("midfield_rating", models.PositiveIntegerField(default=75)),
                ("overall_rating", models.PositiveIntegerField(default=75)),
                (
                    "logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="virtual_teams/logos/"
                    ),
                ),
                ("primary_color", models.CharField(default="#000000", max_length=7)),
                ("secondary_color", models.CharField(default="#FFFFFF", max_length=7)),
                ("matches_played", models.PositiveIntegerField(default=0)),
                ("wins", models.PositiveIntegerField(default=0)),
                ("draws", models.PositiveIntegerField(default=0)),
                ("losses", models.PositiveIntegerField(default=0)),
                ("goals_for", models.PositiveIntegerField(default=0)),
                ("goals_against", models.PositiveIntegerField(default=0)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "virtual_sport",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="teams",
                        to="betting.virtualsport",
                    ),
                ),
            ],
            options={
                "verbose_name": "Virtual Team",
                "verbose_name_plural": "Virtual Teams",
                "db_table": "betting_virtual_team",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="virtualmatch",
            name="away_team",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="away_matches",
                to="betting.virtualteam",
            ),
        ),
        migrations.AddField(
            model_name="virtualmatch",
            name="home_team",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="home_matches",
                to="betting.virtualteam",
            ),
        ),
        migrations.AddIndex(
            model_name="virtualbet",
            index=models.Index(
                fields=["user", "status"], name="betting_vir_user_id_8946ef_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="virtualbet",
            index=models.Index(
                fields=["status", "created_at"], name="betting_vir_status_a94d47_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="virtualbetselection",
            unique_together={("bet", "selection")},
        ),
        migrations.AlterUniqueTogether(
            name="virtualbettype",
            unique_together={("virtual_sport", "slug")},
        ),
        migrations.AlterUniqueTogether(
            name="virtualteam",
            unique_together={("virtual_sport", "name")},
        ),
        migrations.AddIndex(
            model_name="virtualmatch",
            index=models.Index(
                fields=["virtual_sport", "status"],
                name="betting_vir_virtual_f9057f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="virtualmatch",
            index=models.Index(
                fields=["start_time", "status"], name="betting_vir_start_t_6bc14d_idx"
            ),
        ),
    ]
