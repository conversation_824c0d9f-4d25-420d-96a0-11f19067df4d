{% extends 'base.html' %}

{% block title %}Enable Two-Factor Authentication - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Enable Two-Factor Authentication</h3>
                <p class="mb-0">Secure your account with 2FA</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                    <h5>Enhance Your Security</h5>
                    <p class="text-muted">
                        Two-factor authentication adds an extra layer of security to your account.
                    </p>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Why enable 2FA?</h6>
                    <ul class="list-unstyled mb-0">
                        <li>✓ Protect your account from unauthorized access</li>
                        <li>✓ Secure your funds and betting history</li>
                        <li>✓ Meet security requirements for higher limits</li>
                        <li>✓ Peace of mind when betting online</li>
                    </ul>
                </div>
                
                <form method="post" id="enable2FAForm">
                    {% csrf_token %}
                    
                    <!-- 2FA Method Selection -->
                    <div class="mb-4">
                        <label class="form-label"><strong>Choose your 2FA method:</strong></label>
                        
                        {% for choice in form.method %}
                            <div class="card mb-2">
                                <div class="card-body">
                                    <div class="form-check">
                                        {{ choice.tag }}
                                        <label class="form-check-label" for="{{ choice.id_for_label }}">
                                            <strong>
                                                {% if choice.choice_value == 'sms' %}
                                                    <i class="fas fa-sms text-primary"></i> SMS Verification
                                                {% elif choice.choice_value == 'totp' %}
                                                    <i class="fas fa-mobile-alt text-success"></i> Authenticator App
                                                {% else %}
                                                    <i class="fas fa-shield-alt text-info"></i> Both Methods
                                                {% endif %}
                                            </strong>
                                        </label>
                                        <div class="text-muted small mt-1">
                                            {% if choice.choice_value == 'sms' %}
                                                Receive verification codes via SMS to {{ user.phone_number }}
                                            {% elif choice.choice_value == 'totp' %}
                                                Use Google Authenticator, Authy, or similar apps
                                            {% else %}
                                                Maximum security with both SMS and authenticator app
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                        
                        {% if form.method.errors %}
                            <div class="text-danger small">{{ form.method.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Password Confirmation -->
                    <div class="form-floating mb-3">
                        {{ form.password }}
                        <label for="{{ form.password.id_for_label }}">Current Password</label>
                        <div class="form-text">{{ form.password.help_text }}</div>
                        {% if form.password.errors %}
                            <div class="text-danger small">{{ form.password.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Enable Two-Factor Authentication
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:dashboard' %}" class="btn btn-outline-secondary">
                        Cancel
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Security Notice -->
        <div class="card mt-3">
            <div class="card-body text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    <strong>Important:</strong> After enabling 2FA, you'll receive backup codes that can be used if you lose access to your primary 2FA method. Store these codes in a safe place.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on password field when method is selected
    $('input[name="method"]').on('change', function() {
        $('#{{ form.password.id_for_label }}').focus();
    });
    
    // Form validation
    $('#enable2FAForm').on('submit', function(e) {
        const method = $('input[name="method"]:checked').val();
        const password = $('#{{ form.password.id_for_label }}').val();
        
        if (!method) {
            e.preventDefault();
            alert('Please select a 2FA method.');
            return false;
        }
        
        if (!password) {
            e.preventDefault();
            alert('Please enter your current password.');
            $('#{{ form.password.id_for_label }}').focus();
            return false;
        }
    });
});
</script>
{% endblock %}
