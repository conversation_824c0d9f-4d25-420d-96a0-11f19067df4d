from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal

# Import models from different apps
from accounts.models import UserProfile
from sports.models import Sport, League, Team, Event, Odds
from betting.models import Bet, BetSlip, BetSelection
from payments.models import Wallet, Transaction, Deposit, Withdrawal
from promotions.models import Promotion, Bonus, LoyaltyProgram, UserLoyaltyStatus
from casino.models import Game, GameSession

User = get_user_model()


# ===== AUTHENTICATION SERIALIZERS =====

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    phone_number = serializers.CharField(required=True)
    
    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'password_confirm', 'phone_number', 'first_name', 'last_name')
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        phone_number = validated_data.pop('phone_number')
        
        user = User.objects.create_user(**validated_data)
        
        # Create user profile
        UserProfile.objects.create(
            user=user,
            phone_number=phone_number
        )
        
        # Create wallet
        from payments.models import Wallet
        Wallet.objects.create(user=user)
        
        return user


class UserLoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include username and password')
        
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    first_name = serializers.CharField(source='user.first_name')
    last_name = serializers.CharField(source='user.last_name')
    
    class Meta:
        model = UserProfile
        fields = ('username', 'email', 'first_name', 'last_name', 'phone_number', 
                 'date_of_birth', 'country', 'city', 'avatar', 'is_verified')
        read_only_fields = ('is_verified',)
    
    def update(self, instance, validated_data):
        user_data = validated_data.pop('user', {})
        user = instance.user
        
        # Update user fields
        for attr, value in user_data.items():
            setattr(user, attr, value)
        user.save()
        
        # Update profile fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        return instance


# ===== SPORTS SERIALIZERS =====

class SportSerializer(serializers.ModelSerializer):
    events_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Sport
        fields = ('id', 'name', 'slug', 'icon', 'is_active', 'events_count')
    
    def get_events_count(self, obj):
        return obj.events.filter(status='UPCOMING').count()


class LeagueSerializer(serializers.ModelSerializer):
    sport_name = serializers.CharField(source='sport.name', read_only=True)
    events_count = serializers.SerializerMethodField()
    
    class Meta:
        model = League
        fields = ('id', 'name', 'slug', 'sport', 'sport_name', 'country', 'logo', 'is_active', 'events_count')
    
    def get_events_count(self, obj):
        return obj.events.filter(status='UPCOMING').count()


class TeamSerializer(serializers.ModelSerializer):
    sport_name = serializers.CharField(source='sport.name', read_only=True)
    
    class Meta:
        model = Team
        fields = ('id', 'name', 'slug', 'sport', 'sport_name', 'country', 'logo', 'is_active')


class OddsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Odds
        fields = ('id', 'market_type', 'selection', 'odds_value', 'is_active', 'updated_at')


class EventSerializer(serializers.ModelSerializer):
    sport_name = serializers.CharField(source='sport.name', read_only=True)
    league_name = serializers.CharField(source='league.name', read_only=True)
    home_team_name = serializers.CharField(source='home_team.name', read_only=True)
    away_team_name = serializers.CharField(source='away_team.name', read_only=True)
    odds = OddsSerializer(many=True, read_only=True)
    
    class Meta:
        model = Event
        fields = ('id', 'sport', 'sport_name', 'league', 'league_name', 
                 'home_team', 'home_team_name', 'away_team', 'away_team_name',
                 'event_date', 'status', 'home_score', 'away_score', 'odds')


class EventListSerializer(serializers.ModelSerializer):
    """Simplified serializer for event listings"""
    sport_name = serializers.CharField(source='sport.name', read_only=True)
    league_name = serializers.CharField(source='league.name', read_only=True)
    home_team_name = serializers.CharField(source='home_team.name', read_only=True)
    away_team_name = serializers.CharField(source='away_team.name', read_only=True)
    main_odds = serializers.SerializerMethodField()
    
    class Meta:
        model = Event
        fields = ('id', 'sport_name', 'league_name', 'home_team_name', 'away_team_name',
                 'event_date', 'status', 'main_odds')
    
    def get_main_odds(self, obj):
        """Get main market odds (1X2 or Home/Away)"""
        main_odds = obj.odds.filter(market_type__in=['1X2', 'MATCH_WINNER'], is_active=True)
        return OddsSerializer(main_odds, many=True).data


# ===== BETTING SERIALIZERS =====

class BetSelectionSerializer(serializers.ModelSerializer):
    event_name = serializers.SerializerMethodField()
    odds_display = serializers.CharField(source='odds.odds_value', read_only=True)
    
    class Meta:
        model = BetSelection
        fields = ('id', 'event', 'event_name', 'odds', 'odds_display', 'selection_type', 'selection_value')
    
    def get_event_name(self, obj):
        return f"{obj.event.home_team.name} vs {obj.event.away_team.name}"


class BetSerializer(serializers.ModelSerializer):
    selections = BetSelectionSerializer(many=True, read_only=True)
    potential_winnings = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = Bet
        fields = ('id', 'bet_type', 'stake_amount', 'total_odds', 'potential_winnings',
                 'status', 'placed_at', 'settled_at', 'selections')


class PlaceBetSerializer(serializers.Serializer):
    selections = serializers.ListField(
        child=serializers.DictField(
            child=serializers.CharField()
        ),
        min_length=1
    )
    stake_amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=Decimal('1.00'))
    bet_type = serializers.ChoiceField(choices=['SINGLE', 'MULTIPLE'])
    
    def validate_selections(self, value):
        """Validate bet selections"""
        for selection in value:
            if 'event_id' not in selection or 'odds_id' not in selection:
                raise serializers.ValidationError("Each selection must have event_id and odds_id")
        return value
    
    def validate_stake_amount(self, value):
        """Validate stake amount"""
        if value < Decimal('1.00'):
            raise serializers.ValidationError("Minimum stake is 1.00")
        if value > Decimal('100000.00'):
            raise serializers.ValidationError("Maximum stake is 100,000.00")
        return value


# ===== WALLET & PAYMENT SERIALIZERS =====

class WalletSerializer(serializers.ModelSerializer):
    class Meta:
        model = Wallet
        fields = ('balance', 'bonus_balance', 'total_deposits', 'total_withdrawals', 'updated_at')
        read_only_fields = ('balance', 'bonus_balance', 'total_deposits', 'total_withdrawals', 'updated_at')


class TransactionSerializer(serializers.ModelSerializer):
    transaction_type_display = serializers.CharField(source='get_transaction_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Transaction
        fields = ('id', 'transaction_type', 'transaction_type_display', 'amount', 
                 'status', 'status_display', 'description', 'created_at')


class DepositSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    
    class Meta:
        model = Deposit
        fields = ('id', 'amount', 'payment_method', 'payment_method_display', 
                 'phone_number', 'status', 'status_display', 'created_at')


class CreateDepositSerializer(serializers.Serializer):
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=Decimal('10.00'))
    phone_number = serializers.CharField(max_length=15)
    payment_method = serializers.ChoiceField(choices=['MPESA'], default='MPESA')
    
    def validate_amount(self, value):
        if value < Decimal('10.00'):
            raise serializers.ValidationError("Minimum deposit is KES 10.00")
        if value > Decimal('150000.00'):
            raise serializers.ValidationError("Maximum deposit is KES 150,000.00")
        return value
    
    def validate_phone_number(self, value):
        # Remove any non-digit characters
        phone = ''.join(filter(str.isdigit, value))
        
        # Check if it starts with 254 or 0
        if phone.startswith('254'):
            phone = phone[3:]
        elif phone.startswith('0'):
            phone = phone[1:]
        
        # Validate Kenyan mobile number format
        if not (phone.startswith('7') and len(phone) == 9):
            raise serializers.ValidationError("Invalid Kenyan mobile number format")
        
        return f"254{phone}"


class WithdrawalSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Withdrawal
        fields = ('id', 'amount', 'phone_number', 'status', 'status_display', 
                 'requested_at', 'processed_at')


class CreateWithdrawalSerializer(serializers.Serializer):
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=Decimal('50.00'))
    phone_number = serializers.CharField(max_length=15)
    
    def validate_amount(self, value):
        if value < Decimal('50.00'):
            raise serializers.ValidationError("Minimum withdrawal is KES 50.00")
        return value
    
    def validate_phone_number(self, value):
        # Same validation as deposit
        phone = ''.join(filter(str.isdigit, value))
        
        if phone.startswith('254'):
            phone = phone[3:]
        elif phone.startswith('0'):
            phone = phone[1:]
        
        if not (phone.startswith('7') and len(phone) == 9):
            raise serializers.ValidationError("Invalid Kenyan mobile number format")
        
        return f"254{phone}"


# ===== PROMOTION SERIALIZERS =====

class PromotionSerializer(serializers.ModelSerializer):
    bonus_type_name = serializers.CharField(source='bonus_type.name', read_only=True)
    days_remaining = serializers.SerializerMethodField()
    
    class Meta:
        model = Promotion
        fields = ('id', 'name', 'slug', 'description', 'short_description',
                 'promotion_type', 'bonus_type', 'bonus_type_name',
                 'percentage_value', 'fixed_amount', 'max_bonus_amount',
                 'min_deposit_amount', 'start_date', 'end_date', 'days_remaining',
                 'is_featured', 'banner_image')
    
    def get_days_remaining(self, obj):
        return obj.days_remaining


class BonusSerializer(serializers.ModelSerializer):
    bonus_type_name = serializers.CharField(source='bonus_type.name', read_only=True)
    promotion_name = serializers.CharField(source='promotion.name', read_only=True)
    wagering_progress = serializers.SerializerMethodField()
    
    class Meta:
        model = Bonus
        fields = ('id', 'reference', 'bonus_type', 'bonus_type_name',
                 'promotion', 'promotion_name', 'bonus_amount', 'remaining_amount',
                 'wagering_requirement', 'wagered_amount', 'remaining_wagering',
                 'wagering_progress', 'status', 'awarded_at', 'expires_at')
    
    def get_wagering_progress(self, obj):
        return obj.wagering_progress


class LoyaltyProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoyaltyProgram
        fields = ('id', 'name', 'tier', 'min_points_required', 'points_multiplier',
                 'cashback_percentage', 'bonus_percentage', 'free_withdrawals_per_month')


class UserLoyaltyStatusSerializer(serializers.ModelSerializer):
    current_program_name = serializers.CharField(source='current_program.name', read_only=True)
    points_to_next_tier = serializers.SerializerMethodField()
    
    class Meta:
        model = UserLoyaltyStatus
        fields = ('current_program', 'current_program_name', 'total_points',
                 'available_points', 'points_to_next_tier', 'lifetime_deposits',
                 'lifetime_wagering')
    
    def get_points_to_next_tier(self, obj):
        return obj.points_to_next_tier


# ===== CASINO SERIALIZERS =====

class GameSerializer(serializers.ModelSerializer):
    provider_name = serializers.CharField(source='provider.name', read_only=True)
    
    class Meta:
        model = Game
        fields = ('id', 'name', 'slug', 'game_type', 'provider', 'provider_name',
                 'thumbnail', 'is_active', 'min_bet', 'max_bet', 'rtp')


class GameSessionSerializer(serializers.ModelSerializer):
    game_name = serializers.CharField(source='game.name', read_only=True)
    
    class Meta:
        model = GameSession
        fields = ('id', 'game', 'game_name', 'session_token', 'bet_amount',
                 'win_amount', 'status', 'started_at', 'ended_at')
