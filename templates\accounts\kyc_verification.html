{% extends 'base.html' %}

{% block title %}KYC Verification - ZBet{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card">
                <div class="card-header zbet-primary text-white">
                    <h6 class="mb-0">Account Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'accounts:profile_update' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user"></i> Profile
                    </a>
                    <a href="{% url 'accounts:kyc_verification' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-id-card"></i> KYC Verification
                    </a>
                    <a href="{% url 'accounts:change_email' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-at"></i> Change Email
                    </a>
                    <a href="{% url 'accounts:change_phone' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-mobile-alt"></i> Change Phone
                    </a>
                    <a href="{% url 'accounts:two_factor_settings' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> Two-Factor Authentication
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>KYC Verification</h2>
                    <p class="text-muted">Verify your identity to unlock all platform features</p>
                </div>
                <div>
                    {% if kyc_complete %}
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-check-circle"></i> Verified
                        </span>
                    {% elif kyc_pending %}
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-clock"></i> Pending Review
                        </span>
                    {% else %}
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-exclamation-triangle"></i> Not Verified
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- KYC Status Overview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Verification Status</h5>
                </div>
                <div class="card-body">
                    {% if kyc_complete %}
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> Account Fully Verified</h6>
                            <p class="mb-0">Your account has been successfully verified. You can now access all platform features including deposits, withdrawals, and betting.</p>
                        </div>
                    {% elif kyc_pending %}
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-clock"></i> Verification in Progress</h6>
                            <p class="mb-0">Your documents are being reviewed. This process typically takes 24-48 hours. We'll notify you once the review is complete.</p>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-upload"></i> Verification Required</h6>
                            <p class="mb-0">Please upload the required documents to verify your account. This is required for security and regulatory compliance.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Required Documents -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list-check"></i> Required Documents</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- National ID -->
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-id-card fa-3x mb-3 
                                        {% if 'national_id' in approved_documents %}text-success
                                        {% elif 'national_id' in pending_documents %}text-warning
                                        {% elif 'national_id' in rejected_documents %}text-danger
                                        {% else %}text-muted{% endif %}"></i>
                                    <h6>National ID</h6>
                                    <p class="text-muted small">Government-issued ID card</p>
                                    
                                    {% if 'national_id' in approved_documents %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elif 'national_id' in pending_documents %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif 'national_id' in rejected_documents %}
                                        <span class="badge bg-danger">Rejected</span>
                                        <div class="mt-2">
                                            <a href="{% url 'accounts:kyc_document_upload_type' 'national_id' %}" class="btn btn-sm btn-outline-primary">
                                                Re-upload
                                            </a>
                                        </div>
                                    {% else %}
                                        <a href="{% url 'accounts:kyc_document_upload_type' 'national_id' %}" class="btn btn-sm btn-primary">
                                            Upload
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Utility Bill -->
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-invoice fa-3x mb-3 
                                        {% if 'utility_bill' in approved_documents %}text-success
                                        {% elif 'utility_bill' in pending_documents %}text-warning
                                        {% elif 'utility_bill' in rejected_documents %}text-danger
                                        {% else %}text-muted{% endif %}"></i>
                                    <h6>Utility Bill</h6>
                                    <p class="text-muted small">Proof of address (last 3 months)</p>
                                    
                                    {% if 'utility_bill' in approved_documents %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elif 'utility_bill' in pending_documents %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif 'utility_bill' in rejected_documents %}
                                        <span class="badge bg-danger">Rejected</span>
                                        <div class="mt-2">
                                            <a href="{% url 'accounts:kyc_document_upload_type' 'utility_bill' %}" class="btn btn-sm btn-outline-primary">
                                                Re-upload
                                            </a>
                                        </div>
                                    {% else %}
                                        <a href="{% url 'accounts:kyc_document_upload_type' 'utility_bill' %}" class="btn btn-sm btn-primary">
                                            Upload
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Selfie with ID -->
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-camera fa-3x mb-3 
                                        {% if 'selfie' in approved_documents %}text-success
                                        {% elif 'selfie' in pending_documents %}text-warning
                                        {% elif 'selfie' in rejected_documents %}text-danger
                                        {% else %}text-muted{% endif %}"></i>
                                    <h6>Selfie with ID</h6>
                                    <p class="text-muted small">Photo of you holding your ID</p>
                                    
                                    {% if 'selfie' in approved_documents %}
                                        <span class="badge bg-success">Approved</span>
                                    {% elif 'selfie' in pending_documents %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif 'selfie' in rejected_documents %}
                                        <span class="badge bg-danger">Rejected</span>
                                        <div class="mt-2">
                                            <a href="{% url 'accounts:kyc_document_upload_type' 'selfie' %}" class="btn btn-sm btn-outline-primary">
                                                Re-upload
                                            </a>
                                        </div>
                                    {% else %}
                                        <a href="{% url 'accounts:kyc_document_upload_type' 'selfie' %}" class="btn btn-sm btn-primary">
                                            Upload
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Uploaded Documents -->
            {% if kyc_documents %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-folder-open"></i> Uploaded Documents</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Document Type</th>
                                    <th>Document Number</th>
                                    <th>Upload Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for document in kyc_documents %}
                                <tr>
                                    <td>
                                        <i class="fas fa-file-alt"></i>
                                        {{ document.get_document_type_display }}
                                    </td>
                                    <td>{{ document.document_number|default:"-" }}</td>
                                    <td>{{ document.uploaded_at|date:"M d, Y H:i" }}</td>
                                    <td>
                                        {% if document.status == 'approved' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif document.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif document.status == 'rejected' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ document.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if document.status != 'approved' %}
                                            <a href="{% url 'accounts:kyc_document_upload_type' document.document_type %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                Replace
                                            </a>
                                            <a href="{% url 'accounts:kyc_document_delete' document.id %}" 
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('Are you sure you want to delete this document?')">
                                                Delete
                                            </a>
                                        {% else %}
                                            <span class="text-muted">Verified</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if document.status == 'rejected' and document.verification_notes %}
                                <tr>
                                    <td colspan="5">
                                        <div class="alert alert-danger mb-0">
                                            <strong>Rejection Reason:</strong> {{ document.verification_notes }}
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Upload Guidelines -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Upload Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Document Requirements:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Clear, high-quality images</li>
                                <li><i class="fas fa-check text-success"></i> All text must be readable</li>
                                <li><i class="fas fa-check text-success"></i> Documents must be valid and current</li>
                                <li><i class="fas fa-check text-success"></i> Maximum file size: 10MB</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Supported Formats:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-file-image text-primary"></i> JPEG, PNG, GIF</li>
                                <li><i class="fas fa-file-pdf text-danger"></i> PDF documents</li>
                            </ul>
                            
                            <h6 class="mt-3">Processing Time:</h6>
                            <p class="text-muted small">24-48 hours for review</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
