{% extends 'base.html' %}

{% block title %}Register - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Create Your ZBet Account</h3>
                <p class="mb-0">Join thousands of sports betting enthusiasts</p>
            </div>
            
            <div class="card-body">
                <form method="post" id="registrationForm">
                    {% csrf_token %}
                    
                    <!-- Personal Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.first_name }}
                                <label for="{{ form.first_name.id_for_label }}">First Name *</label>
                            </div>
                            {% if form.first_name.errors %}
                                <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.last_name }}
                                <label for="{{ form.last_name.id_for_label }}">Last Name *</label>
                            </div>
                            {% if form.last_name.errors %}
                                <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Username -->
                    <div class="form-floating mb-3">
                        {{ form.username }}
                        <label for="{{ form.username.id_for_label }}">Username *</label>
                        <div id="usernameStatus" class="form-text"></div>
                        {% if form.username.errors %}
                            <div class="text-danger small">{{ form.username.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Email -->
                    <div class="form-floating mb-3">
                        {{ form.email }}
                        <label for="{{ form.email.id_for_label }}">Email Address *</label>
                        <div id="emailStatus" class="form-text"></div>
                        {% if form.email.errors %}
                            <div class="text-danger small">{{ form.email.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Phone Number -->
                    <div class="form-floating mb-3">
                        {{ form.phone_number }}
                        <label for="{{ form.phone_number.id_for_label }}">Phone Number *</label>
                        <div class="form-text">{{ form.phone_number.help_text }}</div>
                        {% if form.phone_number.errors %}
                            <div class="text-danger small">{{ form.phone_number.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Date of Birth and Country -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.date_of_birth }}
                                <label for="{{ form.date_of_birth.id_for_label }}">Date of Birth *</label>
                            </div>
                            <div class="form-text">{{ form.date_of_birth.help_text }}</div>
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger small">{{ form.date_of_birth.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.country }}
                                <label for="{{ form.country.id_for_label }}">Country *</label>
                            </div>
                            {% if form.country.errors %}
                                <div class="text-danger small">{{ form.country.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- City -->
                    <div class="form-floating mb-3">
                        {{ form.city }}
                        <label for="{{ form.city.id_for_label }}">City</label>
                        {% if form.city.errors %}
                            <div class="text-danger small">{{ form.city.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Passwords -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.password1 }}
                                <label for="{{ form.password1.id_for_label }}">Password *</label>
                            </div>
                            {% if form.password1.errors %}
                                <div class="text-danger small">{{ form.password1.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.password2 }}
                                <label for="{{ form.password2.id_for_label }}">Confirm Password *</label>
                            </div>
                            {% if form.password2.errors %}
                                <div class="text-danger small">{{ form.password2.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Referral Code -->
                    <div class="form-floating mb-3">
                        {{ form.referral_code_used }}
                        <label for="{{ form.referral_code_used.id_for_label }}">Referral Code</label>
                        <div id="referralStatus" class="form-text">{{ form.referral_code_used.help_text }}</div>
                        {% if form.referral_code_used.errors %}
                            <div class="text-danger small">{{ form.referral_code_used.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Terms and Marketing -->
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.terms_accepted }}
                            <label class="form-check-label" for="{{ form.terms_accepted.id_for_label }}">
                                {{ form.terms_accepted.label }} *
                            </label>
                            {% if form.terms_accepted.errors %}
                                <div class="text-danger small">{{ form.terms_accepted.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-check">
                            {{ form.marketing_consent }}
                            <label class="form-check-label" for="{{ form.marketing_consent.id_for_label }}">
                                {{ form.marketing_consent.label }}
                            </label>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Create Account
                        </button>
                    </div>
                </form>

                <hr>

                <!-- Social Registration -->
                <div class="text-center mb-3">
                    <p class="text-muted">Or sign up with</p>

                    {% load socialaccount %}

                    <div class="d-grid gap-2">
                        <!-- Google Registration -->
                        <a href="{% provider_login_url 'google' %}" class="btn btn-outline-danger">
                            <i class="fab fa-google"></i> Continue with Google
                        </a>

                        <!-- Facebook Registration -->
                        <a href="{% provider_login_url 'facebook' %}" class="btn btn-outline-primary">
                            <i class="fab fa-facebook-f"></i> Continue with Facebook
                        </a>
                    </div>
                </div>

                <hr>

                <div class="text-center mt-3">
                    <p>Already have an account? <a href="{% url 'accounts:login' %}" class="text-decoration-none">Login here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Username availability check
    $('#{{ form.username.id_for_label }}').on('blur', function() {
        const username = $(this).val();
        if (username.length >= 3) {
            $.get('{% url "accounts:check_username" %}', {username: username}, function(data) {
                const statusDiv = $('#usernameStatus');
                if (data.available) {
                    statusDiv.html('<span class="text-success">✓ ' + data.message + '</span>');
                } else {
                    statusDiv.html('<span class="text-danger">✗ ' + data.message + '</span>');
                }
            });
        }
    });
    
    // Email availability check
    $('#{{ form.email.id_for_label }}').on('blur', function() {
        const email = $(this).val();
        if (email.includes('@')) {
            $.get('{% url "accounts:check_email" %}', {email: email}, function(data) {
                const statusDiv = $('#emailStatus');
                if (data.available) {
                    statusDiv.html('<span class="text-success">✓ ' + data.message + '</span>');
                } else {
                    statusDiv.html('<span class="text-danger">✗ ' + data.message + '</span>');
                }
            });
        }
    });
    
    // Referral code validation
    $('#{{ form.referral_code_used.id_for_label }}').on('blur', function() {
        const referralCode = $(this).val();
        if (referralCode.length > 0) {
            $.get('{% url "accounts:validate_referral" %}', {referral_code: referralCode}, function(data) {
                const statusDiv = $('#referralStatus');
                if (data.valid) {
                    statusDiv.html('<span class="text-success">✓ ' + data.message + '</span>');
                } else {
                    statusDiv.html('<span class="text-danger">✗ ' + data.message + '</span>');
                }
            });
        } else {
            $('#referralStatus').html('{{ form.referral_code_used.help_text }}');
        }
    });
});
</script>
{% endblock %}
