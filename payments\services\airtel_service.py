"""
Airtel Money API Integration Service
Handles Airtel Money payment operations including deposits and withdrawals
"""

import requests
import base64
import json
import time
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from decimal import Decimal
import logging
import uuid

logger = logging.getLogger(__name__)


class AirtelMoneyService:
    """Service for handling Airtel Money payments"""
    
    def __init__(self):
        self.environment = getattr(settings, 'AIRTEL_ENVIRONMENT', 'sandbox')
        self.client_id = getattr(settings, 'AIRTEL_CLIENT_ID', '')
        self.client_secret = getattr(settings, 'AIRTEL_CLIENT_SECRET', '')
        self.merchant_id = getattr(settings, 'AIRTEL_MERCHANT_ID', '')
        
        if self.environment == 'production':
            self.base_url = 'https://openapiuat.airtel.africa'
        else:
            self.base_url = 'https://openapiuat.airtel.africa'  # Sandbox URL
    
    def get_access_token(self):
        """Get OAuth access token for Airtel Money API"""
        cache_key = 'airtel_access_token'
        token = cache.get(cache_key)
        
        if token:
            return token
        
        try:
            url = f"{self.base_url}/auth/oauth2/token"
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*'
            }
            
            payload = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "client_credentials"
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                access_token = result.get('access_token')
                expires_in = result.get('expires_in', 3600)
                
                # Cache token for slightly less than expiry time
                cache.set(cache_key, access_token, expires_in - 60)
                
                return access_token
            else:
                logger.error(f"Failed to get Airtel access token: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting Airtel access token: {str(e)}")
            return None
    
    def format_phone_number(self, phone_number):
        """Format phone number for Airtel Money (Kenyan format)"""
        # Remove any non-digit characters
        phone = ''.join(filter(str.isdigit, str(phone_number)))
        
        # Handle different formats
        if phone.startswith('254'):
            return phone
        elif phone.startswith('0'):
            return '254' + phone[1:]
        elif len(phone) == 9:
            return '254' + phone
        else:
            return phone
    
    def initiate_payment(self, phone_number, amount, reference, description="Payment"):
        """Initiate Airtel Money payment collection"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            formatted_phone = self.format_phone_number(phone_number)
            
            url = f"{self.base_url}/merchant/v1/payments/"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'X-Country': 'KE',
                'X-Currency': 'KES'
            }
            
            payload = {
                "reference": reference,
                "subscriber": {
                    "country": "KE",
                    "currency": "KES",
                    "msisdn": formatted_phone
                },
                "transaction": {
                    "amount": str(amount),
                    "country": "KE",
                    "currency": "KES",
                    "id": reference
                }
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('status', {}).get('success'):
                    return {
                        'success': True,
                        'transaction_id': result.get('data', {}).get('transaction', {}).get('id'),
                        'reference': reference,
                        'message': 'Payment initiated successfully'
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('status', {}).get('message', 'Payment initiation failed')
                    }
            else:
                logger.error(f"Airtel payment request failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Payment request failed'
                }
                
        except Exception as e:
            logger.error(f"Error initiating Airtel payment: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def query_transaction_status(self, transaction_id):
        """Query Airtel Money transaction status"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            url = f"{self.base_url}/standard/v1/payments/{transaction_id}"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Accept': '*/*',
                'X-Country': 'KE',
                'X-Currency': 'KES'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'data': result
                }
            else:
                logger.error(f"Airtel status query failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Status query failed'
                }
                
        except Exception as e:
            logger.error(f"Error querying Airtel transaction status: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def initiate_disbursement(self, phone_number, amount, reference, description="Disbursement"):
        """Initiate Airtel Money disbursement (withdrawal)"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            formatted_phone = self.format_phone_number(phone_number)
            
            url = f"{self.base_url}/standard/v1/disbursements/"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'X-Country': 'KE',
                'X-Currency': 'KES'
            }
            
            payload = {
                "payee": {
                    "msisdn": formatted_phone
                },
                "reference": reference,
                "pin": getattr(settings, 'AIRTEL_DISBURSEMENT_PIN', ''),
                "transaction": {
                    "amount": str(amount),
                    "id": reference
                }
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('status', {}).get('success'):
                    return {
                        'success': True,
                        'transaction_id': result.get('data', {}).get('transaction', {}).get('id'),
                        'reference': reference,
                        'message': 'Disbursement initiated successfully'
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('status', {}).get('message', 'Disbursement failed')
                    }
            else:
                logger.error(f"Airtel disbursement failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Disbursement failed'
                }
                
        except Exception as e:
            logger.error(f"Error initiating Airtel disbursement: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_customer(self, phone_number):
        """Validate Airtel Money customer"""
        try:
            formatted_phone = self.format_phone_number(phone_number)
            
            # Basic phone number validation for Airtel Kenya
            if not formatted_phone.startswith('254'):
                return {'success': False, 'valid': False, 'error': 'Invalid phone number format'}
            
            if len(formatted_phone) != 12:
                return {'success': False, 'valid': False, 'error': 'Phone number must be 12 digits'}
            
            # Check if it's a valid Airtel Kenya number (starts with 2547, 2573, 2578)
            airtel_prefixes = ['2547', '2573', '2578']
            if not any(formatted_phone.startswith(prefix) for prefix in airtel_prefixes):
                return {'success': False, 'valid': False, 'error': 'Not an Airtel Kenya number'}
            
            return {
                'success': True,
                'valid': True,
                'phone_number': formatted_phone
            }
                
        except Exception as e:
            logger.error(f"Error validating Airtel customer: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def process_callback(self, callback_data):
        """Process Airtel Money callback data"""
        try:
            # Extract relevant information from callback
            transaction_id = callback_data.get('transaction', {}).get('id')
            status = callback_data.get('transaction', {}).get('status')
            amount = callback_data.get('transaction', {}).get('amount')
            currency = callback_data.get('transaction', {}).get('currency')
            
            return {
                'success': True,
                'transaction_id': transaction_id,
                'status': status,
                'amount': amount,
                'currency': currency,
                'callback_data': callback_data
            }
            
        except Exception as e:
            logger.error(f"Error processing Airtel callback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


# Create a singleton instance
airtel_service = AirtelMoneyService()
