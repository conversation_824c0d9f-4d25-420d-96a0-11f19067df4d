{% extends 'base.html' %}
{% load static %}

{% block title %}Notifications - ZBet{% endblock %}

{% block extra_css %}
<style>
    .notification-item {
        border-left: 4px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    
    .notification-item.unread {
        border-left-color: #3182ce;
        background-color: #f7fafc;
    }
    
    .notification-item.low-balance {
        border-left-color: #e53e3e;
    }
    
    .notification-item.success {
        border-left-color: #38a169;
    }
    
    .notification-item.balance-update {
        border-left-color: #3182ce;
    }
    
    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
    }
    
    .notification-icon.success {
        background-color: #c6f6d5;
        color: #38a169;
    }
    
    .notification-icon.warning {
        background-color: #fed7d7;
        color: #e53e3e;
    }
    
    .notification-icon.info {
        background-color: #bee3f8;
        color: #3182ce;
    }
    
    .settings-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Notifications</h1>
            <div class="flex space-x-4">
                <button onclick="markAllAsRead()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Mark All as Read
                </button>
                <button onclick="toggleSettings()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Settings
                </button>
            </div>
        </div>

        <!-- Settings Panel (Hidden by default) -->
        <div id="settingsPanel" class="settings-card hidden">
            <h3 class="text-lg font-semibold mb-4">Notification Settings</h3>
            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Low Balance Threshold (KES)
                    </label>
                    <input type="number" 
                           id="thresholdInput" 
                           value="{{ request.user.wallet.low_balance_threshold|default:100 }}"
                           min="0" 
                           step="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="text-sm text-gray-500 mt-1">
                        You'll receive alerts when your balance falls below this amount
                    </p>
                </div>
                <div class="flex items-end">
                    <button onclick="updateThreshold()" 
                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        Update Threshold
                    </button>
                </div>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="space-y-4">
            {% if notifications %}
                {% for notification in notifications %}
                <div class="notification-item {% if not notification.is_read %}unread{% endif %} 
                           {% if notification.notification_type == 'LOW_BALANCE' %}low-balance
                           {% elif notification.notification_type == 'DEPOSIT_SUCCESS' or notification.notification_type == 'WITHDRAWAL_SUCCESS' %}success
                           {% else %}balance-update{% endif %}
                           bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow">
                    
                    <div class="flex items-start space-x-4">
                        <!-- Notification Icon -->
                        <div class="notification-icon 
                                   {% if notification.notification_type == 'LOW_BALANCE' %}warning
                                   {% elif notification.notification_type == 'DEPOSIT_SUCCESS' or notification.notification_type == 'WITHDRAWAL_SUCCESS' %}success
                                   {% else %}info{% endif %}">
                            {% if notification.notification_type == 'LOW_BALANCE' %}
                                ⚠️
                            {% elif notification.notification_type == 'DEPOSIT_SUCCESS' %}
                                ✅
                            {% elif notification.notification_type == 'WITHDRAWAL_SUCCESS' %}
                                💸
                            {% else %}
                                💰
                            {% endif %}
                        </div>
                        
                        <!-- Notification Content -->
                        <div class="flex-1">
                            <div class="flex justify-between items-start">
                                <h3 class="font-semibold text-gray-800 {% if not notification.is_read %}font-bold{% endif %}">
                                    {{ notification.title }}
                                </h3>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-500">
                                        {{ notification.created_at|timesince }} ago
                                    </span>
                                    {% if not notification.is_read %}
                                        <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <p class="text-gray-600 mt-1">{{ notification.message }}</p>
                            
                            <div class="flex justify-between items-center mt-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                           {% if notification.notification_type == 'LOW_BALANCE' %}bg-red-100 text-red-800
                                           {% elif notification.notification_type == 'DEPOSIT_SUCCESS' or notification.notification_type == 'WITHDRAWAL_SUCCESS' %}bg-green-100 text-green-800
                                           {% else %}bg-blue-100 text-blue-800{% endif %}">
                                    {{ notification.get_notification_type_display }}
                                </span>
                                
                                {% if not notification.is_read %}
                                    <button onclick="markAsRead({{ notification.id }})" 
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Mark as Read
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">🔔</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No notifications yet</h3>
                    <p class="text-gray-500">You'll see payment notifications and alerts here</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function toggleSettings() {
    const panel = document.getElementById('settingsPanel');
    panel.classList.toggle('hidden');
}

function updateThreshold() {
    const threshold = document.getElementById('thresholdInput').value;
    
    fetch('{% url "payments:update_low_balance_threshold" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: `threshold=${threshold}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Threshold updated successfully!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error updating threshold');
        console.error('Error:', error);
    });
}

function markAsRead(notificationId) {
    fetch(`/payments/notifications/${notificationId}/read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function markAllAsRead() {
    // This would require a separate endpoint, for now just reload
    location.reload();
}
</script>
{% endblock %}
