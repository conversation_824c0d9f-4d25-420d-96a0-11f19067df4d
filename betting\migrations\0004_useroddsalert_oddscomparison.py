# Generated by Django 5.2.4 on 2025-07-05 20:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("betting", "0003_betcancellation_betcancellationrule"),
        ("sports", "0002_remove_match_statistics_match_stats_data_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserOddsAlert",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("odds_increase", "Odds Increase"),
                            ("odds_decrease", "Odds Decrease"),
                            ("best_odds", "Best Odds Available"),
                            ("arbitrage", "Arbitrage Opportunity"),
                            ("value_bet", "Value Bet"),
                        ],
                        max_length=20,
                    ),
                ),
                ("target_odds", models.DecimalField(decimal_places=2, max_digits=10)),
                ("current_odds", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("triggered", "Triggered"),
                            ("expired", "Expired"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("alert_message", models.TextField(blank=True)),
                ("notification_sent", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("triggered_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "selection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_alerts",
                        to="betting.selection",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="odds_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Odds Alert",
                "verbose_name_plural": "User Odds Alerts",
                "db_table": "betting_user_odds_alert",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="OddsComparison",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("comparison_name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "best_home_odds",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "best_draw_odds",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "best_away_odds",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("total_markets", models.PositiveIntegerField(default=0)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "best_away_market",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="best_away_comparisons",
                        to="betting.market",
                    ),
                ),
                (
                    "best_draw_market",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="best_draw_comparisons",
                        to="betting.market",
                    ),
                ),
                (
                    "best_home_market",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="best_home_comparisons",
                        to="betting.market",
                    ),
                ),
                (
                    "bet_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="odds_comparisons",
                        to="betting.bettype",
                    ),
                ),
                (
                    "match",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="odds_comparisons",
                        to="sports.match",
                    ),
                ),
            ],
            options={
                "verbose_name": "Odds Comparison",
                "verbose_name_plural": "Odds Comparisons",
                "db_table": "betting_odds_comparison",
                "ordering": ["-last_updated"],
                "unique_together": {("match", "bet_type")},
            },
        ),
    ]
