from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
import json

User = get_user_model()


class GameProvider(models.Model):
    """Game providers/studios that supply casino games"""
    PROVIDER_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('MAINTENANCE', 'Under Maintenance'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True)
    logo = models.ImageField(upload_to='casino/providers/', null=True, blank=True)
    description = models.TextField(blank=True)
    website = models.URLField(blank=True)
    api_endpoint = models.URLField(blank=True)
    api_key = models.Char<PERSON>ield(max_length=255, blank=True)
    api_secret = models.CharField(max_length=255, blank=True)
    status = models.CharField(max_length=20, choices=PROVIDER_STATUS_CHOICES, default='ACTIVE')
    is_featured = models.BooleanField(default=False)
    priority = models.PositiveIntegerField(default=0, help_text="Higher numbers appear first")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'casino_game_providers'
        ordering = ['-priority', 'name']

    def __str__(self):
        return self.name

    @property
    def active_games_count(self):
        return self.games.filter(is_active=True).count()


class GameCategory(models.Model):
    """Categories for organizing casino games"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=50, unique=True)
    slug = models.SlugField(max_length=50, unique=True)
    icon = models.CharField(max_length=50, blank=True, help_text="CSS icon class")
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'casino_game_categories'
        ordering = ['-priority', 'name']
        verbose_name_plural = 'Game Categories'

    def __str__(self):
        return self.name


class Game(models.Model):
    """Casino games model with comprehensive game information"""
    GAME_TYPE_CHOICES = [
        ('SLOT', 'Slot Machine'),
        ('BLACKJACK', 'Blackjack'),
        ('ROULETTE', 'Roulette'),
        ('BACCARAT', 'Baccarat'),
        ('POKER', 'Poker'),
        ('AVIATOR', 'Aviator'),
        ('SPIN_WIN', 'Spin & Win'),
        ('LIVE_CASINO', 'Live Casino'),
        ('JACKPOT', 'Jackpot Game'),
        ('TABLE_GAME', 'Table Game'),
        ('CARD_GAME', 'Card Game'),
        ('OTHER', 'Other'),
    ]

    GAME_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('MAINTENANCE', 'Under Maintenance'),
        ('COMING_SOON', 'Coming Soon'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    provider = models.ForeignKey(GameProvider, on_delete=models.CASCADE, related_name='games')
    category = models.ForeignKey(GameCategory, on_delete=models.CASCADE, related_name='games')

    # Basic game information
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    game_type = models.CharField(max_length=20, choices=GAME_TYPE_CHOICES)

    # Game assets
    thumbnail = models.ImageField(upload_to='casino/games/thumbnails/', null=True, blank=True)
    banner_image = models.ImageField(upload_to='casino/games/banners/', null=True, blank=True)
    background_image = models.ImageField(upload_to='casino/games/backgrounds/', null=True, blank=True)

    # Game configuration
    min_bet = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    max_bet = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1000.00'))
    rtp_percentage = models.DecimalField(
        max_digits=5, decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Return to Player percentage"
    )
    volatility = models.CharField(
        max_length=10,
        choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High')],
        default='MEDIUM'
    )

    # Game features
    has_free_spins = models.BooleanField(default=False)
    has_bonus_rounds = models.BooleanField(default=False)
    has_progressive_jackpot = models.BooleanField(default=False)
    has_demo_mode = models.BooleanField(default=True)

    # Status and visibility
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    is_new = models.BooleanField(default=False)
    is_popular = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=GAME_STATUS_CHOICES, default='ACTIVE')

    # External integration
    external_game_id = models.CharField(max_length=100, blank=True)
    game_url = models.URLField(blank=True)
    demo_url = models.URLField(blank=True)

    # Statistics
    play_count = models.PositiveIntegerField(default=0)
    total_bets = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_wins = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Metadata
    tags = models.JSONField(default=list, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'casino_games'
        ordering = ['-is_featured', '-is_popular', 'name']
        indexes = [
            models.Index(fields=['game_type', 'is_active']),
            models.Index(fields=['provider', 'is_active']),
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['is_featured', 'is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({self.provider.name})"

    @property
    def actual_rtp(self):
        """Calculate actual RTP based on game sessions"""
        if self.total_bets > 0:
            return (self.total_wins / self.total_bets) * 100
        return 0

    def increment_play_count(self):
        """Increment play count when game is launched"""
        self.play_count += 1
        self.save(update_fields=['play_count'])


class GameSession(models.Model):
    """Individual game sessions for tracking player activity"""
    SESSION_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('COMPLETED', 'Completed'),
        ('ABANDONED', 'Abandoned'),
        ('ERROR', 'Error'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='casino_sessions')
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='sessions')

    # Session details
    session_token = models.CharField(max_length=255, unique=True)
    status = models.CharField(max_length=20, choices=SESSION_STATUS_CHOICES, default='ACTIVE')
    is_demo = models.BooleanField(default=False)

    # Financial tracking
    initial_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    final_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_bet_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_win_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    net_result = models.DecimalField(max_digits=15, decimal_places=2, default=0)  # wins - bets

    # Session statistics
    rounds_played = models.PositiveIntegerField(default=0)
    max_win = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    max_bet = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Technical details
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    device_type = models.CharField(max_length=20, blank=True)

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    last_activity = models.DateTimeField(auto_now=True)

    # Additional data
    session_data = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'casino_game_sessions'
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['game', 'status']),
            models.Index(fields=['started_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.game.name} ({self.started_at})"

    def end_session(self):
        """End the game session"""
        if self.status == 'ACTIVE':
            self.status = 'COMPLETED'
            self.ended_at = timezone.now()
            self.save(update_fields=['status', 'ended_at'])

    @property
    def duration(self):
        """Get session duration"""
        if self.ended_at:
            return self.ended_at - self.started_at
        return timezone.now() - self.started_at

    @property
    def session_rtp(self):
        """Calculate RTP for this session"""
        if self.total_bet_amount > 0:
            return (self.total_win_amount / self.total_bet_amount) * 100
        return 0


class GameResult(models.Model):
    """Individual game round results"""
    RESULT_TYPE_CHOICES = [
        ('WIN', 'Win'),
        ('LOSS', 'Loss'),
        ('JACKPOT', 'Jackpot'),
        ('BONUS', 'Bonus'),
        ('FREE_SPIN', 'Free Spin'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(GameSession, on_delete=models.CASCADE, related_name='results')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='casino_results')
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='results')

    # Round details
    round_id = models.CharField(max_length=100)
    external_round_id = models.CharField(max_length=100, blank=True)
    result_type = models.CharField(max_length=20, choices=RESULT_TYPE_CHOICES)

    # Financial details
    bet_amount = models.DecimalField(max_digits=10, decimal_places=2)
    win_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_result = models.DecimalField(max_digits=10, decimal_places=2)  # win - bet

    # Game specific data
    game_data = models.JSONField(default=dict, blank=True)  # Symbols, cards, etc.
    multiplier = models.DecimalField(max_digits=8, decimal_places=2, default=1)

    # Bonus features
    triggered_bonus = models.BooleanField(default=False)
    free_spins_awarded = models.PositiveIntegerField(default=0)

    # Timestamps
    played_at = models.DateTimeField(auto_now_add=True)

    # Verification
    is_verified = models.BooleanField(default=False)
    verification_data = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'casino_game_results'
        ordering = ['-played_at']
        indexes = [
            models.Index(fields=['user', 'played_at']),
            models.Index(fields=['game', 'played_at']),
            models.Index(fields=['session', 'played_at']),
            models.Index(fields=['result_type']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.game.name} - {self.result_type} (KES {self.net_result})"


class ProgressiveJackpot(models.Model):
    """Progressive jackpot pools"""
    JACKPOT_TYPE_CHOICES = [
        ('STANDALONE', 'Standalone'),
        ('LOCAL', 'Local Progressive'),
        ('NETWORK', 'Network Progressive'),
        ('MEGA', 'Mega Jackpot'),
    ]

    JACKPOT_STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('TRIGGERED', 'Triggered'),
        ('RESETTING', 'Resetting'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    jackpot_type = models.CharField(max_length=20, choices=JACKPOT_TYPE_CHOICES)
    games = models.ManyToManyField(Game, related_name='jackpots')

    # Jackpot amounts
    current_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    seed_amount = models.DecimalField(max_digits=15, decimal_places=2, default=1000)
    max_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)

    # Contribution settings
    contribution_percentage = models.DecimalField(
        max_digits=5, decimal_places=4, default=Decimal('0.01'),
        help_text="Percentage of each bet that contributes to jackpot"
    )

    # Trigger conditions
    trigger_probability = models.DecimalField(
        max_digits=10, decimal_places=8, default=Decimal('0.00001'),
        help_text="Probability of triggering jackpot per spin"
    )
    min_bet_to_qualify = models.DecimalField(max_digits=10, decimal_places=2, default=1)

    # Status
    status = models.CharField(max_length=20, choices=JACKPOT_STATUS_CHOICES, default='ACTIVE')
    is_featured = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_won_at = models.DateTimeField(null=True, blank=True)

    # Winner tracking
    last_winner = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='jackpots_won')
    last_winning_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_winners = models.PositiveIntegerField(default=0)
    total_paid_out = models.DecimalField(max_digits=20, decimal_places=2, default=0)

    class Meta:
        db_table = 'casino_progressive_jackpots'
        ordering = ['-current_amount']

    def __str__(self):
        return f"{self.name} - KES {self.current_amount:,.2f}"

    def add_contribution(self, amount):
        """Add contribution to jackpot"""
        contribution = amount * self.contribution_percentage
        self.current_amount += contribution
        self.save(update_fields=['current_amount', 'updated_at'])
        return contribution

    def trigger_jackpot(self, winner, winning_amount=None):
        """Trigger jackpot win"""
        if winning_amount is None:
            winning_amount = self.current_amount

        self.last_winner = winner
        self.last_winning_amount = winning_amount
        self.last_won_at = timezone.now()
        self.total_winners += 1
        self.total_paid_out += winning_amount
        self.status = 'TRIGGERED'
        self.save()

        # Create jackpot win record
        JackpotWin.objects.create(
            jackpot=self,
            winner=winner,
            amount=winning_amount,
            game=None  # Will be set by the calling code
        )

        return winning_amount

    def reset_jackpot(self):
        """Reset jackpot to seed amount"""
        self.current_amount = self.seed_amount
        self.status = 'ACTIVE'
        self.save(update_fields=['current_amount', 'status', 'updated_at'])


class JackpotWin(models.Model):
    """Record of jackpot wins"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    jackpot = models.ForeignKey(ProgressiveJackpot, on_delete=models.CASCADE, related_name='wins')
    winner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='jackpot_wins')
    game = models.ForeignKey(Game, on_delete=models.CASCADE, null=True, blank=True)

    amount = models.DecimalField(max_digits=15, decimal_places=2)
    bet_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Verification
    is_verified = models.BooleanField(default=False)
    is_paid = models.BooleanField(default=False)
    paid_at = models.DateTimeField(null=True, blank=True)

    # Additional data
    win_data = models.JSONField(default=dict, blank=True)

    won_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'casino_jackpot_wins'
        ordering = ['-won_at']

    def __str__(self):
        return f"{self.winner.username} won KES {self.amount:,.2f} on {self.jackpot.name}"


class GameStatistics(models.Model):
    """Daily game statistics for analytics"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    game = models.ForeignKey(Game, on_delete=models.CASCADE, related_name='daily_stats')
    date = models.DateField()

    # Player statistics
    unique_players = models.PositiveIntegerField(default=0)
    total_sessions = models.PositiveIntegerField(default=0)
    total_rounds = models.PositiveIntegerField(default=0)

    # Financial statistics
    total_bets = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_wins = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    gross_gaming_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=0)  # bets - wins

    # Performance metrics
    average_session_duration = models.DurationField(null=True, blank=True)
    average_bet_size = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    actual_rtp = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Jackpot statistics
    jackpot_contributions = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    jackpots_won = models.PositiveIntegerField(default=0)
    jackpot_payouts = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'casino_game_statistics'
        unique_together = ['game', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.game.name} - {self.date}"
