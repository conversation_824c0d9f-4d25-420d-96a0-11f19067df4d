# ZBet Platform - Sports Betting Platform

A comprehensive sports betting platform similar to Betika, built with Django and focused on African markets with M-Pesa integration.

## Project Overview

This is a full-featured sports betting platform that includes:
- Sports betting (pre-match and live)
- Casino games integration
- M-Pesa payment integration
- User management and KYC
- Promotions and bonuses
- Mobile-first responsive design
- Admin panel and analytics

## Technology Stack

### Backend
- **Django 4.2.7** - Web framework
- **PostgreSQL** - Primary database (SQLite for development)
- **Redis** - Caching and sessions
- **Celery** - Background tasks
- **Django REST Framework** - API development

### Frontend
- **Django Templates** - Server-side rendering
- **Tailwind CSS** - Styling
- **Alpine.js** - JavaScript framework
- **HTMX** - Dynamic content updates

### Payment Integration
- **M-Pesa Daraja API** - Primary payment method
- **Flutterwave/Paystack** - Alternative payment methods

## Project Structure

```
zbet_platform/
├── accounts/          # User management and authentication
├── sports/           # Sports data and events
├── betting/          # Betting engine and logic
├── casino/           # Casino games integration
├── payments/         # Payment processing (M-Pesa, etc.)
├── promotions/       # Bonuses and promotional campaigns
├── notifications/    # Messaging and notifications
├── analytics/        # Reporting and analytics
├── admin_panel/      # Admin interface
├── api/             # REST API endpoints
├── static/          # Static files (CSS, JS, images)
├── media/           # User uploads
├── templates/       # HTML templates
└── zbet_platform/   # Main project settings
```

## Setup Instructions

### Prerequisites
- Python 3.8+
- PostgreSQL (optional for development)
- Redis (optional for development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd zbet
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv
   # On Windows
   .\venv\Scripts\activate
   # On Linux/Mac
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Run migrations**
   ```bash
   python manage.py migrate
   ```

6. **Create superuser**
   ```bash
   python manage.py createsuperuser
   ```

7. **Run development server**
   ```bash
   python manage.py runserver
   ```

## Environment Variables

Key environment variables (see `.env.example` for full list):

```env
# Django Settings
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (optional for development)
DB_NAME=zbet_platform
DB_USER=postgres
DB_PASSWORD=your-password

# M-Pesa Configuration
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=your-consumer-key
MPESA_CONSUMER_SECRET=your-consumer-secret
MPESA_SHORTCODE=your-shortcode
MPESA_PASSKEY=your-passkey
```

## Development Status

### ✅ Completed
- [x] Django project initialization
- [x] Core application structure
- [x] Basic settings configuration
- [x] Database setup (SQLite for development)
- [x] Requirements and dependencies
- [x] Environment configuration

### 🚧 In Progress
- [ ] User management system
- [ ] Sports betting engine
- [ ] M-Pesa payment integration
- [ ] Casino games integration

### 📋 Planned
- [ ] Mobile optimization
- [ ] API development
- [ ] Testing suite
- [ ] Deployment configuration

## Key Features

### User Management
- Custom user authentication
- KYC verification system
- Multi-factor authentication
- Social media login integration

### Sports Betting
- Pre-match betting
- Live betting with real-time odds
- Multiple bet types (1X2, Over/Under, etc.)
- Accumulator bets
- Virtual sports

### Payment System
- M-Pesa STK Push integration
- Instant deposits and withdrawals
- Multiple payment methods
- Transaction history and receipts

### Casino Games
- Slot machines
- Table games (Blackjack, Roulette)
- Live casino
- Progressive jackpots

### Mobile Experience
- Mobile-first responsive design
- Progressive Web App (PWA)
- Touch-friendly interfaces
- Offline capabilities

## API Documentation

API documentation will be available at `/api/docs/` when the API endpoints are implemented.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is proprietary software. All rights reserved.

## Support

For support and questions, please contact the development team.

## Deployment

Deployment instructions will be added as the project progresses. The platform is designed to be deployed on cloud providers like AWS, Azure, or GCP with proper scaling and security configurations.
