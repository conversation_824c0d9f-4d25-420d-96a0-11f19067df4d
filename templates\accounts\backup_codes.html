{% extends 'base.html' %}

{% block title %}Backup Codes - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Backup Codes</h3>
                <p class="mb-0">
                    {% if regenerated %}New backup codes generated{% else %}Your current backup codes{% endif %}
                </p>
            </div>
            
            <div class="card-body">
                {% if regenerated %}
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> New Backup Codes Generated</h6>
                        <p class="mb-0">
                            Your old backup codes are no longer valid. Save these new codes in a safe place.
                        </p>
                    </div>
                {% endif %}
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Important Security Information</h6>
                    <ul class="mb-0">
                        <li>Each backup code can only be used once</li>
                        <li>Store these codes in a safe, secure location</li>
                        <li>Don't share these codes with anyone</li>
                        <li>Use these codes if you lose access to your primary 2FA method</li>
                    </ul>
                </div>
                
                <!-- Backup Codes -->
                <div class="card bg-light mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-key"></i> Your Backup Codes</h6>
                        <button class="btn btn-sm btn-outline-secondary" onclick="copyAllCodes()">
                            <i class="fas fa-copy"></i> Copy All
                        </button>
                    </div>
                    <div class="card-body">
                        {% if codes %}
                            <div class="row" id="backupCodes">
                                {% for code in codes %}
                                    <div class="col-md-6 mb-2">
                                        <div class="input-group">
                                            <span class="input-group-text">{{ forloop.counter }}</span>
                                            <input type="text" class="form-control font-monospace text-center backup-code" 
                                                   value="{{ code }}" readonly>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted text-center">No backup codes available.</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Statistics -->
                {% if backup_info %}
                <div class="card mb-4">
                    <div class="card-body">
                        <h6><i class="fas fa-chart-bar"></i> Usage Statistics</h6>
                        <div class="row text-center">
                            <div class="col-md-4">
                                <h4 class="text-primary">{{ backup_info.total }}</h4>
                                <small class="text-muted">Total Generated</small>
                            </div>
                            <div class="col-md-4">
                                <h4 class="text-danger">{{ backup_info.used }}</h4>
                                <small class="text-muted">Used</small>
                            </div>
                            <div class="col-md-4">
                                <h4 class="{% if backup_info.remaining < 3 %}text-danger{% elif backup_info.remaining < 5 %}text-warning{% else %}text-success{% endif %}">
                                    {{ backup_info.remaining }}
                                </h4>
                                <small class="text-muted">Remaining</small>
                            </div>
                        </div>
                        
                        {% if backup_info.remaining < 3 %}
                            <div class="alert alert-danger mt-3">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Warning:</strong> You're running low on backup codes. Consider regenerating them soon.
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Actions -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <button class="btn btn-primary" onclick="printCodes()">
                        <i class="fas fa-print"></i> Print Codes
                    </button>
                    <button class="btn btn-success" onclick="downloadCodes()">
                        <i class="fas fa-download"></i> Download as Text
                    </button>
                    <a href="{% url 'accounts:regenerate_backup_codes' %}" class="btn btn-warning">
                        <i class="fas fa-sync"></i> Generate New Codes
                    </a>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:two_factor_settings' %}" class="btn btn-outline-secondary">
                        Back to 2FA Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Usage Instructions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-question-circle"></i> How to Use Backup Codes</h6>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li>If you lose access to your phone or authenticator app, go to the login page</li>
                    <li>Enter your username and password as usual</li>
                    <li>When prompted for your 2FA code, enter one of these 8-character backup codes</li>
                    <li>Each code can only be used once, so cross it off your list after use</li>
                    <li>Generate new backup codes when you're running low</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function copyAllCodes() {
    const codes = [];
    document.querySelectorAll('.backup-code').forEach(function(input) {
        codes.push(input.value);
    });
    
    const codesText = codes.join('\n');
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(codesText).then(function() {
            alert('All backup codes copied to clipboard!');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = codesText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('All backup codes copied to clipboard!');
    }
}

function printCodes() {
    const codes = [];
    document.querySelectorAll('.backup-code').forEach(function(input) {
        codes.push(input.value);
    });
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>ZBet - Backup Codes</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #1e40af; }
                .code { font-family: monospace; font-size: 14px; margin: 5px 0; }
                .warning { color: #dc2626; font-weight: bold; margin: 20px 0; }
            </style>
        </head>
        <body>
            <h1>ZBet - Two-Factor Authentication Backup Codes</h1>
            <p><strong>Account:</strong> {{ user.email }}</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
            
            <div class="warning">
                ⚠️ IMPORTANT: Keep these codes safe and secure. Each code can only be used once.
            </div>
            
            <h2>Backup Codes:</h2>
            ${codes.map((code, index) => `<div class="code">${index + 1}. ${code}</div>`).join('')}
            
            <div class="warning">
                ⚠️ Do not share these codes with anyone. Store them in a safe place.
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function downloadCodes() {
    const codes = [];
    document.querySelectorAll('.backup-code').forEach(function(input) {
        codes.push(input.value);
    });
    
    const content = `ZBet - Two-Factor Authentication Backup Codes
Account: {{ user.email }}
Generated: ${new Date().toLocaleDateString()}

⚠️ IMPORTANT: Keep these codes safe and secure. Each code can only be used once.

Backup Codes:
${codes.map((code, index) => `${index + 1}. ${code}`).join('\n')}

⚠️ Do not share these codes with anyone. Store them in a safe place.
`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'zbet-backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
