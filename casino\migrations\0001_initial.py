# Generated by Django 5.2.4 on 2025-07-08 09:33

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="GameCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=50, unique=True)),
                ("slug", models.SlugField(unique=True)),
                (
                    "icon",
                    models.CharField(
                        blank=True, help_text="CSS icon class", max_length=50
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("priority", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "Game Categories",
                "db_table": "casino_game_categories",
                "ordering": ["-priority", "name"],
            },
        ),
        migrations.CreateModel(
            name="GameProvider",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("code", models.CharField(max_length=20, unique=True)),
                (
                    "logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="casino/providers/"
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("website", models.URLField(blank=True)),
                ("api_endpoint", models.URLField(blank=True)),
                ("api_key", models.CharField(blank=True, max_length=255)),
                ("api_secret", models.CharField(blank=True, max_length=255)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("INACTIVE", "Inactive"),
                            ("MAINTENANCE", "Under Maintenance"),
                        ],
                        default="ACTIVE",
                        max_length=20,
                    ),
                ),
                ("is_featured", models.BooleanField(default=False)),
                (
                    "priority",
                    models.PositiveIntegerField(
                        default=0, help_text="Higher numbers appear first"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "casino_game_providers",
                "ordering": ["-priority", "name"],
            },
        ),
        migrations.CreateModel(
            name="Game",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("slug", models.SlugField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "game_type",
                    models.CharField(
                        choices=[
                            ("SLOT", "Slot Machine"),
                            ("BLACKJACK", "Blackjack"),
                            ("ROULETTE", "Roulette"),
                            ("BACCARAT", "Baccarat"),
                            ("POKER", "Poker"),
                            ("AVIATOR", "Aviator"),
                            ("SPIN_WIN", "Spin & Win"),
                            ("LIVE_CASINO", "Live Casino"),
                            ("JACKPOT", "Jackpot Game"),
                            ("TABLE_GAME", "Table Game"),
                            ("CARD_GAME", "Card Game"),
                            ("OTHER", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "thumbnail",
                    models.ImageField(
                        blank=True, null=True, upload_to="casino/games/thumbnails/"
                    ),
                ),
                (
                    "banner_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="casino/games/banners/"
                    ),
                ),
                (
                    "background_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="casino/games/backgrounds/"
                    ),
                ),
                (
                    "min_bet",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1.00"), max_digits=10
                    ),
                ),
                (
                    "max_bet",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1000.00"), max_digits=10
                    ),
                ),
                (
                    "rtp_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Return to Player percentage",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "volatility",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                        ],
                        default="MEDIUM",
                        max_length=10,
                    ),
                ),
                ("has_free_spins", models.BooleanField(default=False)),
                ("has_bonus_rounds", models.BooleanField(default=False)),
                ("has_progressive_jackpot", models.BooleanField(default=False)),
                ("has_demo_mode", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_featured", models.BooleanField(default=False)),
                ("is_new", models.BooleanField(default=False)),
                ("is_popular", models.BooleanField(default=False)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("INACTIVE", "Inactive"),
                            ("MAINTENANCE", "Under Maintenance"),
                            ("COMING_SOON", "Coming Soon"),
                        ],
                        default="ACTIVE",
                        max_length=20,
                    ),
                ),
                ("external_game_id", models.CharField(blank=True, max_length=100)),
                ("game_url", models.URLField(blank=True)),
                ("demo_url", models.URLField(blank=True)),
                ("play_count", models.PositiveIntegerField(default=0)),
                (
                    "total_bets",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_wins",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("tags", models.JSONField(blank=True, default=list)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="games",
                        to="casino.gamecategory",
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="games",
                        to="casino.gameprovider",
                    ),
                ),
            ],
            options={
                "db_table": "casino_games",
                "ordering": ["-is_featured", "-is_popular", "name"],
            },
        ),
        migrations.CreateModel(
            name="GameSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("session_token", models.CharField(max_length=255, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("COMPLETED", "Completed"),
                            ("ABANDONED", "Abandoned"),
                            ("ERROR", "Error"),
                        ],
                        default="ACTIVE",
                        max_length=20,
                    ),
                ),
                ("is_demo", models.BooleanField(default=False)),
                (
                    "initial_balance",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "final_balance",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_bet_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_win_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "net_result",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("rounds_played", models.PositiveIntegerField(default=0)),
                (
                    "max_win",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "max_bet",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("device_type", models.CharField(blank=True, max_length=20)),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                ("last_activity", models.DateTimeField(auto_now=True)),
                ("session_data", models.JSONField(blank=True, default=dict)),
                (
                    "game",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="casino.game",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="casino_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "casino_game_sessions",
                "ordering": ["-started_at"],
            },
        ),
        migrations.CreateModel(
            name="GameResult",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("round_id", models.CharField(max_length=100)),
                ("external_round_id", models.CharField(blank=True, max_length=100)),
                (
                    "result_type",
                    models.CharField(
                        choices=[
                            ("WIN", "Win"),
                            ("LOSS", "Loss"),
                            ("JACKPOT", "Jackpot"),
                            ("BONUS", "Bonus"),
                            ("FREE_SPIN", "Free Spin"),
                        ],
                        max_length=20,
                    ),
                ),
                ("bet_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "win_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("net_result", models.DecimalField(decimal_places=2, max_digits=10)),
                ("game_data", models.JSONField(blank=True, default=dict)),
                (
                    "multiplier",
                    models.DecimalField(decimal_places=2, default=1, max_digits=8),
                ),
                ("triggered_bonus", models.BooleanField(default=False)),
                ("free_spins_awarded", models.PositiveIntegerField(default=0)),
                ("played_at", models.DateTimeField(auto_now_add=True)),
                ("is_verified", models.BooleanField(default=False)),
                ("verification_data", models.JSONField(blank=True, default=dict)),
                (
                    "game",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="results",
                        to="casino.game",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="casino_results",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="results",
                        to="casino.gamesession",
                    ),
                ),
            ],
            options={
                "db_table": "casino_game_results",
                "ordering": ["-played_at"],
            },
        ),
        migrations.CreateModel(
            name="GameStatistics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField()),
                ("unique_players", models.PositiveIntegerField(default=0)),
                ("total_sessions", models.PositiveIntegerField(default=0)),
                ("total_rounds", models.PositiveIntegerField(default=0)),
                (
                    "total_bets",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_wins",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "gross_gaming_revenue",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "average_session_duration",
                    models.DurationField(blank=True, null=True),
                ),
                (
                    "average_bet_size",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "actual_rtp",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "jackpot_contributions",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("jackpots_won", models.PositiveIntegerField(default=0)),
                (
                    "jackpot_payouts",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "game",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_stats",
                        to="casino.game",
                    ),
                ),
            ],
            options={
                "db_table": "casino_game_statistics",
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="ProgressiveJackpot",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "jackpot_type",
                    models.CharField(
                        choices=[
                            ("STANDALONE", "Standalone"),
                            ("LOCAL", "Local Progressive"),
                            ("NETWORK", "Network Progressive"),
                            ("MEGA", "Mega Jackpot"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "current_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "seed_amount",
                    models.DecimalField(decimal_places=2, default=1000, max_digits=15),
                ),
                (
                    "max_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "contribution_percentage",
                    models.DecimalField(
                        decimal_places=4,
                        default=Decimal("0.01"),
                        help_text="Percentage of each bet that contributes to jackpot",
                        max_digits=5,
                    ),
                ),
                (
                    "trigger_probability",
                    models.DecimalField(
                        decimal_places=8,
                        default=Decimal("0.00001"),
                        help_text="Probability of triggering jackpot per spin",
                        max_digits=10,
                    ),
                ),
                (
                    "min_bet_to_qualify",
                    models.DecimalField(decimal_places=2, default=1, max_digits=10),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("INACTIVE", "Inactive"),
                            ("TRIGGERED", "Triggered"),
                            ("RESETTING", "Resetting"),
                        ],
                        default="ACTIVE",
                        max_length=20,
                    ),
                ),
                ("is_featured", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("last_won_at", models.DateTimeField(blank=True, null=True)),
                (
                    "last_winning_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("total_winners", models.PositiveIntegerField(default=0)),
                (
                    "total_paid_out",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "games",
                    models.ManyToManyField(related_name="jackpots", to="casino.game"),
                ),
                (
                    "last_winner",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="jackpots_won",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "casino_progressive_jackpots",
                "ordering": ["-current_amount"],
            },
        ),
        migrations.CreateModel(
            name="JackpotWin",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "bet_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("is_verified", models.BooleanField(default=False)),
                ("is_paid", models.BooleanField(default=False)),
                ("paid_at", models.DateTimeField(blank=True, null=True)),
                ("win_data", models.JSONField(blank=True, default=dict)),
                ("won_at", models.DateTimeField(auto_now_add=True)),
                (
                    "game",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="casino.game",
                    ),
                ),
                (
                    "winner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="jackpot_wins",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "jackpot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="wins",
                        to="casino.progressivejackpot",
                    ),
                ),
            ],
            options={
                "db_table": "casino_jackpot_wins",
                "ordering": ["-won_at"],
            },
        ),
        migrations.AddIndex(
            model_name="game",
            index=models.Index(
                fields=["game_type", "is_active"], name="casino_game_game_ty_0dee2b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="game",
            index=models.Index(
                fields=["provider", "is_active"], name="casino_game_provide_d7dbf9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="game",
            index=models.Index(
                fields=["category", "is_active"], name="casino_game_categor_af349e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="game",
            index=models.Index(
                fields=["is_featured", "is_active"],
                name="casino_game_is_feat_e14b8c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="gamesession",
            index=models.Index(
                fields=["user", "status"], name="casino_game_user_id_379bcd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gamesession",
            index=models.Index(
                fields=["game", "status"], name="casino_game_game_id_f0872d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gamesession",
            index=models.Index(
                fields=["started_at"], name="casino_game_started_d0b25c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gameresult",
            index=models.Index(
                fields=["user", "played_at"], name="casino_game_user_id_7bb6fe_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gameresult",
            index=models.Index(
                fields=["game", "played_at"], name="casino_game_game_id_d346c9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gameresult",
            index=models.Index(
                fields=["session", "played_at"], name="casino_game_session_ad2c6d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="gameresult",
            index=models.Index(
                fields=["result_type"], name="casino_game_result__470789_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="gamestatistics",
            unique_together={("game", "date")},
        ),
    ]
