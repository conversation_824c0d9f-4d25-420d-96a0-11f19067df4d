{% extends 'base.html' %}

{% block title %}Request SMS Verification - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Verify Your Phone</h3>
                <p class="mb-0">Request SMS verification</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-mobile-alt fa-3x text-warning mb-3"></i>
                    <h5>Phone Not Verified</h5>
                    <p class="text-muted">
                        Your phone number is not yet verified. Please verify your phone to access all features.
                    </p>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Why verify your phone?</h6>
                    <ul class="list-unstyled mb-0">
                        <li>✓ Enable M-Pesa transactions</li>
                        <li>✓ Receive SMS notifications</li>
                        <li>✓ Two-factor authentication</li>
                        <li>✓ Account security alerts</li>
                    </ul>
                </div>
                
                <form method="post" id="requestSMSForm">
                    {% csrf_token %}
                    
                    <!-- Phone Number (readonly) -->
                    <div class="form-floating mb-3">
                        {{ form.phone_number }}
                        <label for="{{ form.phone_number.id_for_label }}">Phone Number</label>
                        <div class="form-text">{{ form.phone_number.help_text }}</div>
                        {% if form.phone_number.errors %}
                            <div class="text-danger small">{{ form.phone_number.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Send Verification Code
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-0">Need to change your phone number?</p>
                    <a href="{% url 'accounts:change_phone' %}" class="btn btn-outline-secondary">
                        Change Phone Number
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Info Card -->
        <div class="card mt-3">
            <div class="card-body text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    SMS charges may apply according to your mobile plan. We'll send a 6-digit verification code.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form submission
    $('#requestSMSForm').on('submit', function(e) {
        const btn = $(this).find('button[type="submit"]');
        btn.prop('disabled', true).text('Sending...');
        
        // Re-enable button after 3 seconds to prevent multiple rapid submissions
        setTimeout(function() {
            btn.prop('disabled', false).text('Send Verification Code');
        }, 3000);
    });
});
</script>
{% endblock %}
