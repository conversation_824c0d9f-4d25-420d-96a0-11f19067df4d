from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal

from accounts.models import UserProfile
from sports.models import Sport, League, Team, Event, Odds
from betting.models import Bet
from payments.models import Wallet, Deposit, Withdrawal
from promotions.models import Promotion, Bonus, BonusType
from casino.models import Game, GameProvider

User = get_user_model()


class APIAuthenticationTestCase(APITestCase):
    """Test API authentication endpoints"""

    def setUp(self):
        self.client = APIClient()
        self.register_url = reverse('api:register')
        self.login_url = reverse('api:login')
        self.profile_url = reverse('api:profile')

        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'password_confirm': 'testpass123',
            'phone_number': '************',
            'first_name': 'Test',
            'last_name': 'User'
        }

    def test_user_registration(self):
        """Test user registration"""
        response = self.client.post(self.register_url, self.user_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('tokens', response.data)
        self.assertIn('user', response.data)

        # Check user was created
        self.assertTrue(User.objects.filter(username='testuser').exists())

    def test_user_login(self):
        """Test user login"""
        # Create user first
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        login_data = {
            'username': 'testuser',
            'password': 'testpass123'
        }

        response = self.client.post(self.login_url, login_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('tokens', response.data)
        self.assertIn('user', response.data)

    def test_profile_access_authenticated(self):
        """Test profile access with authentication"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create profile
        UserProfile.objects.create(
            user=user,
            phone_number='************'
        )

        # Authenticate
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        response = self.client.get(self.profile_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], 'testuser')

    def test_profile_access_unauthenticated(self):
        """Test profile access without authentication"""
        response = self.client.get(self.profile_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class SportsAPITestCase(APITestCase):
    """Test sports API endpoints"""

    def setUp(self):
        self.client = APIClient()

        # Create test data
        self.sport = Sport.objects.create(name='Football', slug='football', is_active=True)
        self.league = League.objects.create(
            name='Premier League',
            slug='premier-league',
            sport=self.sport,
            country='England',
            is_active=True
        )
        self.team1 = Team.objects.create(
            name='Team A',
            slug='team-a',
            sport=self.sport,
            country='England',
            is_active=True
        )
        self.team2 = Team.objects.create(
            name='Team B',
            slug='team-b',
            sport=self.sport,
            country='England',
            is_active=True
        )

        from django.utils import timezone
        self.event = Event.objects.create(
            sport=self.sport,
            league=self.league,
            home_team=self.team1,
            away_team=self.team2,
            event_date=timezone.now() + timezone.timedelta(days=1),
            status='UPCOMING'
        )

        self.odds = Odds.objects.create(
            event=self.event,
            market_type='1X2',
            selection='1',
            odds_value=Decimal('2.50'),
            is_active=True
        )

    def test_sports_list(self):
        """Test sports listing"""
        url = reverse('api:sports-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Football')

    def test_events_list(self):
        """Test events listing"""
        url = reverse('api:events-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_event_detail(self):
        """Test event detail"""
        url = reverse('api:event-detail', kwargs={'pk': self.event.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.event.id)

    def test_event_odds(self):
        """Test event odds"""
        url = reverse('api:event-odds', kwargs={'event_id': self.event.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)


class BettingAPITestCase(APITestCase):
    """Test betting API endpoints"""

    def setUp(self):
        self.client = APIClient()

        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create wallet
        self.wallet = Wallet.objects.create(user=self.user, balance=Decimal('1000.00'))

        # Authenticate
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        # Create test sports data
        self.sport = Sport.objects.create(name='Football', slug='football', is_active=True)
        self.league = League.objects.create(
            name='Premier League',
            slug='premier-league',
            sport=self.sport,
            is_active=True
        )
        self.team1 = Team.objects.create(name='Team A', slug='team-a', sport=self.sport, is_active=True)
        self.team2 = Team.objects.create(name='Team B', slug='team-b', sport=self.sport, is_active=True)

        from django.utils import timezone
        self.event = Event.objects.create(
            sport=self.sport,
            league=self.league,
            home_team=self.team1,
            away_team=self.team2,
            event_date=timezone.now() + timezone.timedelta(days=1),
            status='UPCOMING'
        )

        self.odds = Odds.objects.create(
            event=self.event,
            market_type='1X2',
            selection='1',
            odds_value=Decimal('2.50'),
            is_active=True
        )

    def test_place_single_bet(self):
        """Test placing a single bet"""
        url = reverse('api:place-bet')
        bet_data = {
            'selections': [
                {
                    'event_id': str(self.event.id),
                    'odds_id': str(self.odds.id),
                    'selection_type': '1'
                }
            ],
            'stake_amount': '100.00',
            'bet_type': 'SINGLE'
        }

        response = self.client.post(url, bet_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('bet_id', response.data)

        # Check bet was created
        self.assertTrue(Bet.objects.filter(user=self.user).exists())

        # Check wallet balance was deducted
        self.wallet.refresh_from_db()
        self.assertEqual(self.wallet.balance, Decimal('900.00'))

    def test_user_bets_list(self):
        """Test user bets listing"""
        # Create a bet first
        bet = Bet.objects.create(
            user=self.user,
            bet_type='SINGLE',
            stake_amount=Decimal('100.00'),
            total_odds=Decimal('2.50'),
            potential_winnings=Decimal('250.00'),
            status='PENDING'
        )

        url = reverse('api:user-bets')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)


class PaymentAPITestCase(APITestCase):
    """Test payment API endpoints"""

    def setUp(self):
        self.client = APIClient()

        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create wallet
        self.wallet = Wallet.objects.create(user=self.user, balance=Decimal('1000.00'))

        # Authenticate
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_wallet_balance(self):
        """Test wallet balance endpoint"""
        url = reverse('api:wallet-balance')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['balance'], '1000.00')

    def test_create_deposit(self):
        """Test creating a deposit"""
        url = reverse('api:create-deposit')
        deposit_data = {
            'amount': '500.00',
            'phone_number': '************',
            'payment_method': 'MPESA'
        }

        response = self.client.post(url, deposit_data)
        # Note: This might fail in test environment due to M-Pesa integration
        # In a real test, you'd mock the M-Pesa service
        self.assertIn(response.status_code, [status.HTTP_201_CREATED, status.HTTP_400_BAD_REQUEST])

    def test_create_withdrawal(self):
        """Test creating a withdrawal"""
        url = reverse('api:create-withdrawal')
        withdrawal_data = {
            'amount': '200.00',
            'phone_number': '************'
        }

        response = self.client.post(url, withdrawal_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('withdrawal_id', response.data)

        # Check wallet balance was deducted
        self.wallet.refresh_from_db()
        self.assertEqual(self.wallet.balance, Decimal('800.00'))


class PromotionAPITestCase(APITestCase):
    """Test promotion API endpoints"""

    def setUp(self):
        self.client = APIClient()

        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Authenticate
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        # Create test promotion data
        self.bonus_type = BonusType.objects.create(
            name='Welcome Bonus',
            category='WELCOME',
            description='Welcome bonus for new users'
        )

        from django.utils import timezone
        self.promotion = Promotion.objects.create(
            name='Welcome Bonus 100%',
            slug='welcome-bonus-100',
            description='Get 100% bonus on your first deposit',
            promotion_type='PERCENTAGE',
            bonus_type=self.bonus_type,
            percentage_value=Decimal('100.00'),
            start_date=timezone.now() - timezone.timedelta(days=1),
            end_date=timezone.now() + timezone.timedelta(days=30),
            status='ACTIVE'
        )

    def test_promotions_list(self):
        """Test promotions listing"""
        url = reverse('api:promotions-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_user_bonuses(self):
        """Test user bonuses listing"""
        # Create a bonus for the user
        bonus = Bonus.objects.create(
            user=self.user,
            bonus_type=self.bonus_type,
            promotion=self.promotion,
            bonus_amount=Decimal('100.00'),
            remaining_amount=Decimal('100.00'),
            wagering_requirement=Decimal('500.00'),
            status='ACTIVE'
        )

        url = reverse('api:user-bonuses')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)


class APIThrottlingTestCase(APITestCase):
    """Test API rate limiting"""

    def setUp(self):
        self.client = APIClient()

    def test_anonymous_rate_limiting(self):
        """Test rate limiting for anonymous users"""
        url = reverse('api:sports-list')

        # Make multiple requests to test throttling
        # Note: This test might need adjustment based on throttling settings
        responses = []
        for i in range(10):
            response = self.client.get(url)
            responses.append(response.status_code)

        # All requests should succeed for sports list (no throttling for read operations)
        self.assertTrue(all(status_code == 200 for status_code in responses))


class APIDocumentationTestCase(APITestCase):
    """Test API documentation endpoints"""

    def test_api_schema(self):
        """Test API schema endpoint"""
        url = reverse('api:schema')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_swagger_ui(self):
        """Test Swagger UI endpoint"""
        url = reverse('api:swagger-ui')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_redoc_ui(self):
        """Test ReDoc UI endpoint"""
        url = reverse('api:redoc')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
