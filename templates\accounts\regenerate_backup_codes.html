{% extends 'base.html' %}

{% block title %}Regenerate Backup Codes - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow border-warning">
            <div class="card-header bg-warning text-dark text-center">
                <h3 class="mb-0">Regenerate Backup Codes</h3>
                <p class="mb-0">Generate new backup codes</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-sync-alt fa-3x text-warning mb-3"></i>
                    <h5>Generate New Backup Codes</h5>
                    <p class="text-muted">
                        This will create new backup codes and invalidate your old ones.
                    </p>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Important Information</h6>
                    <ul class="mb-0">
                        <li>Your current backup codes will no longer work</li>
                        <li>You'll receive 10 new backup codes</li>
                        <li>Each new code can only be used once</li>
                        <li>Save the new codes in a safe place</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> When to Regenerate</h6>
                    <ul class="mb-0">
                        <li>You're running low on backup codes</li>
                        <li>You suspect your codes may be compromised</li>
                        <li>You've lost your backup codes</li>
                        <li>As a regular security practice</li>
                    </ul>
                </div>
                
                <form method="post" id="regenerateForm">
                    {% csrf_token %}
                    
                    <!-- Password Confirmation -->
                    <div class="form-floating mb-3">
                        {{ form.password }}
                        <label for="{{ form.password.id_for_label }}">Current Password</label>
                        <div class="form-text">{{ form.password.help_text }}</div>
                        {% if form.password.errors %}
                            <div class="text-danger small">{{ form.password.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Confirmation Checkbox -->
                    <div class="form-check mb-3">
                        {{ form.confirmation }}
                        <label class="form-check-label" for="{{ form.confirmation.id_for_label }}">
                            {{ form.confirmation.label }}
                        </label>
                        {% if form.confirmation.errors %}
                            <div class="text-danger small">{{ form.confirmation.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-warning btn-lg" id="regenerateBtn" disabled>
                            <i class="fas fa-sync-alt"></i> Generate New Backup Codes
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:two_factor_settings' %}" class="btn btn-outline-secondary">
                        Cancel
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Security Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb"></i> Security Best Practices</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success"></i> Store backup codes in multiple safe locations</li>
                    <li><i class="fas fa-check text-success"></i> Don't store codes in plain text on your computer</li>
                    <li><i class="fas fa-check text-success"></i> Consider using a password manager</li>
                    <li><i class="fas fa-check text-success"></i> Print codes and store them physically</li>
                    <li><i class="fas fa-check text-success"></i> Regenerate codes periodically</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Enable/disable submit button based on form completion
    function updateSubmitButton() {
        const password = $('#{{ form.password.id_for_label }}').val();
        const isChecked = $('#{{ form.confirmation.id_for_label }}').is(':checked');
        
        $('#regenerateBtn').prop('disabled', !(password && isChecked));
    }
    
    // Check form fields
    $('#{{ form.confirmation.id_for_label }}, #{{ form.password.id_for_label }}').on('change input', updateSubmitButton);
    
    // Form validation and confirmation
    $('#regenerateForm').on('submit', function(e) {
        const password = $('#{{ form.password.id_for_label }}').val();
        const isChecked = $('#{{ form.confirmation.id_for_label }}').is(':checked');
        
        if (!password) {
            e.preventDefault();
            alert('Please enter your current password.');
            $('#{{ form.password.id_for_label }}').focus();
            return false;
        }
        
        if (!isChecked) {
            e.preventDefault();
            alert('Please confirm that you understand the consequences.');
            return false;
        }
        
        // Final confirmation
        if (!confirm('Are you sure you want to regenerate your backup codes? Your current codes will no longer work.')) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        $('#regenerateBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');
    });
});
</script>
{% endblock %}
