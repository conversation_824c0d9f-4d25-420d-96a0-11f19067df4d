from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from phonenumber_field.modelfields import PhoneNumberField
from django_countries.fields import CountryField
import uuid


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    for the sports betting platform
    """

    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Additional required fields
    email = models.EmailField(unique=True, help_text="User's email address")
    phone_number = PhoneNumberField(
        unique=True,
        help_text="User's phone number for M-Pesa and SMS verification"
    )

    # Personal Information
    date_of_birth = models.DateField(
        null=True,
        blank=True,
        help_text="User's date of birth for age verification"
    )
    country = CountryField(
        default='KE',
        help_text="User's country of residence"
    )
    city = models.CharField(
        max_length=100,
        blank=True,
        help_text="User's city"
    )

    # Account Status
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether the user has completed email/phone verification"
    )
    is_kyc_verified = models.BooleanField(
        default=False,
        help_text="Whether the user has completed KYC verification"
    )
    is_suspended = models.BooleanField(
        default=False,
        help_text="Whether the user account is suspended"
    )

    # Verification timestamps
    email_verified_at = models.DateTimeField(null=True, blank=True)
    phone_verified_at = models.DateTimeField(null=True, blank=True)
    kyc_verified_at = models.DateTimeField(null=True, blank=True)

    # Account settings
    preferred_language = models.CharField(
        max_length=10,
        choices=[
            ('en', 'English'),
            ('sw', 'Swahili'),
            ('fr', 'French'),
        ],
        default='en'
    )
    preferred_currency = models.CharField(
        max_length=3,
        choices=[
            ('KES', 'Kenyan Shilling'),
            ('USD', 'US Dollar'),
            ('EUR', 'Euro'),
            ('GHS', 'Ghanaian Cedi'),
            ('NGN', 'Nigerian Naira'),
        ],
        default='KES'
    )

    # Responsible gambling settings
    daily_deposit_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Daily deposit limit set by user"
    )
    daily_bet_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Daily betting limit set by user"
    )
    self_exclusion_until = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Self-exclusion end date"
    )

    # Marketing preferences
    marketing_emails = models.BooleanField(
        default=True,
        help_text="Whether user wants to receive marketing emails"
    )
    marketing_sms = models.BooleanField(
        default=True,
        help_text="Whether user wants to receive marketing SMS"
    )

    # Referral system
    referral_code = models.CharField(
        max_length=20,
        unique=True,
        blank=True,
        help_text="User's unique referral code"
    )
    referred_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='referrals',
        help_text="User who referred this user"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)

    # Override username requirement
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'phone_number']

    class Meta:
        db_table = 'accounts_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.email} ({self.username})"

    def save(self, *args, **kwargs):
        # Generate referral code if not exists
        if not self.referral_code:
            self.referral_code = self.generate_referral_code()
        super().save(*args, **kwargs)

    def generate_referral_code(self):
        """Generate a unique referral code for the user"""
        import random
        import string

        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            if not User.objects.filter(referral_code=code).exists():
                return code

    @property
    def full_name(self):
        """Return user's full name"""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def age(self):
        """Calculate user's age from date of birth"""
        if self.date_of_birth:
            today = timezone.now().date()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None

    @property
    def is_adult(self):
        """Check if user is 18 or older"""
        return self.age and self.age >= 18

    @property
    def can_bet(self):
        """Check if user can place bets"""
        return (
            self.is_verified and
            self.is_kyc_verified and
            not self.is_suspended and
            self.is_adult and
            (not self.self_exclusion_until or self.self_exclusion_until < timezone.now())
        )


class UserProfile(models.Model):
    """
    Extended user profile information
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')

    # Profile picture
    avatar = models.ImageField(
        upload_to='avatars/',
        null=True,
        blank=True,
        help_text="User's profile picture"
    )

    # Additional personal information
    gender = models.CharField(
        max_length=10,
        choices=[
            ('M', 'Male'),
            ('F', 'Female'),
            ('O', 'Other'),
            ('N', 'Prefer not to say'),
        ],
        blank=True
    )
    occupation = models.CharField(max_length=100, blank=True)
    address_line_1 = models.CharField(max_length=255, blank=True)
    address_line_2 = models.CharField(max_length=255, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)

    # Emergency contact
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = PhoneNumberField(blank=True)
    emergency_contact_relationship = models.CharField(max_length=50, blank=True)

    # Betting preferences
    favorite_sports = models.JSONField(
        default=list,
        help_text="List of user's favorite sports"
    )
    betting_experience = models.CharField(
        max_length=20,
        choices=[
            ('beginner', 'Beginner'),
            ('intermediate', 'Intermediate'),
            ('advanced', 'Advanced'),
            ('professional', 'Professional'),
        ],
        default='beginner'
    )

    # Additional profile information
    bio = models.TextField(
        max_length=500,
        blank=True,
        help_text="Brief description about yourself"
    )

    # Notification preferences
    email_notifications = models.BooleanField(
        default=True,
        help_text="Receive notifications via email"
    )
    sms_notifications = models.BooleanField(
        default=True,
        help_text="Receive notifications via SMS"
    )
    push_notifications = models.BooleanField(
        default=True,
        help_text="Receive push notifications"
    )
    marketing_emails = models.BooleanField(
        default=False,
        help_text="Receive marketing emails"
    )
    bet_notifications = models.BooleanField(
        default=True,
        help_text="Receive betting-related notifications"
    )
    promotion_notifications = models.BooleanField(
        default=True,
        help_text="Receive promotion notifications"
    )

    # Statistics
    total_deposits = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_withdrawals = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_bets_placed = models.PositiveIntegerField(default=0)
    total_bets_won = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounts_user_profile'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return f"Profile for {self.user.email}"

    @property
    def win_rate(self):
        """Calculate user's betting win rate"""
        if self.total_bets_placed > 0:
            return (self.total_bets_won / self.total_bets_placed) * 100
        return 0


class KYCDocument(models.Model):
    """
    KYC (Know Your Customer) document verification
    """
    DOCUMENT_TYPES = [
        ('national_id', 'National ID'),
        ('passport', 'Passport'),
        ('driving_license', 'Driving License'),
        ('utility_bill', 'Utility Bill'),
        ('bank_statement', 'Bank Statement'),
        ('selfie', 'Selfie with ID'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='kyc_documents')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES)
    document_number = models.CharField(max_length=50, blank=True)
    document_file = models.FileField(
        upload_to='kyc_documents/',
        help_text="Upload clear image of the document"
    )

    # Verification details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    verified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_documents'
    )
    verification_notes = models.TextField(blank=True)

    # Timestamps
    uploaded_at = models.DateTimeField(auto_now_add=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_kyc_document'
        verbose_name = 'KYC Document'
        verbose_name_plural = 'KYC Documents'
        unique_together = ['user', 'document_type']

    def __str__(self):
        return f"{self.user.email} - {self.get_document_type_display()}"


class UserSession(models.Model):
    """
    Track user sessions for security purposes
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    device_info = models.JSONField(default=dict)

    # Location information (optional)
    country = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100, blank=True)

    # Session status
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_user_session'
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.email} - {self.ip_address}"


class SecurityLog(models.Model):
    """
    Log security-related events
    """
    EVENT_TYPES = [
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('password_change', 'Password Change'),
        ('password_reset', 'Password Reset'),
        ('email_change', 'Email Change'),
        ('phone_change', 'Phone Change'),
        ('failed_login', 'Failed Login'),
        ('account_locked', 'Account Locked'),
        ('suspicious_activity', 'Suspicious Activity'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='security_logs',
        null=True,
        blank=True
    )
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    details = models.JSONField(default=dict)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'accounts_security_log'
        verbose_name = 'Security Log'
        verbose_name_plural = 'Security Logs'
        ordering = ['-created_at']

    def __str__(self):
        user_email = self.user.email if self.user else 'Anonymous'
        return f"{user_email} - {self.get_event_type_display()}"


class TwoFactorAuth(models.Model):
    """
    Two-Factor Authentication settings for users
    """
    METHOD_CHOICES = [
        ('sms', 'SMS'),
        ('totp', 'Authenticator App (TOTP)'),
        ('both', 'Both SMS and Authenticator'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='two_factor_auth'
    )

    # 2FA Settings
    is_enabled = models.BooleanField(
        default=False,
        help_text="Whether 2FA is enabled for this user"
    )
    method = models.CharField(
        max_length=10,
        choices=METHOD_CHOICES,
        default='sms',
        help_text="Preferred 2FA method"
    )

    # TOTP (Time-based One-Time Password) settings
    totp_secret = models.CharField(
        max_length=32,
        blank=True,
        help_text="Base32 encoded secret for TOTP"
    )
    totp_verified = models.BooleanField(
        default=False,
        help_text="Whether TOTP has been verified"
    )

    # SMS 2FA settings
    sms_enabled = models.BooleanField(
        default=True,
        help_text="Whether SMS 2FA is enabled"
    )

    # Backup codes
    backup_codes = models.JSONField(
        default=list,
        help_text="List of backup codes for account recovery"
    )
    backup_codes_used = models.JSONField(
        default=list,
        help_text="List of used backup codes"
    )

    # Timestamps
    enabled_at = models.DateTimeField(null=True, blank=True)
    last_used_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounts_two_factor_auth'
        verbose_name = 'Two-Factor Authentication'
        verbose_name_plural = 'Two-Factor Authentication'

    def __str__(self):
        return f"{self.user.email} - 2FA ({'Enabled' if self.is_enabled else 'Disabled'})"

    def generate_totp_secret(self):
        """Generate a new TOTP secret"""
        import pyotp
        self.totp_secret = pyotp.random_base32()
        return self.totp_secret

    def get_totp_uri(self):
        """Get TOTP URI for QR code generation"""
        if not self.totp_secret:
            self.generate_totp_secret()

        import pyotp
        totp = pyotp.TOTP(self.totp_secret)
        return totp.provisioning_uri(
            name=self.user.email,
            issuer_name="ZBet Sports Betting"
        )

    def verify_totp_code(self, code):
        """Verify TOTP code"""
        if not self.totp_secret:
            return False

        import pyotp
        totp = pyotp.TOTP(self.totp_secret)
        return totp.verify(code, valid_window=1)

    def generate_backup_codes(self, count=10):
        """Generate backup codes"""
        import secrets
        import string

        codes = []
        for _ in range(count):
            code = ''.join(secrets.choices(string.ascii_uppercase + string.digits, k=8))
            codes.append(code)

        self.backup_codes = codes
        self.backup_codes_used = []
        return codes

    def use_backup_code(self, code):
        """Use a backup code"""
        if code in self.backup_codes and code not in self.backup_codes_used:
            self.backup_codes_used.append(code)
            self.save()
            return True
        return False

    @property
    def available_backup_codes(self):
        """Get available backup codes"""
        return [code for code in self.backup_codes if code not in self.backup_codes_used]

    @property
    def backup_codes_remaining(self):
        """Get number of remaining backup codes"""
        return len(self.available_backup_codes)


class BetHistory(models.Model):
    """
    Placeholder model for betting history
    This will be moved to a dedicated betting app later
    """
    BET_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('cancelled', 'Cancelled'),
        ('cashout', 'Cashed Out'),
    ]

    BET_TYPE_CHOICES = [
        ('single', 'Single Bet'),
        ('multiple', 'Multiple Bet'),
        ('system', 'System Bet'),
        ('live', 'Live Bet'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bet_history'
    )

    # Bet details
    bet_id = models.CharField(max_length=20, unique=True)
    bet_type = models.CharField(max_length=20, choices=BET_TYPE_CHOICES, default='single')
    status = models.CharField(max_length=20, choices=BET_STATUS_CHOICES, default='pending')

    # Financial details
    stake_amount = models.DecimalField(max_digits=10, decimal_places=2)
    potential_win = models.DecimalField(max_digits=10, decimal_places=2)
    actual_win = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_odds = models.DecimalField(max_digits=10, decimal_places=2, default=1.0)

    # Bet details
    selections = models.JSONField(default=list, help_text="List of bet selections")
    match_details = models.JSONField(default=dict, help_text="Match and event details")

    # Timestamps
    placed_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_bet_history'
        ordering = ['-placed_at']
        verbose_name = 'Bet History'
        verbose_name_plural = 'Bet History'

    def __str__(self):
        return f"Bet {self.bet_id} - {self.user.email} - {self.status}"

    @property
    def is_active(self):
        """Check if bet is still active"""
        return self.status == 'pending'

    @property
    def profit_loss(self):
        """Calculate profit or loss"""
        if self.status == 'won':
            return self.actual_win - self.stake_amount
        elif self.status == 'lost':
            return -self.stake_amount
        return 0


class Transaction(models.Model):
    """
    Placeholder model for financial transactions
    This will be moved to a dedicated payments app later
    """
    TRANSACTION_TYPE_CHOICES = [
        ('deposit', 'Deposit'),
        ('withdrawal', 'Withdrawal'),
        ('bet_stake', 'Bet Stake'),
        ('bet_win', 'Bet Winnings'),
        ('bonus', 'Bonus'),
        ('refund', 'Refund'),
        ('fee', 'Fee'),
    ]

    TRANSACTION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('mpesa', 'M-Pesa'),
        ('bank', 'Bank Transfer'),
        ('card', 'Credit/Debit Card'),
        ('bonus', 'Bonus'),
        ('system', 'System'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='transactions'
    )

    # Transaction details
    transaction_id = models.CharField(max_length=50, unique=True)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=TRANSACTION_STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)

    # Financial details
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    balance_before = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    balance_after = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    # Additional details
    description = models.TextField(blank=True)
    reference = models.CharField(max_length=100, blank=True)
    external_reference = models.CharField(max_length=100, blank=True)
    metadata = models.JSONField(default=dict)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_transaction'
        ordering = ['-created_at']
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'

    def __str__(self):
        return f"{self.transaction_type.title()} - {self.amount} - {self.user.email}"

    @property
    def is_credit(self):
        """Check if transaction is a credit"""
        return self.transaction_type in ['deposit', 'bet_win', 'bonus', 'refund']

    @property
    def is_debit(self):
        """Check if transaction is a debit"""
        return self.transaction_type in ['withdrawal', 'bet_stake', 'fee']


class ResponsibleGambling(models.Model):
    """
    Responsible gambling settings and limits for users
    """
    LIMIT_PERIOD_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]

    SELF_EXCLUSION_PERIOD_CHOICES = [
        ('24h', '24 Hours'),
        ('48h', '48 Hours'),
        ('1w', '1 Week'),
        ('1m', '1 Month'),
        ('3m', '3 Months'),
        ('6m', '6 Months'),
        ('1y', '1 Year'),
        ('permanent', 'Permanent'),
    ]

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='responsible_gambling'
    )

    # Deposit limits
    daily_deposit_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum daily deposit amount"
    )
    weekly_deposit_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum weekly deposit amount"
    )
    monthly_deposit_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum monthly deposit amount"
    )

    # Betting limits
    daily_bet_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum daily betting amount"
    )
    weekly_bet_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum weekly betting amount"
    )
    monthly_bet_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum monthly betting amount"
    )

    # Loss limits
    daily_loss_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum daily loss amount"
    )
    weekly_loss_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum weekly loss amount"
    )
    monthly_loss_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum monthly loss amount"
    )

    # Session limits
    session_time_limit = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum session time in minutes"
    )
    daily_session_limit = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum daily session time in minutes"
    )

    # Self-exclusion
    is_self_excluded = models.BooleanField(
        default=False,
        help_text="Whether user is currently self-excluded"
    )
    self_exclusion_period = models.CharField(
        max_length=20,
        choices=SELF_EXCLUSION_PERIOD_CHOICES,
        null=True,
        blank=True
    )
    self_exclusion_start = models.DateTimeField(null=True, blank=True)
    self_exclusion_end = models.DateTimeField(null=True, blank=True)
    self_exclusion_reason = models.TextField(blank=True)

    # Reality checks
    reality_check_enabled = models.BooleanField(
        default=True,
        help_text="Show reality check reminders"
    )
    reality_check_interval = models.PositiveIntegerField(
        default=60,
        help_text="Reality check interval in minutes"
    )

    # Account restrictions
    account_restricted = models.BooleanField(
        default=False,
        help_text="Whether account has restrictions"
    )
    restriction_reason = models.TextField(blank=True)
    restriction_end = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounts_responsible_gambling'
        verbose_name = 'Responsible Gambling Settings'
        verbose_name_plural = 'Responsible Gambling Settings'

    def __str__(self):
        return f"RG Settings - {self.user.email}"

    @property
    def is_currently_excluded(self):
        """Check if user is currently self-excluded"""
        if not self.is_self_excluded:
            return False

        if self.self_exclusion_period == 'permanent':
            return True

        if self.self_exclusion_end:
            from django.utils import timezone
            return timezone.now() < self.self_exclusion_end

        return False

    def get_remaining_exclusion_time(self):
        """Get remaining self-exclusion time"""
        if not self.is_currently_excluded:
            return None

        if self.self_exclusion_period == 'permanent':
            return 'Permanent'

        if self.self_exclusion_end:
            from django.utils import timezone
            remaining = self.self_exclusion_end - timezone.now()
            if remaining.days > 0:
                return f"{remaining.days} days"
            elif remaining.seconds > 3600:
                hours = remaining.seconds // 3600
                return f"{hours} hours"
            else:
                minutes = remaining.seconds // 60
                return f"{minutes} minutes"

        return None


class UserDevice(models.Model):
    """
    Track user devices for security purposes
    """
    DEVICE_TYPE_CHOICES = [
        ('desktop', 'Desktop'),
        ('mobile', 'Mobile'),
        ('tablet', 'Tablet'),
        ('unknown', 'Unknown'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='devices'
    )

    # Device identification
    device_id = models.CharField(max_length=100, unique=True)
    device_name = models.CharField(max_length=200, blank=True)
    device_type = models.CharField(max_length=20, choices=DEVICE_TYPE_CHOICES, default='unknown')

    # Browser/App information
    user_agent = models.TextField()
    browser_name = models.CharField(max_length=100, blank=True)
    browser_version = models.CharField(max_length=50, blank=True)
    os_name = models.CharField(max_length=100, blank=True)
    os_version = models.CharField(max_length=50, blank=True)

    # Location information
    ip_address = models.GenericIPAddressField()
    country = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100, blank=True)

    # Security flags
    is_trusted = models.BooleanField(default=False)
    is_blocked = models.BooleanField(default=False)

    # Timestamps
    first_seen = models.DateTimeField(auto_now_add=True)
    last_seen = models.DateTimeField(auto_now=True)
    last_login = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_user_device'
        ordering = ['-last_seen']
        verbose_name = 'User Device'
        verbose_name_plural = 'User Devices'

    def __str__(self):
        return f"{self.user.email} - {self.device_name or self.device_id}"

    @property
    def is_current_session(self):
        """Check if this device has an active session"""
        return UserSession.objects.filter(
            user=self.user,
            ip_address=self.ip_address,
            is_active=True
        ).exists()


class LoginAttempt(models.Model):
    """
    Track login attempts for rate limiting and security
    """
    STATUS_CHOICES = [
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('blocked', 'Blocked'),
    ]

    # User information (can be null for failed attempts)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='login_attempts'
    )

    # Attempt details
    username_attempted = models.CharField(max_length=150)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)

    # Failure details
    failure_reason = models.CharField(max_length=200, blank=True)

    # Timestamps
    attempted_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'accounts_login_attempt'
        ordering = ['-attempted_at']
        verbose_name = 'Login Attempt'
        verbose_name_plural = 'Login Attempts'
        indexes = [
            models.Index(fields=['ip_address', 'attempted_at']),
            models.Index(fields=['username_attempted', 'attempted_at']),
        ]

    def __str__(self):
        return f"{self.username_attempted} - {self.status} - {self.attempted_at}"


class SuspiciousActivity(models.Model):
    """
    Track suspicious activities for security monitoring
    """
    ACTIVITY_TYPE_CHOICES = [
        ('multiple_failed_logins', 'Multiple Failed Logins'),
        ('login_from_new_location', 'Login from New Location'),
        ('unusual_betting_pattern', 'Unusual Betting Pattern'),
        ('rapid_transactions', 'Rapid Transactions'),
        ('account_takeover_attempt', 'Account Takeover Attempt'),
        ('bot_activity', 'Bot Activity'),
        ('vpn_usage', 'VPN Usage'),
        ('multiple_accounts', 'Multiple Accounts'),
    ]

    RISK_LEVEL_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('open', 'Open'),
        ('investigating', 'Investigating'),
        ('resolved', 'Resolved'),
        ('false_positive', 'False Positive'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='suspicious_activities'
    )

    # Activity details
    activity_type = models.CharField(max_length=50, choices=ACTIVITY_TYPE_CHOICES)
    risk_level = models.CharField(max_length=20, choices=RISK_LEVEL_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')

    # Details
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    metadata = models.JSONField(default=dict)

    # Investigation
    investigated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='investigated_activities'
    )
    investigation_notes = models.TextField(blank=True)

    # Timestamps
    detected_at = models.DateTimeField(auto_now_add=True)
    investigated_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'accounts_suspicious_activity'
        ordering = ['-detected_at']
        verbose_name = 'Suspicious Activity'
        verbose_name_plural = 'Suspicious Activities'

    def __str__(self):
        return f"{self.user.email} - {self.get_activity_type_display()} - {self.risk_level}"
