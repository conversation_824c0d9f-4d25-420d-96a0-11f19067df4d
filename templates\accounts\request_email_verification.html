{% extends 'base.html' %}

{% block title %}Request Email Verification - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Verify Your Email</h3>
                <p class="mb-0">Request email verification</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-envelope-open fa-3x text-warning mb-3"></i>
                    <h5>Email Not Verified</h5>
                    <p class="text-muted">
                        Your email address is not yet verified. Please verify your email to access all features.
                    </p>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Why verify your email?</h6>
                    <ul class="list-unstyled mb-0">
                        <li>✓ Secure your account</li>
                        <li>✓ Receive important notifications</li>
                        <li>✓ Enable password recovery</li>
                        <li>✓ Access all betting features</li>
                    </ul>
                </div>
                
                <form method="post" id="requestVerificationForm">
                    {% csrf_token %}
                    
                    <!-- Email (readonly) -->
                    <div class="form-floating mb-3">
                        {{ form.email }}
                        <label for="{{ form.email.id_for_label }}">Email Address</label>
                        <div class="form-text">{{ form.email.help_text }}</div>
                        {% if form.email.errors %}
                            <div class="text-danger small">{{ form.email.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Send Verification Code
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-0">Need to change your email?</p>
                    <a href="{% url 'accounts:change_email' %}" class="btn btn-outline-secondary">
                        Change Email Address
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Form submission
    $('#requestVerificationForm').on('submit', function(e) {
        const btn = $(this).find('button[type="submit"]');
        btn.prop('disabled', true).text('Sending...');
        
        // Re-enable button after 3 seconds to prevent multiple rapid submissions
        setTimeout(function() {
            btn.prop('disabled', false).text('Send Verification Code');
        }, 3000);
    });
});
</script>
{% endblock %}
