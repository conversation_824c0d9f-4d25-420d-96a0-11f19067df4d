# Generated by Django 5.2.4 on 2025-07-07 11:20

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0003_airteltransaction_banktransaction_cardtransaction"),
    ]

    operations = [
        migrations.AddField(
            model_name="deposit",
            name="receipt_generated",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="deposit",
            name="receipt_generated_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="wallet",
            name="low_balance_alert_sent",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="wallet",
            name="low_balance_threshold",
            field=models.DecimalField(
                decimal_places=2,
                default=Decimal("100.00"),
                help_text="Threshold below which low balance alerts are sent",
                max_digits=12,
            ),
        ),
        migrations.AddField(
            model_name="withdrawal",
            name="receipt_generated",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="withdrawal",
            name="receipt_generated_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="paymentnotification",
            name="notification_type",
            field=models.CharField(
                choices=[
                    ("DEPOSIT_SUCCESS", "Deposit Successful"),
                    ("DEPOSIT_FAILED", "Deposit Failed"),
                    ("WITHDRAWAL_SUCCESS", "Withdrawal Successful"),
                    ("WITHDRAWAL_FAILED", "Withdrawal Failed"),
                    ("LOW_BALANCE", "Low Balance Alert"),
                    ("LIMIT_EXCEEDED", "Limit Exceeded"),
                    ("BALANCE_UPDATE", "Balance Update"),
                    ("BALANCE_CREDITED", "Balance Credited"),
                    ("BALANCE_DEBITED", "Balance Debited"),
                ],
                max_length=20,
            ),
        ),
    ]
