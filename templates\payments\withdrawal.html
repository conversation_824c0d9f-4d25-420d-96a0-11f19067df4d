{% extends 'base.html' %}
{% load static %}

{% block title %}Withdraw Funds - ZBet{% endblock %}

{% block extra_css %}
<style>
    .withdrawal-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .withdrawal-header {
        background: linear-gradient(135deg, #1a1a2e, #2d3748);
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .withdrawal-title {
        color: white;
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .withdrawal-subtitle {
        color: #a0aec0;
        font-size: 16px;
    }
    
    .balance-display {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .balance-label {
        color: #a0aec0;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .balance-amount {
        color: #48bb78;
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .balance-updated {
        color: #a0aec0;
        font-size: 12px;
    }
    
    .withdrawal-form {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .form-section {
        margin-bottom: 30px;
    }
    
    .section-title {
        color: white;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #2d3748;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        color: #a0aec0;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-input {
        width: 100%;
        background: #2d3748;
        border: 1px solid #4a5568;
        border-radius: 8px;
        padding: 12px 15px;
        color: white;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
    
    .form-input.error {
        border-color: #f56565;
        box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
    }
    
    .form-error {
        color: #f56565;
        font-size: 12px;
        margin-top: 5px;
    }
    
    .form-help {
        color: #a0aec0;
        font-size: 12px;
        margin-top: 5px;
    }
    
    .payment-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .payment-method {
        background: #2d3748;
        border: 2px solid #4a5568;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .payment-method:hover {
        border-color: #4299e1;
        background: rgba(66, 153, 225, 0.1);
    }
    
    .payment-method.selected {
        border-color: #4299e1;
        background: rgba(66, 153, 225, 0.2);
    }
    
    .payment-method-icon {
        font-size: 32px;
        margin-bottom: 10px;
    }
    
    .payment-method-name {
        color: white;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .payment-method-desc {
        color: #a0aec0;
        font-size: 12px;
    }
    
    .amount-presets {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .amount-preset {
        background: #2d3748;
        border: 1px solid #4a5568;
        border-radius: 8px;
        padding: 10px;
        color: white;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .amount-preset:hover {
        border-color: #4299e1;
        background: rgba(66, 153, 225, 0.1);
    }
    
    .withdrawal-summary {
        background: #2d3748;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        color: #a0aec0;
    }
    
    .summary-row.total {
        border-top: 1px solid #4a5568;
        padding-top: 10px;
        margin-top: 15px;
        color: white;
        font-weight: bold;
        font-size: 18px;
    }
    
    .submit-btn {
        width: 100%;
        background: linear-gradient(135deg, #f56565, #e53e3e);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 15px 30px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 20px;
    }
    
    .submit-btn:hover {
        background: linear-gradient(135deg, #e53e3e, #c53030);
        transform: translateY(-2px);
    }
    
    .submit-btn:disabled {
        background: #4a5568;
        cursor: not-allowed;
        transform: none;
    }
    
    .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 10px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .limits-info {
        background: rgba(66, 153, 225, 0.1);
        border: 1px solid #4299e1;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .limits-title {
        color: #4299e1;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .limits-text {
        color: #a0aec0;
        font-size: 14px;
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background: rgba(72, 187, 120, 0.1);
        border: 1px solid #48bb78;
        color: #48bb78;
    }
    
    .alert-error {
        background: rgba(245, 101, 101, 0.1);
        border: 1px solid #f56565;
        color: #f56565;
    }
    
    .alert-warning {
        background: rgba(237, 137, 54, 0.1);
        border: 1px solid #ed8936;
        color: #ed8936;
    }
    
    @media (max-width: 768px) {
        .withdrawal-container {
            padding: 15px;
        }
        
        .payment-methods {
            grid-template-columns: 1fr;
        }
        
        .amount-presets {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="withdrawal-container">
    <!-- Header -->
    <div class="withdrawal-header">
        <h1 class="withdrawal-title">Withdraw Funds</h1>
        <p class="withdrawal-subtitle">Request a withdrawal to your M-Pesa account</p>
    </div>
    
    <!-- Balance Display -->
    <div class="balance-display">
        <div class="balance-label">Available Balance</div>
        <div class="balance-amount" id="current-balance">KES {{ wallet.balance|floatformat:2 }}</div>
        <div class="balance-updated" id="balance-updated">Last updated: {{ wallet.last_updated|date:"M d, Y H:i" }}</div>
    </div>
    
    <!-- Alerts -->
    <div id="alert-container"></div>
    
    <!-- Withdrawal Form -->
    <div class="withdrawal-form">
        <form id="withdrawal-form" method="POST">
            {% csrf_token %}
            
            <!-- Payment Method Selection -->
            <div class="form-section">
                <h3 class="section-title">Select Payment Method</h3>
                <div class="payment-methods">
                    {% for method in payment_methods %}
                    <div class="payment-method" data-method="{{ method.code }}" onclick="selectPaymentMethod('{{ method.code }}')">
                        <div class="payment-method-icon">
                            {% if method.code == 'MPESA' %}📱
                            {% elif method.code == 'AIRTEL' %}📞
                            {% else %}💳{% endif %}
                        </div>
                        <div class="payment-method-name">{{ method.name }}</div>
                        <div class="payment-method-desc">{{ method.description }}</div>
                    </div>
                    {% endfor %}
                </div>
                <input type="hidden" name="payment_method" id="selected-payment-method" required>
            </div>

            <!-- M-Pesa Phone Number (shown when M-Pesa is selected) -->
            <div class="form-section" id="mpesa-section" style="display: none;">
                <h3 class="section-title">M-Pesa Details</h3>
                <div class="form-group">
                    <label class="form-label" for="phone-number">M-Pesa Phone Number</label>
                    <input type="tel" name="phone_number" id="phone-number" class="form-input"
                           placeholder="254712345678" maxlength="12">
                    <div class="form-help">Enter your M-Pesa registered phone number (format: 254XXXXXXXXX)</div>
                    <div class="form-error" id="phone-error"></div>
                </div>
            </div>

            <!-- Withdrawal Amount -->
            <div class="form-section">
                <h3 class="section-title">Withdrawal Amount</h3>

                <!-- Limits Info -->
                <div class="limits-info" id="limits-info" style="display: none;">
                    <div class="limits-title">Withdrawal Limits</div>
                    <div class="limits-text" id="limits-text"></div>
                </div>

                <!-- Amount Presets -->
                <div class="amount-presets">
                    <div class="amount-preset" onclick="setAmount(100)">KES 100</div>
                    <div class="amount-preset" onclick="setAmount(500)">KES 500</div>
                    <div class="amount-preset" onclick="setAmount(1000)">KES 1,000</div>
                    <div class="amount-preset" onclick="setAmount(2000)">KES 2,000</div>
                    <div class="amount-preset" onclick="setAmount(5000)">KES 5,000</div>
                    <div class="amount-preset" onclick="setAmount(10000)">KES 10,000</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="amount">Amount (KES)</label>
                    <input type="number" name="amount" id="amount" class="form-input"
                           placeholder="Enter amount" min="1" step="1" required>
                    <div class="form-help">Enter the amount you want to withdraw</div>
                    <div class="form-error" id="amount-error"></div>
                </div>
            </div>

            <!-- Withdrawal Summary -->
            <div class="withdrawal-summary" id="withdrawal-summary" style="display: none;">
                <div class="summary-row">
                    <span>Withdrawal Amount:</span>
                    <span id="summary-amount">KES 0.00</span>
                </div>
                <div class="summary-row">
                    <span>Transaction Fee:</span>
                    <span id="summary-fee">KES 0.00</span>
                </div>
                <div class="summary-row total">
                    <span>You will receive:</span>
                    <span id="summary-total">KES 0.00</span>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn" id="submit-btn" disabled>
                <span class="loading-spinner" id="loading-spinner"></span>
                <span id="submit-text">Request Withdrawal</span>
            </button>
        </form>
    </div>

    <!-- Recent Withdrawals -->
    <div class="withdrawal-form">
        <h3 class="section-title">Recent Withdrawals</h3>
        <div id="recent-withdrawals">
            <!-- Recent withdrawals will be loaded here -->
        </div>
        <div style="text-align: center; margin-top: 20px;">
            <a href="{% url 'payments:withdrawal_history' %}" class="submit-btn" style="display: inline-block; width: auto; padding: 10px 20px; font-size: 14px; text-decoration: none;">
                View All Withdrawals
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedPaymentMethod = null;
let paymentLimits = {};
let currentBalance = {{ wallet.balance }};

// Payment method selection
function selectPaymentMethod(methodCode) {
    selectedPaymentMethod = methodCode;

    // Update UI
    document.querySelectorAll('.payment-method').forEach(el => {
        el.classList.remove('selected');
    });
    document.querySelector(`[data-method="${methodCode}"]`).classList.add('selected');
    document.getElementById('selected-payment-method').value = methodCode;

    // Show/hide M-Pesa section
    const mpesaSection = document.getElementById('mpesa-section');
    if (methodCode === 'MPESA') {
        mpesaSection.style.display = 'block';
    } else {
        mpesaSection.style.display = 'none';
    }

    // Load payment limits
    loadPaymentLimits(methodCode);

    // Validate form
    validateForm();
}

// Load payment limits for selected method
function loadPaymentLimits(methodCode) {
    fetch(`/payments/api/payment-limits/${methodCode}/`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            paymentLimits = data.limits;
            updateLimitsDisplay();
        }
    })
    .catch(error => {
        console.error('Error loading payment limits:', error);
    });
}

// Update limits display
function updateLimitsDisplay() {
    const limitsInfo = document.getElementById('limits-info');
    const limitsText = document.getElementById('limits-text');

    if (paymentLimits.min_withdrawal && paymentLimits.max_withdrawal) {
        limitsText.textContent = `Minimum: KES ${paymentLimits.min_withdrawal}, Maximum: KES ${paymentLimits.max_withdrawal}`;
        limitsInfo.style.display = 'block';
    } else {
        limitsInfo.style.display = 'none';
    }
}

// Set amount from presets
function setAmount(amount) {
    document.getElementById('amount').value = amount;
    calculateWithdrawal();
    validateForm();
}

// Calculate withdrawal fees and summary
function calculateWithdrawal() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;

    if (amount > 0 && selectedPaymentMethod) {
        // Calculate fee (you can customize this logic)
        let fee = 0;
        if (selectedPaymentMethod === 'MPESA') {
            if (amount <= 100) fee = 0;
            else if (amount <= 500) fee = 5;
            else if (amount <= 1000) fee = 10;
            else if (amount <= 5000) fee = 15;
            else fee = 25;
        }

        const netAmount = amount - fee;

        // Update summary
        document.getElementById('summary-amount').textContent = `KES ${amount.toFixed(2)}`;
        document.getElementById('summary-fee').textContent = `KES ${fee.toFixed(2)}`;
        document.getElementById('summary-total').textContent = `KES ${netAmount.toFixed(2)}`;
        document.getElementById('withdrawal-summary').style.display = 'block';
    } else {
        document.getElementById('withdrawal-summary').style.display = 'none';
    }
}

// Validate form
function validateForm() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const phoneNumber = document.getElementById('phone-number').value;
    const submitBtn = document.getElementById('submit-btn');

    let isValid = true;

    // Clear previous errors
    document.querySelectorAll('.form-error').forEach(el => el.textContent = '');
    document.querySelectorAll('.form-input').forEach(el => el.classList.remove('error'));

    // Validate payment method
    if (!selectedPaymentMethod) {
        isValid = false;
    }

    // Validate phone number for M-Pesa
    if (selectedPaymentMethod === 'MPESA') {
        if (!phoneNumber || !phoneNumber.match(/^254[0-9]{9}$/)) {
            document.getElementById('phone-error').textContent = 'Please enter a valid M-Pesa phone number (254XXXXXXXXX)';
            document.getElementById('phone-number').classList.add('error');
            isValid = false;
        }
    }

    // Validate amount
    if (amount <= 0) {
        document.getElementById('amount-error').textContent = 'Please enter a valid amount';
        document.getElementById('amount').classList.add('error');
        isValid = false;
    } else if (amount > currentBalance) {
        document.getElementById('amount-error').textContent = 'Insufficient balance';
        document.getElementById('amount').classList.add('error');
        isValid = false;
    } else if (paymentLimits.min_withdrawal && amount < paymentLimits.min_withdrawal) {
        document.getElementById('amount-error').textContent = `Minimum withdrawal amount is KES ${paymentLimits.min_withdrawal}`;
        document.getElementById('amount').classList.add('error');
        isValid = false;
    } else if (paymentLimits.max_withdrawal && amount > paymentLimits.max_withdrawal) {
        document.getElementById('amount-error').textContent = `Maximum withdrawal amount is KES ${paymentLimits.max_withdrawal}`;
        document.getElementById('amount').classList.add('error');
        isValid = false;
    }

    submitBtn.disabled = !isValid;
}

// Show alert
function showAlert(message, type = 'error') {
    const alertContainer = document.getElementById('alert-container');
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;

    alertContainer.innerHTML = '';
    alertContainer.appendChild(alert);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

// Form submission
document.getElementById('withdrawal-form').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!validateForm()) {
        return;
    }

    const submitBtn = document.getElementById('submit-btn');
    const loadingSpinner = document.getElementById('loading-spinner');
    const submitText = document.getElementById('submit-text');

    // Show loading state
    submitBtn.disabled = true;
    loadingSpinner.style.display = 'inline-block';
    submitText.textContent = 'Processing...';

    // Submit form
    const formData = new FormData(this);

    fetch('{% url "payments:withdrawal_request" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('Withdrawal request submitted successfully! You will receive an SMS confirmation shortly.', 'success');

            // Reset form
            document.getElementById('withdrawal-form').reset();
            selectedPaymentMethod = null;
            document.querySelectorAll('.payment-method').forEach(el => el.classList.remove('selected'));
            document.getElementById('mpesa-section').style.display = 'none';
            document.getElementById('withdrawal-summary').style.display = 'none';

            // Reload recent withdrawals
            loadRecentWithdrawals();

            // Update balance
            updateWalletBalance();
        } else {
            showAlert(data.message || 'Withdrawal request failed. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Network error. Please check your connection and try again.');
    })
    .finally(() => {
        // Reset loading state
        submitBtn.disabled = false;
        loadingSpinner.style.display = 'none';
        submitText.textContent = 'Request Withdrawal';
        validateForm();
    });
});

// Event listeners
document.getElementById('amount').addEventListener('input', function() {
    calculateWithdrawal();
    validateForm();
});

document.getElementById('phone-number').addEventListener('input', validateForm);

// Load recent withdrawals
function loadRecentWithdrawals() {
    fetch('/payments/api/recent-withdrawals/')
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            const container = document.getElementById('recent-withdrawals');
            if (data.withdrawals.length === 0) {
                container.innerHTML = '<p style="color: #a0aec0; text-align: center;">No recent withdrawals</p>';
            } else {
                container.innerHTML = data.withdrawals.map(w => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #2d3748;">
                        <div>
                            <div style="color: white; font-weight: 600;">KES ${w.amount}</div>
                            <div style="color: #a0aec0; font-size: 12px;">${w.created_at}</div>
                        </div>
                        <div>
                            <span class="status-badge status-${w.status.toLowerCase()}">${w.status}</span>
                        </div>
                    </div>
                `).join('');
            }
        }
    })
    .catch(error => {
        console.error('Error loading recent withdrawals:', error);
    });
}

// Update wallet balance
function updateWalletBalance() {
    fetch('/payments/api/wallet/balance/')
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            currentBalance = data.balance;
            document.getElementById('current-balance').textContent = data.formatted_balance;
            document.getElementById('balance-updated').textContent = `Last updated: ${new Date().toLocaleString()}`;
        }
    })
    .catch(error => {
        console.error('Error updating balance:', error);
    });
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    loadRecentWithdrawals();

    // Auto-select M-Pesa if it's the only/primary method
    const mpesaMethod = document.querySelector('[data-method="MPESA"]');
    if (mpesaMethod) {
        selectPaymentMethod('MPESA');
    }
});
</script>
{% endblock %}
