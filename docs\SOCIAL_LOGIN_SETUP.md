# Social Media Login Setup Guide

This guide explains how to set up Google and Facebook OAuth2 authentication for the ZBet platform.

## Overview

The ZBet platform supports social media login using:
- **Google OAuth2** - Sign in with Google accounts
- **Facebook Login** - Sign in with Facebook accounts

## Features

### ✅ Implemented Features
- OAuth2 authentication with Google and Facebook
- Automatic account linking for existing users
- Social account management dashboard
- Integration with existing 2FA system
- Custom adapters for enhanced security
- Automatic email verification for social logins
- Security event logging

### 🔧 Technical Implementation
- **Django Allauth** - OAuth2 provider integration
- **Custom Adapters** - Enhanced security and user experience
- **2FA Integration** - Social logins respect 2FA settings
- **Account Linking** - Connect social accounts to existing users

## Setup Instructions

### 1. Google OAuth2 Setup

#### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API

#### Step 2: Create OAuth2 Credentials
1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Choose **Web application**
4. Add authorized redirect URIs:
   - Development: `http://localhost:8000/accounts/google/login/callback/`
   - Production: `https://yourdomain.com/accounts/google/login/callback/`

#### Step 3: Configure Environment Variables
```bash
# Add to your .env file
GOOGLE_OAUTH2_CLIENT_ID=your_google_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_google_client_secret_here
```

### 2. Facebook OAuth2 Setup

#### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Choose **Consumer** app type

#### Step 2: Configure Facebook Login
1. Add **Facebook Login** product to your app
2. Configure Valid OAuth Redirect URIs:
   - Development: `http://localhost:8000/accounts/facebook/login/callback/`
   - Production: `https://yourdomain.com/accounts/facebook/login/callback/`

#### Step 3: Configure Environment Variables
```bash
# Add to your .env file
FACEBOOK_APP_ID=your_facebook_app_id_here
FACEBOOK_APP_SECRET=your_facebook_app_secret_here
```

### 3. Django Setup

#### Step 1: Run Setup Command
```bash
python manage.py setup_social_apps
```

#### Step 2: Verify Configuration
1. Check Django admin at `/admin/socialaccount/socialapp/`
2. Ensure Google and Facebook apps are created
3. Verify site associations

## Usage

### User Experience

#### Login Flow
1. User clicks "Continue with Google/Facebook" on login page
2. Redirected to OAuth provider for authentication
3. If 2FA is enabled, user completes 2FA verification
4. User is logged in and redirected to dashboard

#### Registration Flow
1. User clicks social login button on registration page
2. OAuth authentication with provider
3. Account created automatically with provider data
4. Email marked as verified for social accounts

#### Account Linking
1. Existing users can link social accounts in dashboard
2. Go to **Social Connections** in user dashboard
3. Click **Connect** for desired provider
4. Complete OAuth flow to link account

### Security Features

#### Two-Factor Authentication
- Social logins respect existing 2FA settings
- Users with 2FA enabled must complete verification after OAuth
- SMS codes sent automatically for SMS 2FA users

#### Account Security
- Email verification automatic for social logins
- Security events logged for all social authentication
- Account linking prevents duplicate accounts

#### Error Handling
- Graceful handling of OAuth errors
- User-friendly error messages
- Automatic fallback to regular login

## API Endpoints

### Social Authentication URLs
- Google Login: `/accounts/google/login/`
- Facebook Login: `/accounts/facebook/login/`
- Social Connections: `/accounts/social-connections/`

### Callback URLs
- Google Callback: `/accounts/google/login/callback/`
- Facebook Callback: `/accounts/facebook/login/callback/`

## Customization

### Custom Adapters
The platform uses custom adapters for enhanced functionality:

#### CustomAccountAdapter
- Handles 2FA integration during login
- Custom redirect logic
- Enhanced user data processing

#### CustomSocialAccountAdapter
- Automatic account linking by email
- Enhanced user profile population
- Security event logging
- Error handling

### Template Customization
Social login buttons are integrated into:
- Login page (`templates/accounts/login.html`)
- Registration page (`templates/accounts/register.html`)
- Social connections page (`templates/accounts/social_connections.html`)

## Troubleshooting

### Common Issues

#### OAuth Redirect URI Mismatch
- Ensure redirect URIs match exactly in provider settings
- Check for trailing slashes and protocol (http/https)

#### Missing Credentials
- Verify environment variables are set correctly
- Run `python manage.py setup_social_apps` after adding credentials

#### 2FA Not Working with Social Login
- Check that custom adapters are configured in settings
- Verify 2FA service is properly integrated

### Debug Mode
Enable debug logging for OAuth issues:
```python
LOGGING = {
    'loggers': {
        'allauth': {
            'level': 'DEBUG',
        },
    },
}
```

## Production Considerations

### Security
- Use HTTPS in production
- Secure OAuth credentials
- Regular security audits
- Monitor failed authentication attempts

### Performance
- Cache social account data
- Optimize database queries
- Monitor OAuth provider rate limits

### Monitoring
- Track social login success rates
- Monitor OAuth errors
- Log security events

## Support

For issues with social media login:
1. Check provider developer documentation
2. Verify OAuth application settings
3. Review Django logs for errors
4. Test with different browsers/devices
