# Generated by Django 5.2.4 on 2025-07-05 19:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0004_bethistory_transaction"),
    ]

    operations = [
        migrations.CreateModel(
            name="ResponsibleGambling",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "daily_deposit_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum daily deposit amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "weekly_deposit_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum weekly deposit amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "monthly_deposit_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum monthly deposit amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "daily_bet_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum daily betting amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "weekly_bet_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum weekly betting amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "monthly_bet_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum monthly betting amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "daily_loss_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum daily loss amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "weekly_loss_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum weekly loss amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "monthly_loss_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum monthly loss amount",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "session_time_limit",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum session time in minutes",
                        null=True,
                    ),
                ),
                (
                    "daily_session_limit",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum daily session time in minutes",
                        null=True,
                    ),
                ),
                (
                    "is_self_excluded",
                    models.BooleanField(
                        default=False,
                        help_text="Whether user is currently self-excluded",
                    ),
                ),
                (
                    "self_exclusion_period",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("24h", "24 Hours"),
                            ("48h", "48 Hours"),
                            ("1w", "1 Week"),
                            ("1m", "1 Month"),
                            ("3m", "3 Months"),
                            ("6m", "6 Months"),
                            ("1y", "1 Year"),
                            ("permanent", "Permanent"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                ("self_exclusion_start", models.DateTimeField(blank=True, null=True)),
                ("self_exclusion_end", models.DateTimeField(blank=True, null=True)),
                ("self_exclusion_reason", models.TextField(blank=True)),
                (
                    "reality_check_enabled",
                    models.BooleanField(
                        default=True, help_text="Show reality check reminders"
                    ),
                ),
                (
                    "reality_check_interval",
                    models.PositiveIntegerField(
                        default=60, help_text="Reality check interval in minutes"
                    ),
                ),
                (
                    "account_restricted",
                    models.BooleanField(
                        default=False, help_text="Whether account has restrictions"
                    ),
                ),
                ("restriction_reason", models.TextField(blank=True)),
                ("restriction_end", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="responsible_gambling",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Responsible Gambling Settings",
                "verbose_name_plural": "Responsible Gambling Settings",
                "db_table": "accounts_responsible_gambling",
            },
        ),
    ]
