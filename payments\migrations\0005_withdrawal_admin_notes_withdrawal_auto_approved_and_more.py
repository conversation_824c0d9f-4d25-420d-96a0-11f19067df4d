# Generated by Django 5.2.4 on 2025-07-08 09:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0004_deposit_receipt_generated_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="withdrawal",
            name="admin_notes",
            field=models.TextField(
                blank=True, help_text="Internal notes for admin use", null=True
            ),
        ),
        migrations.AddField(
            model_name="withdrawal",
            name="auto_approved",
            field=models.Bo<PERSON>anField(
                default=False, help_text="Whether this withdrawal was auto-approved"
            ),
        ),
        migrations.AddField(
            model_name="withdrawal",
            name="rejected_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="withdrawal",
            name="rejected_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="rejected_withdrawals",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="withdrawal",
            name="rejection_reason",
            field=models.TextField(blank=True, null=True),
        ),
    ]
