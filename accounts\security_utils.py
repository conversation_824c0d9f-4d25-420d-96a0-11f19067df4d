"""
Security utilities for user management
"""
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from datetime import timedelta
import hashlib
import re
from .models import LoginAttempt, UserDevice, SuspiciousActivity


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_device_fingerprint(request):
    """Generate device fingerprint from request"""
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    ip_address = get_client_ip(request)
    accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
    
    # Create fingerprint
    fingerprint_data = f"{user_agent}:{ip_address}:{accept_language}"
    return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]


def parse_user_agent(user_agent):
    """Parse user agent string to extract device information (simplified)"""
    user_agent = user_agent.lower()

    # Detect browser
    browser_name = 'Unknown'
    if 'chrome' in user_agent:
        browser_name = 'Chrome'
    elif 'firefox' in user_agent:
        browser_name = 'Firefox'
    elif 'safari' in user_agent and 'chrome' not in user_agent:
        browser_name = 'Safari'
    elif 'edge' in user_agent:
        browser_name = 'Edge'

    # Detect OS
    os_name = 'Unknown'
    if 'windows' in user_agent:
        os_name = 'Windows'
    elif 'mac' in user_agent:
        os_name = 'macOS'
    elif 'linux' in user_agent:
        os_name = 'Linux'
    elif 'android' in user_agent:
        os_name = 'Android'
    elif 'ios' in user_agent or 'iphone' in user_agent or 'ipad' in user_agent:
        os_name = 'iOS'

    # Detect device type
    device_type = 'desktop'
    if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
        device_type = 'mobile'
    elif 'tablet' in user_agent or 'ipad' in user_agent:
        device_type = 'tablet'

    return {
        'browser_name': browser_name,
        'browser_version': 'Unknown',
        'os_name': os_name,
        'os_version': 'Unknown',
        'device_type': device_type,
        'device_name': f"{browser_name} on {os_name}"
    }


def track_user_device(user, request):
    """Track and update user device information"""
    ip_address = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    device_id = get_device_fingerprint(request)
    
    # Parse user agent
    device_info = parse_user_agent(user_agent)
    
    # Get or create device
    device, created = UserDevice.objects.get_or_create(
        device_id=device_id,
        defaults={
            'user': user,
            'ip_address': ip_address,
            'user_agent': user_agent,
            **device_info
        }
    )
    
    # Update device information
    if not created:
        device.user = user
        device.ip_address = ip_address
        device.user_agent = user_agent
        device.last_login = timezone.now()
        device.save()
    
    return device


def check_rate_limit(identifier, limit=5, window=300):
    """
    Check if rate limit is exceeded
    
    Args:
        identifier: Unique identifier (IP, username, etc.)
        limit: Maximum attempts allowed
        window: Time window in seconds
    
    Returns:
        tuple: (is_allowed, remaining_attempts, reset_time)
    """
    cache_key = f"rate_limit:{identifier}"
    
    # Get current attempts
    attempts = cache.get(cache_key, [])
    now = timezone.now()
    
    # Remove old attempts outside the window
    cutoff_time = now - timedelta(seconds=window)
    attempts = [attempt for attempt in attempts if attempt > cutoff_time]
    
    # Check if limit exceeded
    if len(attempts) >= limit:
        # Find when the oldest attempt will expire
        reset_time = attempts[0] + timedelta(seconds=window)
        return False, 0, reset_time
    
    # Add current attempt
    attempts.append(now)
    cache.set(cache_key, attempts, window)
    
    remaining = limit - len(attempts)
    return True, remaining, None


def log_login_attempt(username, request, status, user=None, failure_reason=''):
    """Log login attempt for security monitoring"""
    LoginAttempt.objects.create(
        user=user,
        username_attempted=username,
        ip_address=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        status=status,
        failure_reason=failure_reason
    )


def detect_suspicious_login(user, request):
    """Detect suspicious login patterns"""
    ip_address = get_client_ip(request)
    
    # Check for login from new location
    recent_ips = UserDevice.objects.filter(
        user=user,
        last_login__gte=timezone.now() - timedelta(days=30)
    ).values_list('ip_address', flat=True)
    
    if ip_address not in recent_ips:
        # New location detected
        SuspiciousActivity.objects.create(
            user=user,
            activity_type='login_from_new_location',
            risk_level='medium',
            description=f'Login from new IP address: {ip_address}',
            ip_address=ip_address,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            metadata={
                'previous_ips': list(recent_ips),
                'new_ip': ip_address
            }
        )
        return True
    
    return False


def check_failed_login_attempts(identifier, threshold=5, window=900):
    """Check for excessive failed login attempts"""
    recent_attempts = LoginAttempt.objects.filter(
        username_attempted=identifier,
        status='failed',
        attempted_at__gte=timezone.now() - timedelta(seconds=window)
    ).count()
    
    if recent_attempts >= threshold:
        return True, recent_attempts
    
    return False, recent_attempts


def is_account_locked(user):
    """Check if account is locked due to security reasons"""
    # Check for recent suspicious activities
    critical_activities = SuspiciousActivity.objects.filter(
        user=user,
        risk_level='critical',
        status='open',
        detected_at__gte=timezone.now() - timedelta(hours=24)
    ).exists()
    
    if critical_activities:
        return True, 'Account locked due to critical security alert'
    
    # Check for excessive failed logins
    failed_attempts = LoginAttempt.objects.filter(
        user=user,
        status='failed',
        attempted_at__gte=timezone.now() - timedelta(hours=1)
    ).count()
    
    if failed_attempts >= 10:
        return True, 'Account locked due to excessive failed login attempts'
    
    return False, None


def clean_old_sessions(user, keep_current=True):
    """Clean old user sessions"""
    from django.contrib.sessions.models import Session
    
    sessions = user.sessions.all()
    if keep_current:
        # Keep the most recent session
        sessions = sessions[1:]
    
    for session in sessions:
        try:
            # Delete Django session
            Session.objects.filter(session_key=session.session_key).delete()
            # Delete our session record
            session.delete()
        except:
            pass


def get_security_score(user):
    """Calculate user security score based on various factors"""
    score = 100
    
    # Check 2FA status
    if not hasattr(user, 'two_factor_auth') or not user.two_factor_auth.is_enabled:
        score -= 20
    
    # Check email verification
    if not user.is_verified:
        score -= 15
    
    # Check phone verification
    if not user.phone_verified_at:
        score -= 10
    
    # Check KYC verification
    if not user.is_kyc_verified:
        score -= 15
    
    # Check for recent suspicious activities
    recent_suspicious = SuspiciousActivity.objects.filter(
        user=user,
        detected_at__gte=timezone.now() - timedelta(days=30),
        status='open'
    ).count()
    
    if recent_suspicious > 0:
        score -= min(recent_suspicious * 5, 20)
    
    # Check password age (if we track it)
    # This would require additional password history tracking
    
    return max(score, 0)


def get_device_trust_score(device):
    """Calculate device trust score"""
    score = 50  # Base score
    
    # Trusted device bonus
    if device.is_trusted:
        score += 30
    
    # Usage frequency bonus
    days_since_first_seen = (timezone.now() - device.first_seen).days
    if days_since_first_seen > 30:
        score += 10
    elif days_since_first_seen > 7:
        score += 5
    
    # Recent usage bonus
    days_since_last_seen = (timezone.now() - device.last_seen).days
    if days_since_last_seen == 0:
        score += 10
    elif days_since_last_seen <= 7:
        score += 5
    
    # Penalty for blocked device
    if device.is_blocked:
        score = 0
    
    return min(score, 100)


def require_additional_verification(user, request):
    """Determine if additional verification is required"""
    device = track_user_device(user, request)
    device_score = get_device_trust_score(device)
    security_score = get_security_score(user)
    
    # Require verification for low trust devices
    if device_score < 30:
        return True, 'Unrecognized device'
    
    # Require verification for low security score
    if security_score < 50:
        return True, 'Security verification required'
    
    # Check for suspicious activity
    if detect_suspicious_login(user, request):
        return True, 'Suspicious login detected'
    
    return False, None
