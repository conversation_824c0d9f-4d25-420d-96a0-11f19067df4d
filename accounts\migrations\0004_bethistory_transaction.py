# Generated by Django 5.2.4 on 2025-07-05 18:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0003_userprofile_bet_notifications_userprofile_bio_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="BetHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bet_id", models.CharField(max_length=20, unique=True)),
                (
                    "bet_type",
                    models.CharField(
                        choices=[
                            ("single", "Single Bet"),
                            ("multiple", "Multiple Bet"),
                            ("system", "System Bet"),
                            ("live", "Live Bet"),
                        ],
                        default="single",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("cancelled", "Cancelled"),
                            ("cashout", "Cashed Out"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("stake_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("potential_win", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "actual_win",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "total_odds",
                    models.DecimalField(decimal_places=2, default=1.0, max_digits=10),
                ),
                (
                    "selections",
                    models.JSONField(default=list, help_text="List of bet selections"),
                ),
                (
                    "match_details",
                    models.JSONField(default=dict, help_text="Match and event details"),
                ),
                ("placed_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bet_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Bet History",
                "verbose_name_plural": "Bet History",
                "db_table": "accounts_bet_history",
                "ordering": ["-placed_at"],
            },
        ),
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("transaction_id", models.CharField(max_length=50, unique=True)),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("deposit", "Deposit"),
                            ("withdrawal", "Withdrawal"),
                            ("bet_stake", "Bet Stake"),
                            ("bet_win", "Bet Winnings"),
                            ("bonus", "Bonus"),
                            ("refund", "Refund"),
                            ("fee", "Fee"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("mpesa", "M-Pesa"),
                            ("bank", "Bank Transfer"),
                            ("card", "Credit/Debit Card"),
                            ("bonus", "Bonus"),
                            ("system", "System"),
                        ],
                        max_length=20,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "fee",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "balance_before",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "balance_after",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("description", models.TextField(blank=True)),
                ("reference", models.CharField(blank=True, max_length=100)),
                ("external_reference", models.CharField(blank=True, max_length=100)),
                ("metadata", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction",
                "verbose_name_plural": "Transactions",
                "db_table": "accounts_transaction",
                "ordering": ["-created_at"],
            },
        ),
    ]
