{% extends 'base.html' %}
{% load static %}

{% block title %}Casino - ZBet{% endblock %}

{% block extra_css %}
<style>
.casino-hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 60px 0;
    text-align: center;
    color: white;
}

.casino-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.jackpot-ticker {
    background: #0f3460;
    padding: 20px 0;
    overflow: hidden;
}

.jackpot-item {
    display: inline-block;
    margin: 0 30px;
    padding: 10px 20px;
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid #ffd700;
    border-radius: 25px;
    color: #ffd700;
}

.game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.game-card {
    background: #1e1e2e;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease;
    border: 1px solid #333;
}

.game-card:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
}

.game-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.game-info {
    padding: 15px;
}

.game-title {
    color: white;
    font-weight: bold;
    margin-bottom: 5px;
}

.game-provider {
    color: #888;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.game-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #aaa;
    margin-bottom: 15px;
}

.play-buttons {
    display: flex;
    gap: 10px;
}

.btn-play {
    flex: 1;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-play-real {
    background: #ffd700;
    color: #1a1a2e;
}

.btn-play-real:hover {
    background: #ffed4e;
    color: #1a1a2e;
}

.btn-play-demo {
    background: transparent;
    color: #ffd700;
    border: 1px solid #ffd700;
}

.btn-play-demo:hover {
    background: #ffd700;
    color: #1a1a2e;
}

.category-tabs {
    display: flex;
    justify-content: center;
    margin: 30px 0;
    flex-wrap: wrap;
    gap: 10px;
}

.category-tab {
    padding: 10px 20px;
    background: #1e1e2e;
    color: #aaa;
    border: 1px solid #333;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.category-tab:hover,
.category-tab.active {
    background: #ffd700;
    color: #1a1a2e;
    border-color: #ffd700;
}

.section-title {
    color: white;
    font-size: 2rem;
    margin: 40px 0 20px 0;
    text-align: center;
}

.jackpot-amount {
    font-size: 1.2rem;
    font-weight: bold;
}
</style>
{% endblock %}

{% block content %}
<!-- Casino Hero Section -->
<div class="casino-hero">
    <div class="container">
        <h1>🎰 Welcome to ZBet Casino</h1>
        <p class="lead">Experience the thrill of premium casino games with real money wins!</p>
    </div>
</div>

<!-- Jackpot Ticker -->
<div class="jackpot-ticker">
    <div class="container">
        <div class="d-flex justify-content-center align-items-center">
            <span class="me-3" style="color: #ffd700; font-weight: bold;">🏆 JACKPOTS:</span>
            <div id="jackpot-display">
                {% for jackpot in jackpots %}
                <div class="jackpot-item">
                    <strong>{{ jackpot.name }}</strong>
                    <div class="jackpot-amount">KES {{ jackpot.current_amount|floatformat:2 }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Game Categories -->
<div class="container">
    <div class="category-tabs">
        <a href="#featured" class="category-tab active" data-category="featured">⭐ Featured</a>
        <a href="#popular" class="category-tab" data-category="popular">🔥 Popular</a>
        <a href="#new" class="category-tab" data-category="new">🆕 New</a>
        {% for category in categories %}
        <a href="{% url 'casino:games_by_category' category.slug %}" class="category-tab">
            {{ category.icon }} {{ category.name }}
        </a>
        {% endfor %}
    </div>

    <!-- Featured Games -->
    <div id="featured-games" class="game-section">
        <h2 class="section-title">⭐ Featured Games</h2>
        <div class="game-grid">
            {% for game in featured_games %}
            <div class="game-card">
                {% if game.thumbnail %}
                <img src="{{ game.thumbnail.url }}" alt="{{ game.name }}">
                {% else %}
                <div style="height: 150px; background: linear-gradient(45deg, #1a1a2e, #16213e); display: flex; align-items: center; justify-content: center; color: #ffd700; font-size: 2rem;">
                    🎰
                </div>
                {% endif %}
                <div class="game-info">
                    <div class="game-title">{{ game.name }}</div>
                    <div class="game-provider">{{ game.provider.name }}</div>
                    <div class="game-stats">
                        <span>RTP: {{ game.rtp_percentage }}%</span>
                        <span>{{ game.volatility }} Vol.</span>
                        <span>{{ game.play_count }} plays</span>
                    </div>
                    <div class="play-buttons">
                        <a href="{% url 'casino:launch_game' game.slug %}" class="btn-play btn-play-real">
                            Play Real
                        </a>
                        {% if game.has_demo_mode %}
                        <a href="{% url 'casino:launch_game' game.slug %}?demo=true" class="btn-play btn-play-demo">
                            Demo
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Popular Games -->
    <div id="popular-games" class="game-section" style="display: none;">
        <h2 class="section-title">🔥 Popular Games</h2>
        <div class="game-grid">
            {% for game in popular_games %}
            <div class="game-card">
                {% if game.thumbnail %}
                <img src="{{ game.thumbnail.url }}" alt="{{ game.name }}">
                {% else %}
                <div style="height: 150px; background: linear-gradient(45deg, #1a1a2e, #16213e); display: flex; align-items: center; justify-content: center; color: #ffd700; font-size: 2rem;">
                    🎰
                </div>
                {% endif %}
                <div class="game-info">
                    <div class="game-title">{{ game.name }}</div>
                    <div class="game-provider">{{ game.provider.name }}</div>
                    <div class="game-stats">
                        <span>RTP: {{ game.rtp_percentage }}%</span>
                        <span>{{ game.volatility }} Vol.</span>
                        <span>{{ game.play_count }} plays</span>
                    </div>
                    <div class="play-buttons">
                        <a href="{% url 'casino:launch_game' game.slug %}" class="btn-play btn-play-real">
                            Play Real
                        </a>
                        {% if game.has_demo_mode %}
                        <a href="{% url 'casino:launch_game' game.slug %}?demo=true" class="btn-play btn-play-demo">
                            Demo
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- New Games -->
    <div id="new-games" class="game-section" style="display: none;">
        <h2 class="section-title">🆕 New Games</h2>
        <div class="game-grid">
            {% for game in new_games %}
            <div class="game-card">
                {% if game.thumbnail %}
                <img src="{{ game.thumbnail.url }}" alt="{{ game.name }}">
                {% else %}
                <div style="height: 150px; background: linear-gradient(45deg, #1a1a2e, #16213e); display: flex; align-items: center; justify-content: center; color: #ffd700; font-size: 2rem;">
                    🎰
                </div>
                {% endif %}
                <div class="game-info">
                    <div class="game-title">{{ game.name }}</div>
                    <div class="game-provider">{{ game.provider.name }}</div>
                    <div class="game-stats">
                        <span>RTP: {{ game.rtp_percentage }}%</span>
                        <span>{{ game.volatility }} Vol.</span>
                        <span>{{ game.play_count }} plays</span>
                    </div>
                    <div class="play-buttons">
                        <a href="{% url 'casino:launch_game' game.slug %}" class="btn-play btn-play-real">
                            Play Real
                        </a>
                        {% if game.has_demo_mode %}
                        <a href="{% url 'casino:launch_game' game.slug %}?demo=true" class="btn-play btn-play-demo">
                            Demo
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
// Category tab switching
document.querySelectorAll('.category-tab[data-category]').forEach(tab => {
    tab.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs
        document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
        
        // Add active class to clicked tab
        this.classList.add('active');
        
        // Hide all game sections
        document.querySelectorAll('.game-section').forEach(section => {
            section.style.display = 'none';
        });
        
        // Show selected section
        const category = this.dataset.category;
        const section = document.getElementById(category + '-games');
        if (section) {
            section.style.display = 'block';
        }
    });
});

// Update jackpots every 30 seconds
setInterval(function() {
    fetch('/casino/api/jackpots/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const display = document.getElementById('jackpot-display');
                display.innerHTML = data.jackpots.map(jackpot => `
                    <div class="jackpot-item">
                        <strong>${jackpot.name}</strong>
                        <div class="jackpot-amount">KES ${jackpot.current_amount.toLocaleString()}</div>
                    </div>
                `).join('');
            }
        })
        .catch(error => console.error('Error updating jackpots:', error));
}, 30000);
</script>
{% endblock %}
