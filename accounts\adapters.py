"""
Custom Allauth Adapters for ZBet Platform
Handles social authentication integration with our custom User model
"""

from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.socialaccount.models import SocialAccount
from django.contrib.auth import get_user_model
from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone
from .two_factor_service import two_factor_service
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class CustomAccountAdapter(DefaultAccountAdapter):
    """
    Custom account adapter for allauth
    """
    
    def get_login_redirect_url(self, request):
        """
        Custom login redirect with 2FA check
        """
        user = request.user
        
        # Check if 2FA is enabled for the user
        if two_factor_service.is_2fa_enabled(user):
            # Store user ID in session for 2FA verification
            request.session['pending_2fa_user_id'] = str(user.id)
            request.session['social_login'] = True
            
            # Send SMS code if SMS 2FA is enabled
            methods = two_factor_service.get_2fa_methods(user)
            if 'sms' in methods:
                result = two_factor_service.send_sms_2fa_code(user)
                if not result['success']:
                    messages.warning(request, 'Failed to send SMS code. You can still use your authenticator app.')
            
            messages.info(request, 'Please enter your 2FA code to complete login.')
            return reverse('accounts:two_factor_login')
        
        # No 2FA required, proceed to dashboard
        return reverse('accounts:dashboard')
    
    def save_user(self, request, user, form, commit=True):
        """
        Save user with additional processing
        """
        user = super().save_user(request, user, form, commit=False)
        
        # Set additional fields if needed
        if not user.first_name and hasattr(form, 'cleaned_data'):
            # Try to extract name from email
            email_parts = user.email.split('@')[0].split('.')
            if len(email_parts) >= 2:
                user.first_name = email_parts[0].capitalize()
                user.last_name = email_parts[1].capitalize()
            else:
                user.first_name = email_parts[0].capitalize()
        
        if commit:
            user.save()
        
        return user


class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Custom social account adapter for allauth
    """
    
    def pre_social_login(self, request, sociallogin):
        """
        Invoked just after a user successfully authenticates via a social provider,
        but before the login is actually processed
        """
        # Check if user already exists with this email
        if sociallogin.account.provider == 'google' or sociallogin.account.provider == 'facebook':
            email = sociallogin.account.extra_data.get('email')
            if email:
                try:
                    existing_user = User.objects.get(email=email)
                    # Connect the social account to existing user
                    sociallogin.connect(request, existing_user)
                    logger.info(f"Connected {sociallogin.account.provider} account to existing user {email}")
                except User.DoesNotExist:
                    # New user, will be created automatically
                    pass
    
    def populate_user(self, request, sociallogin, data):
        """
        Populate user information from social account data
        """
        user = super().populate_user(request, sociallogin, data)
        
        # Extract additional information from social providers
        if sociallogin.account.provider == 'google':
            extra_data = sociallogin.account.extra_data
            user.first_name = extra_data.get('given_name', '')
            user.last_name = extra_data.get('family_name', '')
            
            # Set profile picture URL if available
            picture_url = extra_data.get('picture')
            if picture_url and hasattr(user, 'profile'):
                user.profile.avatar_url = picture_url
        
        elif sociallogin.account.provider == 'facebook':
            extra_data = sociallogin.account.extra_data
            user.first_name = extra_data.get('first_name', '')
            user.last_name = extra_data.get('last_name', '')
            
            # Set profile picture URL if available
            picture_data = extra_data.get('picture', {})
            if isinstance(picture_data, dict):
                picture_url = picture_data.get('data', {}).get('url')
                if picture_url and hasattr(user, 'profile'):
                    user.profile.avatar_url = picture_url
        
        return user
    
    def save_user(self, request, sociallogin, form=None):
        """
        Save user from social login
        """
        user = super().save_user(request, sociallogin, form)
        
        # Mark email as verified for social logins
        if not user.is_verified and user.email:
            user.is_verified = True
            user.email_verified_at = timezone.now()
            user.save()
        
        # Log the social login
        from .models import SecurityLog
        from django.utils import timezone
        
        SecurityLog.objects.create(
            user=user,
            event_type='social_login',
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={
                'provider': sociallogin.account.provider,
                'provider_id': sociallogin.account.uid,
                'email': user.email,
            }
        )
        
        logger.info(f"User {user.email} logged in via {sociallogin.account.provider}")
        
        return user
    
    def get_connect_redirect_url(self, request, socialaccount):
        """
        Redirect URL after connecting a social account
        """
        messages.success(
            request, 
            f'{socialaccount.provider.capitalize()} account connected successfully!'
        )
        return reverse('accounts:dashboard')
    
    def authentication_error(self, request, provider_id, error=None, exception=None, extra_context=None):
        """
        Handle authentication errors
        """
        logger.error(f"Social authentication error for {provider_id}: {error}")
        
        if error == 'access_denied':
            messages.error(request, 'Access was denied. Please try again.')
        elif error == 'invalid_request':
            messages.error(request, 'Invalid request. Please try again.')
        else:
            messages.error(request, f'Authentication failed. Please try again or contact support.')
        
        return redirect('accounts:login')
    
    def is_auto_signup_allowed(self, request, sociallogin):
        """
        Control whether auto signup is allowed
        """
        # Allow auto signup for trusted providers
        trusted_providers = ['google', 'facebook']
        return sociallogin.account.provider in trusted_providers
    
    def get_signup_form_initial_data(self, request, sociallogin):
        """
        Pre-populate signup form with social account data
        """
        initial = {}
        
        if sociallogin.account.provider == 'google':
            extra_data = sociallogin.account.extra_data
            initial.update({
                'first_name': extra_data.get('given_name', ''),
                'last_name': extra_data.get('family_name', ''),
                'email': extra_data.get('email', ''),
            })
        
        elif sociallogin.account.provider == 'facebook':
            extra_data = sociallogin.account.extra_data
            initial.update({
                'first_name': extra_data.get('first_name', ''),
                'last_name': extra_data.get('last_name', ''),
                'email': extra_data.get('email', ''),
            })
        
        return initial
