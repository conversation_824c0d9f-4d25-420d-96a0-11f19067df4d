from django.contrib import admin
from .models import PaymentMethod, Transaction, Wallet, MPesaTransaction, PaymentLimit

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active', 'priority')
    list_filter = ('is_active',)
    search_fields = ('name', 'code')
    ordering = ('priority', 'name')

@admin.register(Wallet)
class WalletAdmin(admin.ModelAdmin):
    list_display = ('user', 'balance', 'is_active', 'last_updated')
    list_filter = ('is_active',)
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('last_updated',)

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('reference', 'user', 'transaction_type', 'amount', 'status', 'created_at')
    list_filter = ('transaction_type', 'status', 'payment_method')
    search_fields = ('reference', 'user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)

@admin.register(MPesaTransaction)
class MPesaTransactionAdmin(admin.ModelAdmin):
    list_display = ('mpesa_receipt_number', 'phone_number', 'transaction', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('mpesa_receipt_number', 'phone_number', 'transaction__reference')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(PaymentLimit)
class PaymentLimitAdmin(admin.ModelAdmin):
    list_display = ('payment_method', 'limit_type', 'amount', 'is_active')
    list_filter = ('limit_type', 'is_active', 'payment_method')
    search_fields = ('payment_method__name',)
