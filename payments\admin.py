from django.contrib import admin
from .models import (
    PaymentMethod, Transaction, Wallet, MPesaTransaction,
    PaymentLimit, UserPaymentStats, PaymentNotification
)

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active', 'priority')
    list_filter = ('is_active',)
    search_fields = ('name', 'code')
    ordering = ('priority', 'name')

@admin.register(Wallet)
class WalletAdmin(admin.ModelAdmin):
    list_display = ('user', 'balance', 'is_active', 'last_updated')
    list_filter = ('is_active', 'created_at')
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('last_updated', 'created_at')
    ordering = ('-balance',)


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('reference', 'user', 'transaction_type', 'amount', 'status', 'payment_method', 'created_at')
    list_filter = ('transaction_type', 'status', 'payment_method', 'created_at')
    search_fields = ('reference', 'user__username', 'user__email', 'external_reference')
    readonly_fields = ('reference', 'created_at', 'updated_at', 'balance_before', 'balance_after')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Transaction Details', {
            'fields': ('reference', 'user', 'wallet', 'transaction_type', 'amount', 'fee')
        }),
        ('Payment Information', {
            'fields': ('payment_method', 'status', 'external_reference')
        }),
        ('Balance Information', {
            'fields': ('balance_before', 'balance_after')
        }),
        ('Additional Information', {
            'fields': ('description', 'callback_data')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(MPesaTransaction)
class MPesaTransactionAdmin(admin.ModelAdmin):
    list_display = ('transaction', 'phone_number', 'mpesa_receipt_number', 'result_code', 'created_at')
    list_filter = ('result_code', 'created_at')
    search_fields = ('phone_number', 'mpesa_receipt_number', 'checkout_request_id', 'merchant_request_id')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)

    fieldsets = (
        ('M-Pesa Details', {
            'fields': ('transaction', 'phone_number', 'mpesa_receipt_number')
        }),
        ('API Information', {
            'fields': ('checkout_request_id', 'merchant_request_id', 'account_reference', 'transaction_desc')
        }),
        ('Result Information', {
            'fields': ('result_code', 'result_description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(PaymentLimit)
class PaymentLimitAdmin(admin.ModelAdmin):
    list_display = ('payment_method', 'limit_type', 'amount', 'is_active')
    list_filter = ('payment_method', 'limit_type', 'is_active')
    search_fields = ('payment_method__name',)
    ordering = ('payment_method', 'limit_type')


@admin.register(UserPaymentStats)
class UserPaymentStatsAdmin(admin.ModelAdmin):
    list_display = ('user', 'date', 'total_deposits', 'total_withdrawals', 'deposit_count', 'withdrawal_count')
    list_filter = ('date',)
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-date', 'user')
    date_hierarchy = 'date'


@admin.register(PaymentNotification)
class PaymentNotificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'notification_type', 'title', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('user__username', 'user__email', 'title', 'message')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        queryset.update(is_read=True)
        self.message_user(request, f"{queryset.count()} notifications marked as read.")
    mark_as_read.short_description = "Mark selected notifications as read"

    def mark_as_unread(self, request, queryset):
        queryset.update(is_read=False)
        self.message_user(request, f"{queryset.count()} notifications marked as unread.")
    mark_as_unread.short_description = "Mark selected notifications as unread"


