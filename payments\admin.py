from django.contrib import admin
from .models import (
    PaymentMethod, Transaction, Wallet, MPesaTransaction,
    PaymentLimit, UserPaymentStats, PaymentNotification,
    Deposit, Withdrawal, TransactionFee, AirtelTransaction,
    CardTransaction, BankTransaction
)

@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active', 'priority')
    list_filter = ('is_active',)
    search_fields = ('name', 'code')
    ordering = ('priority', 'name')

@admin.register(Wallet)
class WalletAdmin(admin.ModelAdmin):
    list_display = ('user', 'balance', 'low_balance_threshold', 'low_balance_alert_sent', 'is_active', 'last_updated')
    list_filter = ('is_active', 'low_balance_alert_sent', 'created_at')
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('last_updated', 'created_at')
    ordering = ('-balance',)
    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Balance Information', {
            'fields': ('balance', 'low_balance_threshold', 'low_balance_alert_sent')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'last_updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('reference', 'user', 'transaction_type', 'amount', 'status', 'payment_method', 'created_at')
    list_filter = ('transaction_type', 'status', 'payment_method', 'created_at')
    search_fields = ('reference', 'user__username', 'user__email', 'external_reference')
    readonly_fields = ('reference', 'created_at', 'updated_at', 'balance_before', 'balance_after')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Transaction Details', {
            'fields': ('reference', 'user', 'wallet', 'transaction_type', 'amount', 'fee')
        }),
        ('Payment Information', {
            'fields': ('payment_method', 'status', 'external_reference')
        }),
        ('Balance Information', {
            'fields': ('balance_before', 'balance_after')
        }),
        ('Additional Information', {
            'fields': ('description', 'callback_data')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(MPesaTransaction)
class MPesaTransactionAdmin(admin.ModelAdmin):
    list_display = ('transaction', 'phone_number', 'mpesa_receipt_number', 'result_code', 'created_at')
    list_filter = ('result_code', 'created_at')
    search_fields = ('phone_number', 'mpesa_receipt_number', 'checkout_request_id', 'merchant_request_id')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)

    fieldsets = (
        ('M-Pesa Details', {
            'fields': ('transaction', 'phone_number', 'mpesa_receipt_number')
        }),
        ('API Information', {
            'fields': ('checkout_request_id', 'merchant_request_id', 'account_reference', 'transaction_desc')
        }),
        ('Result Information', {
            'fields': ('result_code', 'result_description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(PaymentLimit)
class PaymentLimitAdmin(admin.ModelAdmin):
    list_display = ('payment_method', 'limit_type', 'amount', 'is_active')
    list_filter = ('payment_method', 'limit_type', 'is_active')
    search_fields = ('payment_method__name',)
    ordering = ('payment_method', 'limit_type')


@admin.register(UserPaymentStats)
class UserPaymentStatsAdmin(admin.ModelAdmin):
    list_display = ('user', 'date', 'total_deposits', 'total_withdrawals', 'deposit_count', 'withdrawal_count')
    list_filter = ('date',)
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-date', 'user')
    date_hierarchy = 'date'


@admin.register(PaymentNotification)
class PaymentNotificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'notification_type', 'title', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('user__username', 'user__email', 'title', 'message')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        queryset.update(is_read=True)
        self.message_user(request, f"{queryset.count()} notifications marked as read.")
    mark_as_read.short_description = "Mark selected notifications as read"

    def mark_as_unread(self, request, queryset):
        queryset.update(is_read=False)
        self.message_user(request, f"{queryset.count()} notifications marked as unread.")
    mark_as_unread.short_description = "Mark selected notifications as unread"


@admin.register(Deposit)
class DepositAdmin(admin.ModelAdmin):
    list_display = ['reference', 'user', 'payment_method', 'amount', 'fee_amount', 'net_amount', 'status', 'receipt_generated', 'created_at']
    list_filter = ['status', 'payment_method', 'receipt_generated', 'created_at']
    search_fields = ['reference', 'external_reference', 'user__username', 'phone_number']
    readonly_fields = ['id', 'created_at', 'updated_at', 'processed_at', 'receipt_generated_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'wallet', 'payment_method', 'phone_number')
        }),
        ('Amount Details', {
            'fields': ('amount', 'fee_amount', 'net_amount')
        }),
        ('Status & References', {
            'fields': ('status', 'reference', 'external_reference')
        }),
        ('Additional Info', {
            'fields': ('description', 'callback_data')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'processed_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(Withdrawal)
class WithdrawalAdmin(admin.ModelAdmin):
    list_display = ['reference', 'user', 'payment_method', 'amount', 'fee_amount', 'net_amount', 'status', 'approval_required', 'receipt_generated', 'created_at']
    list_filter = ['status', 'payment_method', 'approval_required', 'receipt_generated', 'created_at']
    search_fields = ['reference', 'external_reference', 'user__username', 'phone_number']
    readonly_fields = ['id', 'created_at', 'updated_at', 'processed_at', 'receipt_generated_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'wallet', 'payment_method', 'phone_number')
        }),
        ('Amount Details', {
            'fields': ('amount', 'fee_amount', 'net_amount')
        }),
        ('Status & References', {
            'fields': ('status', 'reference', 'external_reference')
        }),
        ('Approval', {
            'fields': ('approval_required', 'approved_by', 'approved_at')
        }),
        ('Additional Info', {
            'fields': ('description', 'callback_data')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'processed_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(TransactionFee)
class TransactionFeeAdmin(admin.ModelAdmin):
    list_display = ['payment_method', 'transaction_type', 'fee_type', 'min_amount', 'max_amount', 'fee_amount', 'is_active']
    list_filter = ['payment_method', 'transaction_type', 'fee_type', 'is_active']
    search_fields = ['payment_method__name']
    ordering = ['payment_method', 'transaction_type', 'min_amount']

    fieldsets = (
        ('Basic Information', {
            'fields': ('payment_method', 'transaction_type', 'fee_type', 'is_active')
        }),
        ('Amount Range', {
            'fields': ('min_amount', 'max_amount')
        }),
        ('Fee Configuration', {
            'fields': ('fee_amount', 'min_fee', 'max_fee')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(AirtelTransaction)
class AirtelTransactionAdmin(admin.ModelAdmin):
    list_display = ['airtel_transaction_id', 'reference', 'phone_number', 'status', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['airtel_transaction_id', 'reference', 'phone_number']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Transaction Details', {
            'fields': ('transaction', 'airtel_transaction_id', 'reference')
        }),
        ('Customer Info', {
            'fields': ('phone_number',)
        }),
        ('Status', {
            'fields': ('status', 'status_description')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(CardTransaction)
class CardTransactionAdmin(admin.ModelAdmin):
    list_display = ['payment_intent_id', 'card_brand', 'card_last_four', 'customer_email', 'created_at']
    list_filter = ['card_brand', 'created_at']
    search_fields = ['payment_intent_id', 'payment_method_id', 'customer_email', 'card_last_four']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Transaction Details', {
            'fields': ('transaction', 'payment_intent_id', 'client_secret', 'payment_method_id')
        }),
        ('Card Info', {
            'fields': ('card_brand', 'card_last_four')
        }),
        ('Customer Info', {
            'fields': ('customer_email',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(BankTransaction)
class BankTransactionAdmin(admin.ModelAdmin):
    list_display = ['order_tracking_id', 'merchant_reference', 'bank_code', 'account_number', 'customer_email', 'created_at']
    list_filter = ['bank_code', 'created_at']
    search_fields = ['order_tracking_id', 'merchant_reference', 'account_number', 'customer_email']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Transaction Details', {
            'fields': ('transaction', 'order_tracking_id', 'merchant_reference', 'redirect_url')
        }),
        ('Bank Info', {
            'fields': ('bank_code', 'account_number', 'account_name')
        }),
        ('Customer Info', {
            'fields': ('customer_email', 'customer_phone')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


