"""
Management command to check for low wallet balances and send alerts
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import logging

from payments.models import Wallet, PaymentNotification

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Check for low wallet balances and send alerts to users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force check even if alerts were already sent',
        )
        parser.add_argument(
            '--threshold',
            type=float,
            help='Override default threshold for all wallets',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually sending alerts',
        )

    def handle(self, *args, **options):
        force = options['force']
        threshold_override = options.get('threshold')
        dry_run = options['dry_run']

        self.stdout.write(
            self.style.SUCCESS(f'Starting low balance check at {timezone.now()}')
        )

        # Get all active wallets
        wallets = Wallet.objects.filter(is_active=True).select_related('user')
        
        if threshold_override:
            # Use override threshold for all wallets
            wallets = wallets.filter(balance__lt=Decimal(str(threshold_override)))
            if not force:
                wallets = wallets.filter(low_balance_alert_sent=False)
        else:
            # Use individual wallet thresholds
            if force:
                # Check all wallets below their individual thresholds
                low_balance_wallets = []
                for wallet in wallets:
                    if wallet.balance < wallet.low_balance_threshold:
                        low_balance_wallets.append(wallet)
                wallets = low_balance_wallets
            else:
                # Only check wallets that haven't been alerted yet
                low_balance_wallets = []
                for wallet in wallets:
                    if (wallet.balance < wallet.low_balance_threshold and 
                        not wallet.low_balance_alert_sent):
                        low_balance_wallets.append(wallet)
                wallets = low_balance_wallets

        if not wallets:
            self.stdout.write(
                self.style.SUCCESS('No wallets found with low balances requiring alerts.')
            )
            return

        alerts_sent = 0
        errors = 0

        for wallet in wallets:
            try:
                threshold = (Decimal(str(threshold_override)) if threshold_override 
                           else wallet.low_balance_threshold)
                
                if dry_run:
                    self.stdout.write(
                        f'Would send low balance alert to {wallet.user.username} '
                        f'(Balance: KES {wallet.balance:,.2f}, Threshold: KES {threshold:,.2f})'
                    )
                    continue

                # Create notification
                notification = PaymentNotification.objects.create(
                    user=wallet.user,
                    notification_type='LOW_BALANCE',
                    title='Low Balance Alert',
                    message=(
                        f'Your wallet balance (KES {wallet.balance:,.2f}) is below '
                        f'your threshold of KES {threshold:,.2f}. '
                        f'Please top up your account to continue enjoying our services.'
                    )
                )

                # Mark alert as sent
                wallet.low_balance_alert_sent = True
                wallet.save(update_fields=['low_balance_alert_sent'])

                alerts_sent += 1
                
                self.stdout.write(
                    f'Sent low balance alert to {wallet.user.username} '
                    f'(Balance: KES {wallet.balance:,.2f}, Threshold: KES {threshold:,.2f})'
                )

                # Log the alert
                logger.info(
                    f'Low balance alert sent to user {wallet.user.id} '
                    f'({wallet.user.username}). Balance: {wallet.balance}, '
                    f'Threshold: {threshold}'
                )

            except Exception as e:
                errors += 1
                error_msg = f'Error sending alert to {wallet.user.username}: {str(e)}'
                self.stdout.write(self.style.ERROR(error_msg))
                logger.error(error_msg)

        # Summary
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Dry run completed. Would have sent {len(wallets)} alerts.'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Low balance check completed. '
                    f'Alerts sent: {alerts_sent}, Errors: {errors}'
                )
            )

        # Additional statistics
        total_wallets = Wallet.objects.filter(is_active=True).count()
        total_low_balance = len([w for w in Wallet.objects.filter(is_active=True) 
                               if w.balance < w.low_balance_threshold])
        
        self.stdout.write(
            f'Statistics: {total_wallets} active wallets, '
            f'{total_low_balance} with low balances'
        )
