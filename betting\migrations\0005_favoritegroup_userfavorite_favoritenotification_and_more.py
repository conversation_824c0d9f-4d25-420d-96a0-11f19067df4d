# Generated by Django 5.2.4 on 2025-07-05 21:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("betting", "0004_useroddsalert_oddscomparison"),
        ("sports", "0002_remove_match_statistics_match_stats_data_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="FavoriteGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("is_default", models.BooleanField(default=False)),
                ("is_public", models.BooleanField(default=False)),
                ("group_notifications", models.<PERSON>olean<PERSON>ield(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorite_groups",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Favorite Group",
                "verbose_name_plural": "Favorite Groups",
                "db_table": "betting_favorite_group",
                "ordering": ["name"],
                "unique_together": {("user", "name")},
            },
        ),
        migrations.CreateModel(
            name="UserFavorite",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "favorite_type",
                    models.CharField(
                        choices=[
                            ("match", "Match"),
                            ("team", "Team"),
                            ("league", "League"),
                            ("sport", "Sport"),
                        ],
                        max_length=20,
                    ),
                ),
                ("notify_on_odds_change", models.BooleanField(default=True)),
                ("notify_on_match_start", models.BooleanField(default=True)),
                ("notify_on_goals", models.BooleanField(default=False)),
                ("notify_on_results", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "league",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="sports.league",
                    ),
                ),
                (
                    "match",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="sports.match",
                    ),
                ),
                (
                    "sport",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="sports.sport",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="sports.team",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorites",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Favorite",
                "verbose_name_plural": "User Favorites",
                "db_table": "betting_user_favorite",
                "ordering": ["-created_at"],
                "unique_together": {
                    ("user", "league"),
                    ("user", "match"),
                    ("user", "sport"),
                    ("user", "team"),
                },
            },
        ),
        migrations.CreateModel(
            name="FavoriteNotification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("odds_change", "Odds Change"),
                            ("match_start", "Match Starting"),
                            ("goal_scored", "Goal Scored"),
                            ("match_result", "Match Result"),
                            ("lineup_announced", "Lineup Announced"),
                            ("match_postponed", "Match Postponed"),
                            ("new_market", "New Market Available"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                ("data", models.JSONField(blank=True, default=dict)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("delivery_method", models.CharField(blank=True, max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorite_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "favorite",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="betting.userfavorite",
                    ),
                ),
            ],
            options={
                "verbose_name": "Favorite Notification",
                "verbose_name_plural": "Favorite Notifications",
                "db_table": "betting_favorite_notification",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="FavoriteGroupMembership",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("added_at", models.DateTimeField(auto_now_add=True)),
                (
                    "added_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="added_group_memberships",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="memberships",
                        to="betting.favoritegroup",
                    ),
                ),
                (
                    "favorite",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="group_memberships",
                        to="betting.userfavorite",
                    ),
                ),
            ],
            options={
                "verbose_name": "Favorite Group Membership",
                "verbose_name_plural": "Favorite Group Memberships",
                "db_table": "betting_favorite_group_membership",
                "ordering": ["-added_at"],
                "unique_together": {("group", "favorite")},
            },
        ),
    ]
