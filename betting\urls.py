from django.urls import path
from . import views

app_name = 'betting'

urlpatterns = [
    # Sports and matches
    path('', views.sports_list_view, name='sports_list'),
    path('sport/<slug:sport_slug>/', views.sport_matches_view, name='sport_matches'),
    path('match/<int:match_id>/', views.match_detail_view, name='match_detail'),
    path('live/', views.live_matches_view, name='live_matches'),
    
    # Bet slip management
    path('bet-slip/', views.bet_slip_view, name='bet_slip'),
    path('api/add-to-bet-slip/', views.add_to_bet_slip, name='add_to_bet_slip'),
    path('api/remove-from-bet-slip/', views.remove_from_bet_slip, name='remove_from_bet_slip'),
    path('api/place-bet/', views.place_bet, name='place_bet'),
    
    # Bet management
    path('bet/<str:bet_id>/', views.bet_detail_view, name='bet_detail'),
    path('my-bets/', views.user_bets_view, name='user_bets'),
    
    # Live betting
    path('live-betting/<int:match_id>/', views.live_betting_view, name='live_betting'),
    path('api/live-odds/<int:match_id>/', views.get_live_odds, name='get_live_odds'),
    path('api/live-match-data/<int:match_id>/', views.get_live_match_data, name='get_live_match_data'),
    path('api/live-matches-feed/', views.get_live_matches_feed, name='get_live_matches_feed'),
    path('api/place-live-bet/', views.place_live_bet, name='place_live_bet'),
    path('api/cashout-bet/<str:bet_id>/', views.cashout_bet, name='cashout_bet'),

    # Bet cancellation
    path('api/request-cancellation/<str:bet_id>/', views.request_bet_cancellation, name='request_bet_cancellation'),
    path('api/cancellation-status/<str:bet_id>/', views.bet_cancellation_status, name='bet_cancellation_status'),
    path('api/check-cancellable/<str:bet_id>/', views.check_bet_cancellable, name='check_bet_cancellable'),
    path('my-cancellations/', views.user_cancellations_view, name='user_cancellations'),

    # Odds comparison
    path('odds-comparison/<int:match_id>/', views.odds_comparison_view, name='odds_comparison'),
    path('api/odds-comparison/<int:match_id>/<int:bet_type_id>/', views.get_odds_comparison_data, name='get_odds_comparison_data'),
    path('api/create-odds-alert/', views.create_odds_alert, name='create_odds_alert'),
    path('api/cancel-odds-alert/<int:alert_id>/', views.cancel_odds_alert, name='cancel_odds_alert'),
    path('my-odds-alerts/', views.user_odds_alerts_view, name='user_odds_alerts'),
    path('best-odds-widget/', views.best_odds_widget_view, name='best_odds_widget'),
]
