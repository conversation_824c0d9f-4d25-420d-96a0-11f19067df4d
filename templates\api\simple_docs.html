<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZBet API Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
            color: #333;
        }
        h1 {
            color: #1e40af;
            text-align: center;
            border-bottom: 3px solid #1e40af;
            padding-bottom: 10px;
        }
        h2 {
            color: #1e40af;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin: 10px 0;
        }
        .method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
            margin-right: 10px;
            color: white;
        }
        .get { background: #28a745; }
        .post { background: #ffc107; color: #000; }
        .put { background: #007bff; }
        .delete { background: #dc3545; }
        .url {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .base-url {
            background: #1e40af;
            color: white;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
            border-radius: 5px;
        }
        .auth-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .nav {
            background: #f8f9fa;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .nav a {
            color: #1e40af;
            text-decoration: none;
            margin-right: 20px;
            font-weight: bold;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>ZBet API Documentation</h1>
    
    <div class="base-url">
        <strong>Base URL:</strong> http://127.0.0.1:8000/api/v1/
    </div>

    <div class="auth-box">
        <h3>Authentication</h3>
        <p>This API uses JWT tokens. Include in header: <code>Authorization: Bearer &lt;token&gt;</code></p>
    </div>

    <div class="nav">
        <a href="#auth">Authentication</a>
        <a href="#sports">Sports</a>
        <a href="#betting">Betting</a>
        <a href="#payments">Payments</a>
        <a href="#utils">Utilities</a>
    </div>

    <section id="auth">
        <h2>Authentication</h2>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/auth/register/</span>
            <p>Register new user</p>
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/auth/login/</span>
            <p>Login and get JWT tokens</p>
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/auth/profile/</span>
            <p>Get user profile</p>
        </div>
    </section>

    <section id="sports">
        <h2>Sports</h2>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/sports/</span>
            <p>List all sports</p>
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/leagues/</span>
            <p>List leagues</p>
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/matches/</span>
            <p>List matches with filtering</p>
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/matches/{id}/</span>
            <p>Get match details</p>
        </div>
    </section>

    <section id="betting">
        <h2>Betting</h2>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/bets/</span>
            <p>List user bets</p>
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/bets/place/</span>
            <p>Place a new bet</p>
        </div>
    </section>

    <section id="payments">
        <h2>Payments</h2>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/wallet/balance/</span>
            <p>Get wallet balance</p>
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/deposits/create/</span>
            <p>Create M-Pesa deposit</p>
        </div>
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="url">/withdrawals/create/</span>
            <p>Create withdrawal</p>
        </div>
    </section>

    <section id="utils">
        <h2>Utilities</h2>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/config/</span>
            <p>Get app configuration</p>
        </div>
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="url">/dashboard/</span>
            <p>Get user dashboard data</p>
        </div>
    </section>

    <h2>Interactive Documentation</h2>
    <ul>
        <li><a href="/api/v1/docs/" target="_blank">Swagger UI</a> - Interactive API explorer</li>
        <li><a href="/api/v1/redoc/" target="_blank">ReDoc</a> - Clean documentation</li>
        <li><a href="/api/v1/schema/" target="_blank">OpenAPI Schema</a> - Raw schema</li>
    </ul>

    <h2>Rate Limits</h2>
    <ul>
        <li>Anonymous: 100/hour</li>
        <li>Authenticated: 1000/hour</li>
        <li>Betting: 500/hour</li>
        <li>Payments: 100/hour</li>
    </ul>
</body>
</html>
