"""
Custom middleware for session and security management
"""
from django.utils import timezone
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.urls import reverse
from django.conf import settings
from django.contrib import messages
from datetime import timedelta
from .security_utils import (
    get_client_ip, track_user_device, check_rate_limit,
    log_login_attempt, is_account_locked, require_additional_verification
)
from .models import UserSession, SecurityLog


class SessionSecurityMiddleware:
    """
    Middleware to handle session security and timeouts
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Process request before view
        self.process_request(request)
        
        # Get response from view
        response = self.get_response(request)
        
        # Process response after view
        return self.process_response(request, response)
    
    def process_request(self, request):
        """Process request for session security"""
        if request.user.is_authenticated:
            # Check session timeout
            if self.is_session_expired(request):
                logout(request)
                messages.warning(request, 'Your session has expired. Please log in again.')
                return redirect('accounts:login')
            
            # Update session activity
            self.update_session_activity(request)
            
            # Track user device
            if hasattr(request.user, 'id'):
                track_user_device(request.user, request)
    
    def process_response(self, request, response):
        """Process response for session security"""
        if request.user.is_authenticated:
            # Update last activity timestamp
            request.session['last_activity'] = timezone.now().timestamp()
        
        return response
    
    def is_session_expired(self, request):
        """Check if session has expired"""
        if 'last_activity' not in request.session:
            return False
        
        last_activity = timezone.datetime.fromtimestamp(
            request.session['last_activity'],
            tz=timezone.get_current_timezone()
        )
        
        # Check if session has been inactive for too long
        inactive_duration = timezone.now() - last_activity
        max_inactive = timedelta(seconds=getattr(settings, 'SESSION_COOKIE_AGE', 3600))
        
        return inactive_duration > max_inactive
    
    def update_session_activity(self, request):
        """Update session activity in database"""
        session_key = request.session.session_key
        if session_key:
            try:
                user_session = UserSession.objects.get(
                    user=request.user,
                    session_key=session_key
                )
                user_session.last_activity = timezone.now()
                user_session.save()
            except UserSession.DoesNotExist:
                # Create new session record
                UserSession.objects.create(
                    user=request.user,
                    session_key=session_key,
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    is_active=True
                )


class RateLimitMiddleware:
    """
    Middleware to handle rate limiting
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Check rate limits for sensitive endpoints
        if self.should_rate_limit(request):
            if not self.check_rate_limit(request):
                return self.rate_limit_response(request)
        
        response = self.get_response(request)
        return response
    
    def should_rate_limit(self, request):
        """Determine if request should be rate limited"""
        rate_limited_paths = [
            '/accounts/login/',
            '/accounts/register/',
            '/accounts/password/reset/',
            '/accounts/verify-email/',
            '/accounts/verify-phone/',
        ]
        
        return any(request.path.startswith(path) for path in rate_limited_paths)
    
    def check_rate_limit(self, request):
        """Check if request exceeds rate limit"""
        ip_address = get_client_ip(request)
        
        # Different limits for different endpoints
        if '/login/' in request.path:
            allowed, remaining, reset_time = check_rate_limit(
                f"login:{ip_address}", limit=5, window=300  # 5 attempts per 5 minutes
            )
        elif '/register/' in request.path:
            allowed, remaining, reset_time = check_rate_limit(
                f"register:{ip_address}", limit=3, window=3600  # 3 attempts per hour
            )
        elif '/password/reset/' in request.path:
            allowed, remaining, reset_time = check_rate_limit(
                f"password_reset:{ip_address}", limit=3, window=3600  # 3 attempts per hour
            )
        else:
            allowed, remaining, reset_time = check_rate_limit(
                f"general:{ip_address}", limit=10, window=300  # 10 attempts per 5 minutes
            )
        
        return allowed
    
    def rate_limit_response(self, request):
        """Return rate limit exceeded response"""
        from django.http import HttpResponseTooManyRequests
        from django.template.response import TemplateResponse
        
        context = {
            'error_message': 'Too many requests. Please try again later.',
            'retry_after': 300  # 5 minutes
        }
        
        response = TemplateResponse(
            request,
            'accounts/rate_limit_exceeded.html',
            context,
            status=429
        )
        response['Retry-After'] = '300'
        return response


class SecurityMonitoringMiddleware:
    """
    Middleware to monitor and log security events
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Monitor request for suspicious activity
        self.monitor_request(request)
        
        response = self.get_response(request)
        
        # Monitor response for security issues
        self.monitor_response(request, response)
        
        return response
    
    def monitor_request(self, request):
        """Monitor request for suspicious patterns"""
        if request.user.is_authenticated:
            # Check for account lockout
            is_locked, reason = is_account_locked(request.user)
            if is_locked:
                logout(request)
                messages.error(request, f'Account access restricted: {reason}')
                return redirect('accounts:login')
            
            # Check if additional verification is required
            needs_verification, reason = require_additional_verification(request.user, request)
            if needs_verification and not self.is_verification_endpoint(request):
                messages.warning(request, f'Additional verification required: {reason}')
                return redirect('accounts:two_factor_verify')
    
    def monitor_response(self, request, response):
        """Monitor response for security logging"""
        # Log failed authentication attempts
        if response.status_code == 403 and '/login/' in request.path:
            log_login_attempt(
                request.POST.get('username', ''),
                request,
                'failed',
                failure_reason='Invalid credentials'
            )
    
    def is_verification_endpoint(self, request):
        """Check if request is for verification endpoint"""
        verification_paths = [
            '/accounts/two-factor/',
            '/accounts/verify-email/',
            '/accounts/verify-phone/',
            '/accounts/logout/',
        ]
        
        return any(request.path.startswith(path) for path in verification_paths)


class AccountLockoutMiddleware:
    """
    Middleware to handle account lockouts
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        if request.user.is_authenticated:
            # Check if account should be locked
            if self.should_lock_account(request):
                self.lock_account(request)
                return redirect('accounts:account_locked')
        
        response = self.get_response(request)
        return response
    
    def should_lock_account(self, request):
        """Determine if account should be locked"""
        # Check for suspicious activity
        from .models import SuspiciousActivity
        
        critical_activities = SuspiciousActivity.objects.filter(
            user=request.user,
            risk_level='critical',
            status='open',
            detected_at__gte=timezone.now() - timedelta(hours=24)
        ).count()
        
        return critical_activities > 0
    
    def lock_account(self, request):
        """Lock user account"""
        request.user.is_suspended = True
        request.user.save()
        
        # Log security event
        SecurityLog.objects.create(
            user=request.user,
            event_type='account_locked',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={'reason': 'Critical suspicious activity detected'}
        )
        
        logout(request)
        messages.error(request, 'Your account has been temporarily locked for security reasons.')
