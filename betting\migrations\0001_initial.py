# Generated by Django 5.2.4 on 2025-07-05 19:52

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("sports", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Bet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bet_id", models.CharField(max_length=20, unique=True)),
                (
                    "bet_type",
                    models.CharField(
                        choices=[
                            ("single", "Single Bet"),
                            ("multiple", "Multiple Bet"),
                            ("system", "System Bet"),
                        ],
                        default="single",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("void", "Void"),
                            ("cashout", "Cashed Out"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("stake", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "total_odds",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1.00"), max_digits=10
                    ),
                ),
                ("potential_win", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "actual_win",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "cashout_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("cashout_at", models.DateTimeField(blank=True, null=True)),
                ("placed_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bets",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Bet",
                "verbose_name_plural": "Bets",
                "db_table": "betting_bet",
                "ordering": ["-placed_at"],
            },
        ),
        migrations.CreateModel(
            name="BetSlip",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_key", models.CharField(blank=True, max_length=40)),
                ("bet_type", models.CharField(default="single", max_length=20)),
                (
                    "stake",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "total_odds",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("1.00"), max_digits=10
                    ),
                ),
                (
                    "potential_win",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bet_slips",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Bet Slip",
                "verbose_name_plural": "Bet Slips",
                "db_table": "betting_bet_slip",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="BetType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("slug", models.SlugField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_live_betting",
                    models.BooleanField(
                        default=False, help_text="Available for live betting"
                    ),
                ),
                ("display_order", models.PositiveIntegerField(default=0)),
                (
                    "margin_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("5.00"),
                        help_text="House margin percentage",
                        max_digits=5,
                    ),
                ),
                (
                    "sports",
                    models.ManyToManyField(related_name="bet_types", to="sports.sport"),
                ),
            ],
            options={
                "verbose_name": "Bet Type",
                "verbose_name_plural": "Bet Types",
                "db_table": "betting_bet_type",
                "ordering": ["display_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="Market",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("suspended", "Suspended"),
                            ("settled", "Settled"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("parameters", models.JSONField(blank=True, default=dict)),
                ("winning_selection", models.CharField(blank=True, max_length=200)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "bet_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="markets",
                        to="betting.bettype",
                    ),
                ),
                (
                    "match",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="markets",
                        to="sports.match",
                    ),
                ),
            ],
            options={
                "verbose_name": "Market",
                "verbose_name_plural": "Markets",
                "db_table": "betting_market",
                "ordering": ["match__start_time", "bet_type__display_order"],
                "unique_together": {("match", "bet_type", "parameters")},
            },
        ),
        migrations.CreateModel(
            name="Selection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("short_name", models.CharField(blank=True, max_length=50)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("suspended", "Suspended"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("void", "Void"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "decimal_odds",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.01"))
                        ],
                    ),
                ),
                ("fractional_odds", models.CharField(blank=True, max_length=20)),
                ("american_odds", models.IntegerField(blank=True, null=True)),
                (
                    "min_bet",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("10.00"), max_digits=10
                    ),
                ),
                (
                    "max_bet",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("10000.00"), max_digits=15
                    ),
                ),
                ("total_bets", models.PositiveIntegerField(default=0)),
                (
                    "total_stake",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "market",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="selections",
                        to="betting.market",
                    ),
                ),
            ],
            options={
                "verbose_name": "Selection",
                "verbose_name_plural": "Selections",
                "db_table": "betting_selection",
                "ordering": ["market", "name"],
            },
        ),
        migrations.CreateModel(
            name="OddsHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "old_decimal_odds",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "new_decimal_odds",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("change_reason", models.CharField(blank=True, max_length=200)),
                ("changed_at", models.DateTimeField(auto_now_add=True)),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "selection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="odds_history",
                        to="betting.selection",
                    ),
                ),
            ],
            options={
                "verbose_name": "Odds History",
                "verbose_name_plural": "Odds History",
                "db_table": "betting_odds_history",
                "ordering": ["-changed_at"],
            },
        ),
        migrations.CreateModel(
            name="BetSlipSelection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("order", models.PositiveIntegerField(default=0)),
                ("added_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bet_slip",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="betting.betslip",
                    ),
                ),
                (
                    "selection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="betting.selection",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bet Slip Selection",
                "verbose_name_plural": "Bet Slip Selections",
                "db_table": "betting_bet_slip_selection",
                "ordering": ["order", "added_at"],
                "unique_together": {("bet_slip", "selection")},
            },
        ),
        migrations.AddField(
            model_name="betslip",
            name="selections",
            field=models.ManyToManyField(
                through="betting.BetSlipSelection", to="betting.selection"
            ),
        ),
        migrations.CreateModel(
            name="BetSelection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("odds_taken", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("void", "Void"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "bet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="selections",
                        to="betting.bet",
                    ),
                ),
                (
                    "selection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bet_selections",
                        to="betting.selection",
                    ),
                ),
            ],
            options={
                "verbose_name": "Bet Selection",
                "verbose_name_plural": "Bet Selections",
                "db_table": "betting_bet_selection",
                "ordering": ["created_at"],
            },
        ),
    ]
