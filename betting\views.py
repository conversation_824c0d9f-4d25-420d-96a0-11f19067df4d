from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.db import transaction, models
from decimal import Decimal
import json

from .models import (
    BetType, Market, Selection, Bet, BetSelection,
    BetSlip, BetSlipSelection, OddsHistory,
    BetCancellation, BetCancellationRule,
    OddsComparison, UserOddsAlert,
    UserFavorite, FavoriteNotification, FavoriteGroup, FavoriteGroupMembership,
    LiveBetNotification, UserNotificationPreference, NotificationDeliveryLog,
    VirtualSport, VirtualTeam, VirtualMatch, VirtualBetType, VirtualMarket,
    VirtualSelection, VirtualBet, VirtualBetSelection
)
from sports.models import Match, Sport, League, MatchEvent


def sports_list_view(request):
    """
    Display list of available sports
    """
    sports = Sport.objects.filter(is_active=True).order_by('display_order', 'name')

    # Get featured sports
    featured_sports = sports.filter(is_featured=True)[:6]

    context = {
        'sports': sports,
        'featured_sports': featured_sports,
    }

    return render(request, 'betting/sports_list.html', context)


def sport_matches_view(request, sport_slug):
    """
    Display matches for a specific sport
    """
    sport = get_object_or_404(Sport, slug=sport_slug, is_active=True)

    # Get upcoming matches
    upcoming_matches = Match.objects.filter(
        league__sport=sport,
        status='scheduled',
        start_time__gte=timezone.now(),
        betting_enabled=True
    ).select_related('home_team', 'away_team', 'league').order_by('start_time')[:20]

    # Get live matches
    live_matches = Match.objects.filter(
        league__sport=sport,
        status__in=['live', 'halftime'],
        betting_enabled=True
    ).select_related('home_team', 'away_team', 'league')[:10]

    # Get leagues for this sport
    leagues = League.objects.filter(
        sport=sport,
        is_active=True
    ).order_by('display_order', 'name')

    context = {
        'sport': sport,
        'upcoming_matches': upcoming_matches,
        'live_matches': live_matches,
        'leagues': leagues,
    }

    return render(request, 'betting/sport_matches.html', context)


def match_detail_view(request, match_id):
    """
    Display detailed betting markets for a match
    """
    match = get_object_or_404(
        Match,
        id=match_id,
        betting_enabled=True
    )

    # Get available markets
    markets = Market.objects.filter(
        match=match,
        status='active'
    ).select_related('bet_type').prefetch_related('selections').order_by('bet_type__display_order')

    # Group markets by bet type
    markets_by_type = {}
    for market in markets:
        bet_type = market.bet_type.name
        if bet_type not in markets_by_type:
            markets_by_type[bet_type] = []
        markets_by_type[bet_type].append(market)

    # Get user's current bet slip
    bet_slip = None
    if request.user.is_authenticated:
        bet_slip, created = BetSlip.objects.get_or_create(
            user=request.user,
            defaults={'session_key': request.session.session_key}
        )

    context = {
        'match': match,
        'markets': markets,
        'markets_by_type': markets_by_type,
        'bet_slip': bet_slip,
    }

    return render(request, 'betting/match_detail.html', context)


@login_required
@require_http_methods(["POST"])
def add_to_bet_slip(request):
    """
    Add selection to bet slip via AJAX
    """
    try:
        data = json.loads(request.body)
        selection_id = data.get('selection_id')

        selection = get_object_or_404(Selection, id=selection_id)

        # Check if selection is active
        if not selection.is_active:
            return JsonResponse({
                'success': False,
                'message': 'This selection is no longer available.'
            })

        # Get or create bet slip
        bet_slip, created = BetSlip.objects.get_or_create(
            user=request.user,
            defaults={'session_key': request.session.session_key}
        )

        # Check if selection already in bet slip
        if BetSlipSelection.objects.filter(bet_slip=bet_slip, selection=selection).exists():
            return JsonResponse({
                'success': False,
                'message': 'Selection already in bet slip.'
            })

        # Add selection to bet slip
        BetSlipSelection.objects.create(
            bet_slip=bet_slip,
            selection=selection,
            order=bet_slip.selections.count()
        )

        # Update bet slip calculations
        bet_slip.update_calculations()

        return JsonResponse({
            'success': True,
            'message': 'Selection added to bet slip.',
            'bet_slip_count': bet_slip.selections.count()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def remove_from_bet_slip(request):
    """
    Remove selection from bet slip via AJAX
    """
    try:
        data = json.loads(request.body)
        selection_id = data.get('selection_id')

        bet_slip = get_object_or_404(BetSlip, user=request.user)

        # Remove selection
        BetSlipSelection.objects.filter(
            bet_slip=bet_slip,
            selection_id=selection_id
        ).delete()

        # Update bet slip calculations
        bet_slip.update_calculations()

        return JsonResponse({
            'success': True,
            'message': 'Selection removed from bet slip.',
            'bet_slip_count': bet_slip.selections.count()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def bet_slip_view(request):
    """
    Display bet slip
    """
    try:
        bet_slip = BetSlip.objects.get(user=request.user)
        selections = BetSlipSelection.objects.filter(bet_slip=bet_slip).select_related(
            'selection__market__match__home_team',
            'selection__market__match__away_team',
            'selection__market__bet_type'
        ).order_by('order')
    except BetSlip.DoesNotExist:
        bet_slip = None
        selections = []

    context = {
        'bet_slip': bet_slip,
        'selections': selections,
    }

    return render(request, 'betting/bet_slip.html', context)


@login_required
@require_http_methods(["POST"])
def place_bet(request):
    """
    Place a bet from the bet slip
    """
    try:
        data = json.loads(request.body)
        stake = Decimal(str(data.get('stake', '0')))
        bet_type = data.get('bet_type', 'single')

        # Validate stake
        if stake <= 0:
            return JsonResponse({
                'success': False,
                'message': 'Please enter a valid stake amount.'
            })

        # Get bet slip
        bet_slip = get_object_or_404(BetSlip, user=request.user)

        if not bet_slip.selections.exists():
            return JsonResponse({
                'success': False,
                'message': 'No selections in bet slip.'
            })

        # Check minimum stake
        min_stake = Decimal('10.00')  # This should come from settings
        if stake < min_stake:
            return JsonResponse({
                'success': False,
                'message': f'Minimum stake is {min_stake}.'
            })

        # Check user balance (this would integrate with payments app)
        # For now, we'll assume user has sufficient balance

        with transaction.atomic():
            # Create bet
            bet = Bet.objects.create(
                user=request.user,
                bet_type=bet_type,
                stake=stake,
                total_odds=bet_slip.calculate_total_odds(),
                potential_win=stake * bet_slip.calculate_total_odds()
            )

            # Create bet selections
            for bet_slip_selection in bet_slip.betslipselection_set.all():
                BetSelection.objects.create(
                    bet=bet,
                    selection=bet_slip_selection.selection,
                    odds_taken=bet_slip_selection.selection.decimal_odds
                )

                # Update selection statistics
                selection = bet_slip_selection.selection
                selection.total_bets += 1
                selection.total_stake += stake if bet_type == 'single' else stake / bet_slip.selections.count()
                selection.save()

            # Clear bet slip
            bet_slip.betslipselection_set.all().delete()
            bet_slip.stake = Decimal('0.00')
            bet_slip.total_odds = Decimal('1.00')
            bet_slip.potential_win = Decimal('0.00')
            bet_slip.save()

            return JsonResponse({
                'success': True,
                'message': f'Bet placed successfully! Bet ID: {bet.bet_id}',
                'bet_id': bet.bet_id,
                'redirect_url': f'/betting/bet/{bet.bet_id}/'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error placing bet: {str(e)}'
        })


@login_required
def bet_detail_view(request, bet_id):
    """
    Display bet details
    """
    bet = get_object_or_404(Bet, bet_id=bet_id, user=request.user)

    selections = BetSelection.objects.filter(bet=bet).select_related(
        'selection__market__match__home_team',
        'selection__market__match__away_team',
        'selection__market__bet_type'
    )

    context = {
        'bet': bet,
        'selections': selections,
    }

    return render(request, 'betting/bet_detail.html', context)


@login_required
def user_bets_view(request):
    """
    Display user's betting history
    """
    bets = Bet.objects.filter(user=request.user).order_by('-placed_at')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        bets = bets.filter(status=status_filter)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(bets, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate statistics
    total_bets = Bet.objects.filter(user=request.user).count()
    total_stakes = Bet.objects.filter(user=request.user).aggregate(
        total=models.Sum('stake')
    )['total'] or Decimal('0.00')
    total_winnings = Bet.objects.filter(user=request.user, status='won').aggregate(
        total=models.Sum('actual_win')
    )['total'] or Decimal('0.00')

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'stats': {
            'total_bets': total_bets,
            'total_stakes': total_stakes,
            'total_winnings': total_winnings,
            'net_profit': total_winnings - total_stakes,
        },
        'bet_status_choices': Bet.BET_STATUS_CHOICES,
    }

    return render(request, 'betting/user_bets.html', context)


def live_matches_view(request):
    """
    Display live matches available for betting
    """
    live_matches = Match.objects.filter(
        status__in=['live', 'halftime'],
        betting_enabled=True,
        live_betting_enabled=True
    ).select_related('home_team', 'away_team', 'league').order_by('start_time')

    context = {
        'live_matches': live_matches,
    }

    return render(request, 'betting/live_matches.html', context)


@require_http_methods(["GET"])
def get_live_odds(request, match_id):
    """
    Get live odds for a match via AJAX
    """
    try:
        match = get_object_or_404(Match, id=match_id, live_betting_enabled=True)

        markets = Market.objects.filter(
            match=match,
            status='active'
        ).prefetch_related('selections')

        odds_data = {}
        for market in markets:
            market_data = {
                'name': market.name,
                'selections': []
            }

            for selection in market.selections.filter(status='active'):
                market_data['selections'].append({
                    'id': selection.id,
                    'name': selection.name,
                    'odds': float(selection.decimal_odds)
                })

            odds_data[market.id] = market_data

        return JsonResponse({
            'success': True,
            'odds': odds_data,
            'match_status': match.status,
            'minute': match.minute,
            'score': f"{match.home_score or 0} - {match.away_score or 0}"
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def live_betting_view(request, match_id):
    """
    Live betting interface for a specific match
    """
    match = get_object_or_404(
        Match,
        id=match_id,
        status__in=['live', 'halftime'],
        live_betting_enabled=True
    )

    # Get live betting markets
    markets = Market.objects.filter(
        match=match,
        status='active',
        bet_type__is_live_betting=True
    ).select_related('bet_type').prefetch_related('selections').order_by('bet_type__display_order')

    # Get match events
    events = MatchEvent.objects.filter(match=match).order_by('-minute', '-created_at')[:10]

    # Get user's current bet slip
    bet_slip = None
    if request.user.is_authenticated:
        bet_slip, created = BetSlip.objects.get_or_create(
            user=request.user,
            defaults={'session_key': request.session.session_key}
        )

    context = {
        'match': match,
        'markets': markets,
        'events': events,
        'bet_slip': bet_slip,
        'is_live': True,
    }

    return render(request, 'betting/live_betting.html', context)


@require_http_methods(["GET"])
def get_live_match_data(request, match_id):
    """
    Get comprehensive live match data via AJAX
    """
    try:
        match = get_object_or_404(Match, id=match_id, live_betting_enabled=True)

        # Get match data
        match_data = {
            'id': match.id,
            'status': match.status,
            'minute': match.minute,
            'period': match.period,
            'home_team': match.home_team.name,
            'away_team': match.away_team.name,
            'home_score': match.home_score or 0,
            'away_score': match.away_score or 0,
            'venue': match.venue,
        }

        # Get live markets and odds
        markets = Market.objects.filter(
            match=match,
            status='active',
            bet_type__is_live_betting=True
        ).prefetch_related('selections')

        markets_data = {}
        for market in markets:
            market_data = {
                'id': market.id,
                'name': market.name,
                'status': market.status,
                'selections': []
            }

            for selection in market.selections.filter(status='active'):
                market_data['selections'].append({
                    'id': selection.id,
                    'name': selection.name,
                    'decimal_odds': float(selection.decimal_odds),
                    'fractional_odds': selection.fractional_odds,
                    'status': selection.status
                })

            markets_data[market.id] = market_data

        # Get recent events
        recent_events = MatchEvent.objects.filter(
            match=match
        ).order_by('-minute', '-created_at')[:5]

        events_data = []
        for event in recent_events:
            events_data.append({
                'id': event.id,
                'type': event.event_type,
                'type_display': event.get_event_type_display(),
                'team': event.team.name,
                'minute': event.minute,
                'period': event.period,
                'player': event.player_name,
                'description': event.description
            })

        return JsonResponse({
            'success': True,
            'match': match_data,
            'markets': markets_data,
            'events': events_data,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def place_live_bet(request):
    """
    Place a live bet with additional validation
    """
    try:
        data = json.loads(request.body)
        selection_id = data.get('selection_id')
        stake = Decimal(str(data.get('stake', '0')))

        # Get selection and validate
        selection = get_object_or_404(Selection, id=selection_id)

        # Check if selection is for live betting
        if not selection.market.bet_type.is_live_betting:
            return JsonResponse({
                'success': False,
                'message': 'This selection is not available for live betting.'
            })

        # Check if match is live
        if not selection.market.match.is_live:
            return JsonResponse({
                'success': False,
                'message': 'Match is not currently live.'
            })

        # Check if live betting is enabled
        if not selection.market.match.live_betting_enabled:
            return JsonResponse({
                'success': False,
                'message': 'Live betting is not enabled for this match.'
            })

        # Validate stake
        if stake < selection.min_bet:
            return JsonResponse({
                'success': False,
                'message': f'Minimum stake is {selection.min_bet}.'
            })

        if stake > selection.max_bet:
            return JsonResponse({
                'success': False,
                'message': f'Maximum stake is {selection.max_bet}.'
            })

        # Check if selection is still active
        if not selection.is_active:
            return JsonResponse({
                'success': False,
                'message': 'This selection is no longer available.'
            })

        with transaction.atomic():
            # Create bet
            bet = Bet.objects.create(
                user=request.user,
                bet_type='single',
                stake=stake,
                total_odds=selection.decimal_odds,
                potential_win=stake * selection.decimal_odds
            )

            # Create bet selection
            BetSelection.objects.create(
                bet=bet,
                selection=selection,
                odds_taken=selection.decimal_odds
            )

            # Update selection statistics
            selection.total_bets += 1
            selection.total_stake += stake
            selection.save()

            return JsonResponse({
                'success': True,
                'message': f'Live bet placed successfully! Bet ID: {bet.bet_id}',
                'bet_id': bet.bet_id,
                'potential_win': float(bet.potential_win)
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error placing live bet: {str(e)}'
        })


@require_http_methods(["GET"])
def get_live_matches_feed(request):
    """
    Get feed of all live matches for dashboard
    """
    try:
        live_matches = Match.objects.filter(
            status__in=['live', 'halftime'],
            live_betting_enabled=True
        ).select_related('home_team', 'away_team', 'league')

        matches_data = []
        for match in live_matches:
            # Get main market (match winner) odds
            main_market = Market.objects.filter(
                match=match,
                bet_type__slug='match-winner',
                status='active'
            ).first()

            odds_data = {}
            if main_market:
                for selection in main_market.selections.filter(status='active'):
                    odds_data[selection.name.lower()] = float(selection.decimal_odds)

            matches_data.append({
                'id': match.id,
                'home_team': match.home_team.name,
                'away_team': match.away_team.name,
                'league': match.league.name,
                'status': match.status,
                'minute': match.minute,
                'home_score': match.home_score or 0,
                'away_score': match.away_score or 0,
                'odds': odds_data,
                'start_time': match.start_time.isoformat()
            })

        return JsonResponse({
            'success': True,
            'matches': matches_data,
            'count': len(matches_data),
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def cashout_bet(request, bet_id):
    """
    Cash out a live bet (if available)
    """
    try:
        bet = get_object_or_404(Bet, bet_id=bet_id, user=request.user)

        # Check if bet can be cashed out
        if bet.status != 'pending':
            return JsonResponse({
                'success': False,
                'message': 'Bet cannot be cashed out.'
            })

        # Check if any selections are from live matches
        live_selections = bet.selections.filter(
            selection__market__match__status__in=['live', 'halftime']
        )

        if not live_selections.exists():
            return JsonResponse({
                'success': False,
                'message': 'Cashout not available for this bet.'
            })

        # Calculate cashout value (simplified - would be more complex in reality)
        # This is a basic implementation
        cashout_percentage = Decimal('0.85')  # 85% of current value
        current_value = bet.stake * bet.total_odds * cashout_percentage

        with transaction.atomic():
            # Update bet
            bet.status = 'cashout'
            bet.cashout_value = current_value
            bet.cashout_at = timezone.now()
            bet.settled_at = timezone.now()
            bet.save()

            return JsonResponse({
                'success': True,
                'message': f'Bet cashed out for {current_value}.',
                'cashout_value': float(current_value)
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def request_bet_cancellation(request, bet_id):
    """
    Request bet cancellation
    """
    try:
        bet = get_object_or_404(Bet, bet_id=bet_id, user=request.user)

        # Check if bet already has a cancellation request
        if hasattr(bet, 'cancellation'):
            return JsonResponse({
                'success': False,
                'message': 'Cancellation already requested for this bet.'
            })

        # Check if bet can be cancelled
        cancellation = BetCancellation(bet=bet)
        can_cancel, reason = cancellation.can_be_cancelled()

        if not can_cancel:
            return JsonResponse({
                'success': False,
                'message': reason
            })

        # Create cancellation request
        data = json.loads(request.body) if request.body else {}
        reason = data.get('reason', 'user_request')
        reason_details = data.get('reason_details', '')

        cancellation.reason = reason
        cancellation.reason_details = reason_details
        cancellation.save()

        # Calculate potential refund
        net_refund = cancellation.calculate_refund()

        return JsonResponse({
            'success': True,
            'message': 'Cancellation request submitted successfully.',
            'cancellation_id': cancellation.cancellation_id,
            'potential_refund': float(net_refund),
            'cancellation_fee': float(cancellation.cancellation_fee)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def bet_cancellation_status(request, bet_id):
    """
    Check bet cancellation status
    """
    try:
        bet = get_object_or_404(Bet, bet_id=bet_id, user=request.user)

        if not hasattr(bet, 'cancellation'):
            return JsonResponse({
                'success': False,
                'message': 'No cancellation request found for this bet.'
            })

        cancellation = bet.cancellation

        return JsonResponse({
            'success': True,
            'cancellation': {
                'id': cancellation.cancellation_id,
                'status': cancellation.status,
                'reason': cancellation.get_reason_display(),
                'reason_details': cancellation.reason_details,
                'refund_amount': float(cancellation.refund_amount),
                'cancellation_fee': float(cancellation.cancellation_fee),
                'net_refund': float(cancellation.net_refund),
                'requested_at': cancellation.requested_at.isoformat(),
                'processed_at': cancellation.processed_at.isoformat() if cancellation.processed_at else None
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def user_cancellations_view(request):
    """
    Display user's bet cancellation history
    """
    cancellations = BetCancellation.objects.filter(
        bet__user=request.user
    ).select_related('bet').order_by('-requested_at')

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(cancellations, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'cancellation_status_choices': BetCancellation.CANCELLATION_STATUS_CHOICES,
    }

    return render(request, 'betting/user_cancellations.html', context)


def check_bet_cancellable(request, bet_id):
    """
    Check if a bet can be cancelled (AJAX)
    """
    try:
        if request.user.is_authenticated:
            bet = get_object_or_404(Bet, bet_id=bet_id, user=request.user)
        else:
            return JsonResponse({
                'success': False,
                'message': 'Authentication required.'
            })

        # Check if bet already has a cancellation
        if hasattr(bet, 'cancellation'):
            return JsonResponse({
                'success': False,
                'cancellable': False,
                'message': 'Cancellation already requested.',
                'cancellation_status': bet.cancellation.status
            })

        # Check cancellation rules
        cancellation = BetCancellation(bet=bet)
        can_cancel, reason = cancellation.can_be_cancelled()

        response_data = {
            'success': True,
            'cancellable': can_cancel,
            'message': reason
        }

        if can_cancel:
            # Calculate potential fees
            net_refund = cancellation.calculate_refund()
            response_data.update({
                'potential_refund': float(net_refund),
                'cancellation_fee': float(cancellation.cancellation_fee),
                'refund_amount': float(cancellation.refund_amount)
            })

        return JsonResponse(response_data)

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


def odds_comparison_view(request, match_id):
    """
    Display odds comparison for a match
    """
    match = get_object_or_404(Match, id=match_id)

    # Get available bet types for this match
    bet_types = BetType.objects.filter(
        markets__match=match,
        markets__status='active'
    ).distinct()

    # Get or create odds comparisons
    comparisons = {}
    for bet_type in bet_types:
        comparison, created = OddsComparison.objects.get_or_create(
            match=match,
            bet_type=bet_type,
            defaults={
                'comparison_name': f"{bet_type.name} - {match}",
                'description': f"Odds comparison for {bet_type.name}"
            }
        )

        # Update best odds
        comparison.update_best_odds()
        comparisons[bet_type.id] = comparison

    # Get all markets for detailed comparison
    markets = Market.objects.filter(
        match=match,
        status='active'
    ).select_related('bet_type').prefetch_related('selections')

    # Group markets by bet type
    markets_by_type = {}
    for market in markets:
        bet_type_id = market.bet_type.id
        if bet_type_id not in markets_by_type:
            markets_by_type[bet_type_id] = []
        markets_by_type[bet_type_id].append(market)

    context = {
        'match': match,
        'bet_types': bet_types,
        'comparisons': comparisons,
        'markets_by_type': markets_by_type,
    }

    return render(request, 'betting/odds_comparison.html', context)


@require_http_methods(["GET"])
def get_odds_comparison_data(request, match_id, bet_type_id):
    """
    Get odds comparison data via AJAX
    """
    try:
        match = get_object_or_404(Match, id=match_id)
        bet_type = get_object_or_404(BetType, id=bet_type_id)

        # Get or create comparison
        comparison, created = OddsComparison.objects.get_or_create(
            match=match,
            bet_type=bet_type,
            defaults={
                'comparison_name': f"{bet_type.name} - {match}",
            }
        )

        # Update best odds
        comparison.update_best_odds()

        # Get all markets for this bet type
        markets = Market.objects.filter(
            match=match,
            bet_type=bet_type,
            status='active'
        ).prefetch_related('selections')

        markets_data = []
        for market in markets:
            market_data = {
                'id': market.id,
                'name': market.name,
                'selections': []
            }

            for selection in market.selections.filter(status='active'):
                market_data['selections'].append({
                    'id': selection.id,
                    'name': selection.name,
                    'decimal_odds': float(selection.decimal_odds),
                    'fractional_odds': selection.fractional_odds,
                    'is_best': False  # Will be determined on frontend
                })

            markets_data.append(market_data)

        # Get arbitrage opportunity
        has_arbitrage, profit_margin = comparison.arbitrage_opportunity

        return JsonResponse({
            'success': True,
            'comparison': {
                'best_home_odds': float(comparison.best_home_odds) if comparison.best_home_odds else None,
                'best_draw_odds': float(comparison.best_draw_odds) if comparison.best_draw_odds else None,
                'best_away_odds': float(comparison.best_away_odds) if comparison.best_away_odds else None,
                'total_markets': comparison.total_markets,
                'odds_spread': float(comparison.odds_spread),
                'has_arbitrage': has_arbitrage,
                'profit_margin': profit_margin,
                'last_updated': comparison.last_updated.isoformat()
            },
            'markets': markets_data
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def create_odds_alert(request):
    """
    Create an odds alert for a user
    """
    try:
        data = json.loads(request.body)
        selection_id = data.get('selection_id')
        alert_type = data.get('alert_type')
        target_odds = Decimal(str(data.get('target_odds')))

        selection = get_object_or_404(Selection, id=selection_id)

        # Check if alert already exists
        existing_alert = UserOddsAlert.objects.filter(
            user=request.user,
            selection=selection,
            status='active'
        ).first()

        if existing_alert:
            return JsonResponse({
                'success': False,
                'message': 'You already have an active alert for this selection.'
            })

        # Create alert
        alert = UserOddsAlert.objects.create(
            user=request.user,
            selection=selection,
            alert_type=alert_type,
            target_odds=target_odds,
            current_odds=selection.decimal_odds
        )

        return JsonResponse({
            'success': True,
            'message': 'Odds alert created successfully.',
            'alert_id': alert.id
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def user_odds_alerts_view(request):
    """
    Display user's odds alerts
    """
    alerts = UserOddsAlert.objects.filter(
        user=request.user
    ).select_related('selection__market__match').order_by('-created_at')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        alerts = alerts.filter(status=status_filter)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(alerts, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'alert_status_choices': UserOddsAlert.ALERT_STATUS_CHOICES,
        'alert_type_choices': UserOddsAlert.ALERT_TYPE_CHOICES,
    }

    return render(request, 'betting/user_odds_alerts.html', context)


@login_required
@require_http_methods(["POST"])
def cancel_odds_alert(request, alert_id):
    """
    Cancel an odds alert
    """
    try:
        alert = get_object_or_404(UserOddsAlert, id=alert_id, user=request.user)

        if alert.status != 'active':
            return JsonResponse({
                'success': False,
                'message': 'Alert is not active.'
            })

        alert.status = 'cancelled'
        alert.save()

        return JsonResponse({
            'success': True,
            'message': 'Alert cancelled successfully.'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


def best_odds_widget_view(request):
    """
    Display best odds widget for homepage/dashboard
    """
    from datetime import timedelta

    # Get upcoming matches with best odds
    upcoming_matches = Match.objects.filter(
        status='scheduled',
        start_time__gte=timezone.now(),
        start_time__lte=timezone.now() + timedelta(days=7),
        betting_enabled=True
    ).select_related('home_team', 'away_team', 'league')[:10]

    matches_with_odds = []
    for match in upcoming_matches:
        # Get match winner odds comparison
        comparison = OddsComparison.objects.filter(
            match=match,
            bet_type__slug='match-winner'
        ).first()

        if comparison:
            comparison.update_best_odds()
            matches_with_odds.append({
                'match': match,
                'comparison': comparison
            })

    context = {
        'matches_with_odds': matches_with_odds,
    }

    return render(request, 'betting/best_odds_widget.html', context)


@login_required
@require_http_methods(["POST"])
def add_favorite(request):
    """
    Add an item to user's favorites
    """
    try:
        data = json.loads(request.body)
        favorite_type = data.get('favorite_type')
        object_id = data.get('object_id')

        # Validate favorite type
        valid_types = ['match', 'team', 'league', 'sport']
        if favorite_type not in valid_types:
            return JsonResponse({
                'success': False,
                'message': 'Invalid favorite type.'
            })

        # Get the object based on type
        favorite_object = None
        if favorite_type == 'match':
            from sports.models import Match
            favorite_object = get_object_or_404(Match, id=object_id)
        elif favorite_type == 'team':
            from sports.models import Team
            favorite_object = get_object_or_404(Team, id=object_id)
        elif favorite_type == 'league':
            from sports.models import League
            favorite_object = get_object_or_404(League, id=object_id)
        elif favorite_type == 'sport':
            from sports.models import Sport
            favorite_object = get_object_or_404(Sport, id=object_id)

        # Check if already favorited
        existing_favorite = UserFavorite.objects.filter(
            user=request.user,
            favorite_type=favorite_type,
            **{favorite_type: favorite_object}
        ).first()

        if existing_favorite:
            return JsonResponse({
                'success': False,
                'message': 'Item is already in your favorites.'
            })

        # Create favorite
        favorite_data = {
            'user': request.user,
            'favorite_type': favorite_type,
            favorite_type: favorite_object
        }

        # Add notification preferences from request
        notification_prefs = data.get('notifications', {})
        favorite_data.update({
            'notify_on_odds_change': notification_prefs.get('odds_change', True),
            'notify_on_match_start': notification_prefs.get('match_start', True),
            'notify_on_goals': notification_prefs.get('goals', False),
            'notify_on_results': notification_prefs.get('results', True),
        })

        favorite = UserFavorite.objects.create(**favorite_data)

        return JsonResponse({
            'success': True,
            'message': 'Added to favorites successfully.',
            'favorite_id': favorite.id
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def remove_favorite(request, favorite_id):
    """
    Remove an item from user's favorites
    """
    try:
        favorite = get_object_or_404(UserFavorite, id=favorite_id, user=request.user)
        favorite.delete()

        return JsonResponse({
            'success': True,
            'message': 'Removed from favorites successfully.'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def user_favorites_view(request):
    """
    Display user's favorites
    """
    # Get filter parameters
    favorite_type = request.GET.get('type', 'all')
    group_id = request.GET.get('group')

    # Base queryset
    favorites = UserFavorite.objects.filter(user=request.user)

    # Apply filters
    if favorite_type != 'all':
        favorites = favorites.filter(favorite_type=favorite_type)

    if group_id:
        favorites = favorites.filter(group_memberships__group_id=group_id)

    # Select related objects for efficiency
    favorites = favorites.select_related(
        'match__home_team', 'match__away_team', 'match__league',
        'team', 'league', 'sport'
    ).prefetch_related('group_memberships__group')

    # Get user's favorite groups
    favorite_groups = FavoriteGroup.objects.filter(user=request.user)

    # Get upcoming matches for favorite teams/leagues
    upcoming_matches = []
    if favorite_type in ['all', 'team', 'league']:
        from datetime import timedelta
        from sports.models import Match

        team_favorites = favorites.filter(favorite_type='team')
        league_favorites = favorites.filter(favorite_type='league')

        team_ids = [f.team.id for f in team_favorites if f.team]
        league_ids = [f.league.id for f in league_favorites if f.league]

        upcoming_matches = Match.objects.filter(
            models.Q(home_team_id__in=team_ids) |
            models.Q(away_team_id__in=team_ids) |
            models.Q(league_id__in=league_ids),
            status='scheduled',
            start_time__gte=timezone.now(),
            start_time__lte=timezone.now() + timedelta(days=7)
        ).select_related('home_team', 'away_team', 'league')[:10]

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(favorites, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'favorite_type': favorite_type,
        'group_id': group_id,
        'favorite_groups': favorite_groups,
        'upcoming_matches': upcoming_matches,
        'favorite_type_choices': UserFavorite.FAVORITE_TYPE_CHOICES,
    }

    return render(request, 'betting/user_favorites.html', context)


@login_required
@require_http_methods(["GET"])
def check_favorite_status(request):
    """
    Check if items are favorited by the user
    """
    try:
        favorite_type = request.GET.get('type')
        object_ids = request.GET.getlist('ids[]')

        if not favorite_type or not object_ids:
            return JsonResponse({
                'success': False,
                'message': 'Missing required parameters.'
            })

        # Get favorited items
        favorites = UserFavorite.objects.filter(
            user=request.user,
            favorite_type=favorite_type
        )

        # Build filter based on type
        if favorite_type == 'match':
            favorites = favorites.filter(match_id__in=object_ids)
            favorited_ids = [str(f.match_id) for f in favorites]
        elif favorite_type == 'team':
            favorites = favorites.filter(team_id__in=object_ids)
            favorited_ids = [str(f.team_id) for f in favorites]
        elif favorite_type == 'league':
            favorites = favorites.filter(league_id__in=object_ids)
            favorited_ids = [str(f.league_id) for f in favorites]
        elif favorite_type == 'sport':
            favorites = favorites.filter(sport_id__in=object_ids)
            favorited_ids = [str(f.sport_id) for f in favorites]
        else:
            return JsonResponse({
                'success': False,
                'message': 'Invalid favorite type.'
            })

        # Create response mapping
        status_map = {}
        for object_id in object_ids:
            status_map[object_id] = object_id in favorited_ids

        return JsonResponse({
            'success': True,
            'favorites': status_map
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def create_favorite_group(request):
    """
    Create a new favorite group
    """
    try:
        data = json.loads(request.body)
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        is_default = data.get('is_default', False)

        if not name:
            return JsonResponse({
                'success': False,
                'message': 'Group name is required.'
            })

        # Check if group name already exists for user
        if FavoriteGroup.objects.filter(user=request.user, name=name).exists():
            return JsonResponse({
                'success': False,
                'message': 'A group with this name already exists.'
            })

        # If setting as default, unset other default groups
        if is_default:
            FavoriteGroup.objects.filter(user=request.user, is_default=True).update(is_default=False)

        # Create group
        group = FavoriteGroup.objects.create(
            user=request.user,
            name=name,
            description=description,
            is_default=is_default
        )

        return JsonResponse({
            'success': True,
            'message': 'Favorite group created successfully.',
            'group': {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'is_default': group.is_default,
                'favorites_count': 0
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def add_to_favorite_group(request):
    """
    Add favorites to a group
    """
    try:
        data = json.loads(request.body)
        group_id = data.get('group_id')
        favorite_ids = data.get('favorite_ids', [])

        group = get_object_or_404(FavoriteGroup, id=group_id, user=request.user)

        # Get favorites
        favorites = UserFavorite.objects.filter(
            id__in=favorite_ids,
            user=request.user
        )

        added_count = 0
        for favorite in favorites:
            # Check if already in group
            if not FavoriteGroupMembership.objects.filter(
                group=group,
                favorite=favorite
            ).exists():
                FavoriteGroupMembership.objects.create(
                    group=group,
                    favorite=favorite,
                    added_by=request.user
                )
                added_count += 1

        return JsonResponse({
            'success': True,
            'message': f'Added {added_count} favorites to group.',
            'added_count': added_count
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def favorite_groups_view(request):
    """
    Display user's favorite groups
    """
    groups = FavoriteGroup.objects.filter(user=request.user).annotate(
        favorites_count=models.Count('memberships')
    )

    context = {
        'groups': groups,
    }

    return render(request, 'betting/favorite_groups.html', context)


@login_required
def favorite_group_detail_view(request, group_id):
    """
    Display favorites in a specific group
    """
    group = get_object_or_404(FavoriteGroup, id=group_id, user=request.user)

    # Get group memberships with related favorites
    memberships = FavoriteGroupMembership.objects.filter(
        group=group
    ).select_related(
        'favorite__match__home_team',
        'favorite__match__away_team',
        'favorite__match__league',
        'favorite__team',
        'favorite__league',
        'favorite__sport'
    ).order_by('-added_at')

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(memberships, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'group': group,
        'page_obj': page_obj,
    }

    return render(request, 'betting/favorite_group_detail.html', context)


@login_required
def favorite_notifications_view(request):
    """
    Display user's favorite notifications
    """
    notifications = FavoriteNotification.objects.filter(
        user=request.user
    ).select_related('favorite').order_by('-created_at')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        notifications = notifications.filter(status=status_filter)

    # Filter by notification type if provided
    type_filter = request.GET.get('type')
    if type_filter:
        notifications = notifications.filter(notification_type=type_filter)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(notifications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'notification_status_choices': FavoriteNotification.NOTIFICATION_STATUS_CHOICES,
        'notification_type_choices': FavoriteNotification.NOTIFICATION_TYPE_CHOICES,
    }

    return render(request, 'betting/favorite_notifications.html', context)


@login_required
def live_notifications_view(request):
    """
    Display user's live bet notifications
    """
    notifications = LiveBetNotification.objects.filter(
        user=request.user
    ).select_related('match', 'market', 'bet').order_by('-created_at')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        notifications = notifications.filter(delivery_status=status_filter)

    # Filter by type if provided
    type_filter = request.GET.get('type')
    if type_filter:
        notifications = notifications.filter(notification_type=type_filter)

    # Filter by priority if provided
    priority_filter = request.GET.get('priority')
    if priority_filter:
        notifications = notifications.filter(priority=priority_filter)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(notifications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'priority_filter': priority_filter,
        'notification_status_choices': LiveBetNotification.DELIVERY_STATUS_CHOICES,
        'notification_type_choices': LiveBetNotification.NOTIFICATION_TYPE_CHOICES,
        'priority_choices': LiveBetNotification.PRIORITY_CHOICES,
    }

    return render(request, 'betting/live_notifications.html', context)


@login_required
@require_http_methods(["GET"])
def get_live_notifications(request):
    """
    Get live notifications via AJAX for real-time updates
    """
    try:
        # Get timestamp for filtering new notifications
        since = request.GET.get('since')
        if since:
            from datetime import datetime
            since_datetime = datetime.fromisoformat(since.replace('Z', '+00:00'))
            notifications = LiveBetNotification.objects.filter(
                user=request.user,
                created_at__gt=since_datetime
            )
        else:
            # Get recent notifications (last 5 minutes)
            from datetime import timedelta
            recent_time = timezone.now() - timedelta(minutes=5)
            notifications = LiveBetNotification.objects.filter(
                user=request.user,
                created_at__gte=recent_time
            )

        notifications = notifications.select_related(
            'match', 'market', 'bet'
        ).order_by('-created_at')[:20]

        notifications_data = []
        for notification in notifications:
            notification_data = {
                'id': notification.id,
                'type': notification.notification_type,
                'title': notification.title,
                'message': notification.message,
                'priority': notification.priority,
                'data': notification.data,
                'created_at': notification.created_at.isoformat(),
                'delivery_status': notification.delivery_status,
            }

            # Add related object data
            if notification.match:
                notification_data['match'] = {
                    'id': notification.match.id,
                    'home_team': notification.match.home_team.name,
                    'away_team': notification.match.away_team.name,
                }

            if notification.market:
                notification_data['market'] = {
                    'id': notification.market.id,
                    'name': notification.market.name,
                }

            if notification.bet:
                notification_data['bet'] = {
                    'id': notification.bet.bet_id,
                    'stake': float(notification.bet.stake),
                    'status': notification.bet.status,
                }

            notifications_data.append(notification_data)

        return JsonResponse({
            'success': True,
            'notifications': notifications_data,
            'count': len(notifications_data),
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """
    Mark a notification as read/delivered
    """
    try:
        notification = get_object_or_404(
            LiveBetNotification,
            id=notification_id,
            user=request.user
        )

        notification.mark_as_delivered()

        return JsonResponse({
            'success': True,
            'message': 'Notification marked as read.'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def notification_preferences_view(request):
    """
    Display and update user notification preferences
    """
    preferences, created = UserNotificationPreference.objects.get_or_create(
        user=request.user
    )

    if request.method == 'POST':
        # Update preferences
        preferences.enable_live_notifications = request.POST.get('enable_live_notifications') == 'on'
        preferences.enable_push_notifications = request.POST.get('enable_push_notifications') == 'on'
        preferences.enable_email_notifications = request.POST.get('enable_email_notifications') == 'on'
        preferences.enable_sms_notifications = request.POST.get('enable_sms_notifications') == 'on'

        preferences.notify_odds_changes = request.POST.get('notify_odds_changes') == 'on'
        preferences.notify_new_markets = request.POST.get('notify_new_markets') == 'on'
        preferences.notify_market_suspensions = request.POST.get('notify_market_suspensions') == 'on'
        preferences.notify_match_events = request.POST.get('notify_match_events') == 'on'
        preferences.notify_bet_results = request.POST.get('notify_bet_results') == 'on'
        preferences.notify_cashout_opportunities = request.POST.get('notify_cashout_opportunities') == 'on'
        preferences.notify_live_streams = request.POST.get('notify_live_streams') == 'on'

        # Frequency settings
        max_notifications = request.POST.get('max_notifications_per_hour')
        if max_notifications:
            preferences.max_notifications_per_hour = int(max_notifications)

        quiet_start = request.POST.get('quiet_hours_start')
        if quiet_start:
            from datetime import datetime
            preferences.quiet_hours_start = datetime.strptime(quiet_start, '%H:%M').time()

        quiet_end = request.POST.get('quiet_hours_end')
        if quiet_end:
            from datetime import datetime
            preferences.quiet_hours_end = datetime.strptime(quiet_end, '%H:%M').time()

        # Match preferences
        preferences.only_favorite_matches = request.POST.get('only_favorite_matches') == 'on'
        preferences.only_active_bets = request.POST.get('only_active_bets') == 'on'

        min_odds_change = request.POST.get('minimum_odds_change')
        if min_odds_change:
            preferences.minimum_odds_change = Decimal(min_odds_change)

        preferences.save()

        return JsonResponse({
            'success': True,
            'message': 'Notification preferences updated successfully.'
        })

    context = {
        'preferences': preferences,
    }

    return render(request, 'betting/notification_preferences.html', context)


@login_required
def virtual_sports_view(request):
    """
    Display available virtual sports
    """
    virtual_sports = VirtualSport.objects.filter(is_active=True).prefetch_related('teams')

    context = {
        'virtual_sports': virtual_sports,
    }

    return render(request, 'betting/virtual_sports.html', context)


@login_required
def virtual_sport_detail(request, sport_id):
    """
    Display virtual sport details with upcoming matches
    """
    virtual_sport = get_object_or_404(VirtualSport, id=sport_id, is_active=True)

    # Get upcoming matches
    upcoming_matches = VirtualMatch.objects.filter(
        virtual_sport=virtual_sport,
        status='scheduled',
        start_time__gte=timezone.now()
    ).select_related('home_team', 'away_team').order_by('start_time')[:20]

    # Get recent results
    recent_results = VirtualMatch.objects.filter(
        virtual_sport=virtual_sport,
        status='finished'
    ).select_related('home_team', 'away_team').order_by('-start_time')[:10]

    # Get team standings
    teams = virtual_sport.teams.filter(is_active=True).order_by('-wins', '-goal_difference')

    context = {
        'virtual_sport': virtual_sport,
        'upcoming_matches': upcoming_matches,
        'recent_results': recent_results,
        'teams': teams,
    }

    return render(request, 'betting/virtual_sport_detail.html', context)


@login_required
def virtual_match_detail(request, match_id):
    """
    Display virtual match details with betting markets
    """
    match = get_object_or_404(
        VirtualMatch,
        id=match_id,
        virtual_sport__is_active=True
    )

    # Get betting markets
    markets = match.markets.filter(status='active').prefetch_related('selections')

    # Get user's existing bets on this match
    user_bets = VirtualBet.objects.filter(
        user=request.user,
        selections__selection__market__virtual_match=match
    ).distinct()

    context = {
        'match': match,
        'markets': markets,
        'user_bets': user_bets,
    }

    return render(request, 'betting/virtual_match_detail.html', context)


@login_required
@require_http_methods(["POST"])
def place_virtual_bet(request):
    """
    Place a bet on virtual sports
    """
    try:
        with transaction.atomic():
            # Get form data
            selection_ids = request.POST.getlist('selections')
            stake = Decimal(request.POST.get('stake', '0'))
            bet_type = request.POST.get('bet_type', 'single')

            if not selection_ids:
                return JsonResponse({
                    'success': False,
                    'message': 'No selections provided.'
                })

            if stake <= 0:
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid stake amount.'
                })

            # Get selections
            selections = VirtualSelection.objects.filter(
                id__in=selection_ids,
                status='active',
                market__status='active'
            ).select_related('market__virtual_match')

            if len(selections) != len(selection_ids):
                return JsonResponse({
                    'success': False,
                    'message': 'Some selections are no longer available.'
                })

            # Check if matches haven't started
            for selection in selections:
                if selection.market.virtual_match.start_time <= timezone.now():
                    return JsonResponse({
                        'success': False,
                        'message': f'Match {selection.market.virtual_match} has already started.'
                    })

            # Calculate total odds
            total_odds = Decimal('1.00')
            for selection in selections:
                total_odds *= selection.decimal_odds

            # Check minimum/maximum bet amounts
            virtual_sport = selections[0].market.virtual_match.virtual_sport
            if stake < virtual_sport.min_bet_amount:
                return JsonResponse({
                    'success': False,
                    'message': f'Minimum bet amount is {virtual_sport.min_bet_amount}.'
                })

            if stake > virtual_sport.max_bet_amount:
                return JsonResponse({
                    'success': False,
                    'message': f'Maximum bet amount is {virtual_sport.max_bet_amount}.'
                })

            # Create bet
            bet = VirtualBet.objects.create(
                user=request.user,
                bet_type=bet_type,
                stake=stake,
                total_odds=total_odds,
                status='active'
            )

            # Create bet selections
            for selection in selections:
                VirtualBetSelection.objects.create(
                    bet=bet,
                    selection=selection,
                    odds_at_placement=selection.decimal_odds
                )

                # Update selection statistics
                selection.total_bets += 1
                selection.total_stake += stake if bet_type == 'single' else stake / len(selections)
                selection.save()

            return JsonResponse({
                'success': True,
                'message': 'Bet placed successfully!',
                'bet_id': bet.bet_id,
                'potential_winnings': float(bet.potential_winnings)
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
def virtual_bet_history(request):
    """
    Display user's virtual bet history
    """
    bets = VirtualBet.objects.filter(user=request.user).prefetch_related(
        'selections__selection__market__virtual_match'
    ).order_by('-created_at')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        bets = bets.filter(status=status_filter)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(bets, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'bet_status_choices': VirtualBet.BET_STATUS_CHOICES,
    }

    return render(request, 'betting/virtual_bet_history.html', context)


@login_required
@require_http_methods(["GET"])
def get_virtual_match_odds(request, match_id):
    """
    Get current odds for a virtual match via AJAX
    """
    try:
        match = get_object_or_404(VirtualMatch, id=match_id)

        markets_data = []
        for market in match.markets.filter(status='active'):
            selections_data = []
            for selection in market.selections.filter(status='active'):
                selections_data.append({
                    'id': selection.id,
                    'name': selection.name,
                    'decimal_odds': float(selection.decimal_odds),
                    'fractional_odds': selection.fractional_odds,
                })

            markets_data.append({
                'id': market.id,
                'name': market.name,
                'selections': selections_data,
            })

        return JsonResponse({
            'success': True,
            'match_id': match.id,
            'markets': markets_data,
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@login_required
@require_http_methods(["POST"])
def simulate_virtual_match(request, match_id):
    """
    Manually trigger virtual match simulation (for testing)
    """
    try:
        match = get_object_or_404(VirtualMatch, id=match_id, status='scheduled')

        # Only allow simulation if match start time has passed
        if match.start_time > timezone.now():
            return JsonResponse({
                'success': False,
                'message': 'Match has not started yet.'
            })

        # Simulate the match
        if match.simulate_match():
            return JsonResponse({
                'success': True,
                'message': 'Match simulated successfully.',
                'result': {
                    'home_score': match.home_score,
                    'away_score': match.away_score,
                    'winner': match.winner,
                    'events': match.events,
                }
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Failed to simulate match.'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })
