from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid

class PaymentMethod(models.Model):
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=20, unique=True)
    is_active = models.BooleanField(default=True)
    priority = models.IntegerField(default=0)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['priority', 'name']

    def __str__(self):
        return self.name

class Wallet(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    last_updated = models.DateTimeField(auto_now=True)
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}'s wallet - KES {self.balance}"

    def credit(self, amount, description=""):
        """Credit the wallet with the specified amount"""
        self.balance += Decimal(str(amount))
        self.save()
        return self.balance

    def debit(self, amount, description=""):
        """Debit the wallet with the specified amount"""
        if self.balance >= Decimal(str(amount)):
            self.balance -= Decimal(str(amount))
            self.save()
            return self.balance
        else:
            raise ValueError("Insufficient balance")

    def has_sufficient_balance(self, amount):
        """Check if wallet has sufficient balance"""
        return self.balance >= Decimal(str(amount))

class Transaction(models.Model):
    TRANSACTION_TYPES = [
        ('DEPOSIT', 'Deposit'),
        ('WITHDRAWAL', 'Withdrawal'),
        ('BET_PLACEMENT', 'Bet Placement'),
        ('BET_WINNING', 'Bet Winning'),
        ('BONUS', 'Bonus'),
        ('REFUND', 'Refund')
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled')
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payment_transactions')
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.PROTECT)
    reference = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    fee = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    balance_before = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    balance_after = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    external_reference = models.CharField(max_length=100, blank=True, null=True)  # For M-Pesa receipt numbers
    callback_data = models.JSONField(blank=True, null=True)  # Store callback response data

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['reference']),
        ]

    def __str__(self):
        return f"{self.reference} - {self.transaction_type} - KES {self.amount}"

    @property
    def net_amount(self):
        """Calculate net amount after fees"""
        return self.amount - self.fee

    def mark_completed(self):
        """Mark transaction as completed and update wallet"""
        if self.status != 'COMPLETED':
            self.status = 'COMPLETED'
            self.balance_before = self.wallet.balance

            if self.transaction_type == 'DEPOSIT':
                self.wallet.credit(self.net_amount)
            elif self.transaction_type == 'WITHDRAWAL':
                self.wallet.debit(self.amount)  # Full amount including fees

            self.balance_after = self.wallet.balance
            self.save()

    def mark_failed(self, reason=""):
        """Mark transaction as failed"""
        self.status = 'FAILED'
        if reason:
            self.description = f"{self.description}\nFailure reason: {reason}".strip()
        self.save()

class MPesaTransaction(models.Model):
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE, related_name='mpesa_transaction')
    phone_number = models.CharField(max_length=15, help_text="Phone number in format 254XXXXXXXXX")
    mpesa_receipt_number = models.CharField(max_length=30, unique=True, null=True, blank=True)
    checkout_request_id = models.CharField(max_length=100, unique=True)
    merchant_request_id = models.CharField(max_length=100)
    result_code = models.CharField(max_length=5, null=True, blank=True)
    result_description = models.TextField(null=True, blank=True)
    account_reference = models.CharField(max_length=50, blank=True)  # Account reference sent to M-Pesa
    transaction_desc = models.CharField(max_length=100, blank=True)  # Transaction description
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['checkout_request_id']),
            models.Index(fields=['mpesa_receipt_number']),
            models.Index(fields=['phone_number', '-created_at']),
        ]

    def __str__(self):
        receipt = self.mpesa_receipt_number or "Pending"
        return f"M-Pesa {receipt} - {self.phone_number}"

    @property
    def is_successful(self):
        """Check if M-Pesa transaction was successful"""
        return self.result_code == '0'

    @property
    def formatted_phone(self):
        """Format phone number for display"""
        if self.phone_number.startswith('254'):
            return f"+{self.phone_number}"
        return self.phone_number

class PaymentLimit(models.Model):
    LIMIT_TYPES = [
        ('MIN_DEPOSIT', 'Minimum Deposit'),
        ('MAX_DEPOSIT', 'Maximum Deposit'),
        ('MIN_WITHDRAWAL', 'Minimum Withdrawal'),
        ('MAX_WITHDRAWAL', 'Maximum Withdrawal'),
        ('DAILY_DEPOSIT', 'Daily Deposit Limit'),
        ('DAILY_WITHDRAWAL', 'Daily Withdrawal Limit')
    ]

    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)
    limit_type = models.CharField(max_length=20, choices=LIMIT_TYPES)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['payment_method', 'limit_type']

    def __str__(self):
        return f"{self.payment_method.name} - {self.get_limit_type_display()}: KES {self.amount}"


class UserPaymentStats(models.Model):
    """Track daily payment statistics for users"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    date = models.DateField()
    total_deposits = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_withdrawals = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    deposit_count = models.IntegerField(default=0)
    withdrawal_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'date']
        indexes = [
            models.Index(fields=['user', 'date']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.date}"


class PaymentNotification(models.Model):
    """Store payment notifications and alerts"""
    NOTIFICATION_TYPES = [
        ('DEPOSIT_SUCCESS', 'Deposit Successful'),
        ('DEPOSIT_FAILED', 'Deposit Failed'),
        ('WITHDRAWAL_SUCCESS', 'Withdrawal Successful'),
        ('WITHDRAWAL_FAILED', 'Withdrawal Failed'),
        ('LOW_BALANCE', 'Low Balance Alert'),
        ('LIMIT_EXCEEDED', 'Limit Exceeded'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, null=True, blank=True)
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=100)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_notification_type_display()}"


class Deposit(models.Model):
    """Separate model for deposit transactions"""
    DEPOSIT_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='deposits')
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE, related_name='deposits')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    fee_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=15, decimal_places=2)  # amount - fee
    status = models.CharField(max_length=20, choices=DEPOSIT_STATUS_CHOICES, default='PENDING')
    reference = models.CharField(max_length=100, unique=True)
    external_reference = models.CharField(max_length=100, blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    description = models.TextField(blank=True)
    callback_data = models.JSONField(default=dict, blank=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'deposits'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['reference']),
            models.Index(fields=['external_reference']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Deposit {self.reference} - {self.user.username} - KES {self.amount}"

    def save(self, *args, **kwargs):
        if not self.net_amount:
            self.net_amount = self.amount - self.fee_amount
        super().save(*args, **kwargs)

    def mark_completed(self):
        """Mark deposit as completed and credit wallet"""
        if self.status != 'COMPLETED':
            self.status = 'COMPLETED'
            self.processed_at = timezone.now()
            self.wallet.credit(self.net_amount, f"Deposit {self.reference}")
            self.save()

            # Create transaction record
            Transaction.objects.create(
                user=self.user,
                wallet=self.wallet,
                transaction_type='DEPOSIT',
                amount=self.amount,
                fee_amount=self.fee_amount,
                net_amount=self.net_amount,
                status='COMPLETED',
                reference=self.reference,
                external_reference=self.external_reference,
                description=f"Deposit via {self.payment_method.name}",
                balance_before=self.wallet.balance - self.net_amount,
                balance_after=self.wallet.balance
            )


class Withdrawal(models.Model):
    """Separate model for withdrawal transactions"""
    WITHDRAWAL_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('APPROVED', 'Approved'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
        ('REJECTED', 'Rejected'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='withdrawals')
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE, related_name='withdrawals')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    fee_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=15, decimal_places=2)  # amount - fee
    status = models.CharField(max_length=20, choices=WITHDRAWAL_STATUS_CHOICES, default='PENDING')
    reference = models.CharField(max_length=100, unique=True)
    external_reference = models.CharField(max_length=100, blank=True, null=True)
    phone_number = models.CharField(max_length=20)  # Required for M-Pesa withdrawals
    description = models.TextField(blank=True)
    callback_data = models.JSONField(default=dict, blank=True)
    approval_required = models.BooleanField(default=False)
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_withdrawals')
    approved_at = models.DateTimeField(null=True, blank=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'withdrawals'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['reference']),
            models.Index(fields=['external_reference']),
            models.Index(fields=['created_at']),
            models.Index(fields=['status', 'approval_required']),
        ]

    def __str__(self):
        return f"Withdrawal {self.reference} - {self.user.username} - KES {self.amount}"

    def save(self, *args, **kwargs):
        if not self.net_amount:
            self.net_amount = self.amount - self.fee_amount
        super().save(*args, **kwargs)

    def mark_completed(self):
        """Mark withdrawal as completed and debit wallet"""
        if self.status != 'COMPLETED':
            self.status = 'COMPLETED'
            self.processed_at = timezone.now()
            self.wallet.debit(self.amount, f"Withdrawal {self.reference}")
            self.save()

            # Create transaction record
            Transaction.objects.create(
                user=self.user,
                wallet=self.wallet,
                transaction_type='WITHDRAWAL',
                amount=self.amount,
                fee_amount=self.fee_amount,
                net_amount=self.net_amount,
                status='COMPLETED',
                reference=self.reference,
                external_reference=self.external_reference,
                description=f"Withdrawal via {self.payment_method.name}",
                balance_before=self.wallet.balance + self.amount,
                balance_after=self.wallet.balance
            )


class TransactionFee(models.Model):
    """Model to define transaction fees for different payment methods and amounts"""
    FEE_TYPE_CHOICES = [
        ('FIXED', 'Fixed Amount'),
        ('PERCENTAGE', 'Percentage'),
        ('TIERED', 'Tiered (Amount Range)'),
    ]

    TRANSACTION_TYPE_CHOICES = [
        ('DEPOSIT', 'Deposit'),
        ('WITHDRAWAL', 'Withdrawal'),
    ]

    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE, related_name='fees')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    fee_type = models.CharField(max_length=20, choices=FEE_TYPE_CHOICES)
    min_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    max_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    fee_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)  # Fixed fee or percentage
    min_fee = models.DecimalField(max_digits=15, decimal_places=2, default=0)  # Minimum fee for percentage
    max_fee = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)  # Maximum fee
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'transaction_fees'
        ordering = ['payment_method', 'transaction_type', 'min_amount']
        indexes = [
            models.Index(fields=['payment_method', 'transaction_type', 'is_active']),
            models.Index(fields=['min_amount', 'max_amount']),
        ]

    def __str__(self):
        return f"{self.payment_method.name} - {self.transaction_type} - {self.get_fee_type_display()}"

    def calculate_fee(self, amount):
        """Calculate fee for given amount"""
        amount = Decimal(str(amount))

        if self.fee_type == 'FIXED':
            return self.fee_amount
        elif self.fee_type == 'PERCENTAGE':
            fee = amount * (self.fee_amount / 100)
            if self.min_fee and fee < self.min_fee:
                fee = self.min_fee
            if self.max_fee and fee > self.max_fee:
                fee = self.max_fee
            return fee
        elif self.fee_type == 'TIERED':
            # For tiered fees, return the fixed amount for this tier
            return self.fee_amount

        return Decimal('0')

    @classmethod
    def get_fee_for_transaction(cls, payment_method, transaction_type, amount):
        """Get applicable fee for a transaction"""
        amount = Decimal(str(amount))

        # Find applicable fee structure
        fees = cls.objects.filter(
            payment_method=payment_method,
            transaction_type=transaction_type,
            is_active=True,
            min_amount__lte=amount
        ).filter(
            models.Q(max_amount__isnull=True) | models.Q(max_amount__gte=amount)
        ).order_by('min_amount').first()

        if fees:
            return fees.calculate_fee(amount)

        return Decimal('0')


class AirtelTransaction(models.Model):
    """Airtel Money specific transaction details"""
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE, related_name='airtel_details')
    airtel_transaction_id = models.CharField(max_length=100, unique=True)
    reference = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15)
    status = models.CharField(max_length=50, blank=True, null=True)
    status_description = models.CharField(max_length=255, blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'payments_airtel_transaction'
        indexes = [
            models.Index(fields=['airtel_transaction_id']),
            models.Index(fields=['reference']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Airtel {self.airtel_transaction_id}"


class CardTransaction(models.Model):
    """Card payment specific transaction details"""
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE, related_name='card_details')
    payment_intent_id = models.CharField(max_length=100, unique=True)
    client_secret = models.CharField(max_length=255, blank=True, null=True)
    payment_method_id = models.CharField(max_length=100, blank=True, null=True)
    card_last_four = models.CharField(max_length=4, blank=True, null=True)
    card_brand = models.CharField(max_length=20, blank=True, null=True)
    customer_email = models.EmailField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'payments_card_transaction'
        indexes = [
            models.Index(fields=['payment_intent_id']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Card {self.payment_intent_id}"


class BankTransaction(models.Model):
    """Bank transfer specific transaction details"""
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE, related_name='bank_details')
    order_tracking_id = models.CharField(max_length=100, unique=True)
    merchant_reference = models.CharField(max_length=100)
    bank_code = models.CharField(max_length=10, blank=True, null=True)
    account_number = models.CharField(max_length=50, blank=True, null=True)
    account_name = models.CharField(max_length=100, blank=True, null=True)
    customer_email = models.EmailField()
    customer_phone = models.CharField(max_length=15)
    redirect_url = models.URLField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'payments_bank_transaction'
        indexes = [
            models.Index(fields=['order_tracking_id']),
            models.Index(fields=['merchant_reference']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Bank {self.order_tracking_id}"
