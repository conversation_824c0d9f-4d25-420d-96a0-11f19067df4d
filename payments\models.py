from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _

class PaymentMethod(models.Model):
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=20, unique=True)
    is_active = models.BooleanField(default=True)
    priority = models.IntegerField(default=0)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['priority', 'name']

    def __str__(self):
        return self.name

class Wallet(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    balance = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    last_updated = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.user.username}'s wallet"

class Transaction(models.Model):
    TRANSACTION_TYPES = [
        ('DEPOSIT', 'Deposit'),
        ('WITHDRAWAL', 'Withdrawal'),
        ('BET_PLACEMENT', 'Bet Placement'),
        ('BET_WINNING', 'Bet Winning'),
        ('BONUS', 'Bonus'),
        ('REFUND', 'Refund')
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled')
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.PROTECT)
    reference = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    fee = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.reference} - {self.transaction_type}"

class MPesaTransaction(models.Model):
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=15)
    mpesa_receipt_number = models.CharField(max_length=30, unique=True, null=True, blank=True)
    checkout_request_id = models.CharField(max_length=100, unique=True)
    merchant_request_id = models.CharField(max_length=100)
    result_code = models.CharField(max_length=5, null=True, blank=True)
    result_description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"M-Pesa Transaction {self.mpesa_receipt_number}"

class PaymentLimit(models.Model):
    LIMIT_TYPES = [
        ('MIN_DEPOSIT', 'Minimum Deposit'),
        ('MAX_DEPOSIT', 'Maximum Deposit'),
        ('MIN_WITHDRAWAL', 'Minimum Withdrawal'),
        ('MAX_WITHDRAWAL', 'Maximum Withdrawal'),
        ('DAILY_DEPOSIT', 'Daily Deposit Limit'),
        ('DAILY_WITHDRAWAL', 'Daily Withdrawal Limit')
    ]

    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)
    limit_type = models.CharField(max_length=20, choices=LIMIT_TYPES)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['payment_method', 'limit_type']

    def __str__(self):
        return f"{self.payment_method.name} - {self.limit_type}"
