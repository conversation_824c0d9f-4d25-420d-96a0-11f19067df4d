from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid

class PaymentMethod(models.Model):
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=20, unique=True)
    is_active = models.BooleanField(default=True)
    priority = models.IntegerField(default=0)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['priority', 'name']

    def __str__(self):
        return self.name

class Wallet(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    balance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    last_updated = models.DateTimeField(auto_now=True)
    is_active = models.<PERSON><PERSON>anField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}'s wallet - KES {self.balance}"

    def credit(self, amount, description=""):
        """Credit the wallet with the specified amount"""
        self.balance += Decimal(str(amount))
        self.save()
        return self.balance

    def debit(self, amount, description=""):
        """Debit the wallet with the specified amount"""
        if self.balance >= Decimal(str(amount)):
            self.balance -= Decimal(str(amount))
            self.save()
            return self.balance
        else:
            raise ValueError("Insufficient balance")

    def has_sufficient_balance(self, amount):
        """Check if wallet has sufficient balance"""
        return self.balance >= Decimal(str(amount))

class Transaction(models.Model):
    TRANSACTION_TYPES = [
        ('DEPOSIT', 'Deposit'),
        ('WITHDRAWAL', 'Withdrawal'),
        ('BET_PLACEMENT', 'Bet Placement'),
        ('BET_WINNING', 'Bet Winning'),
        ('BONUS', 'Bonus'),
        ('REFUND', 'Refund')
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled')
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payment_transactions')
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.PROTECT)
    reference = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    fee = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    balance_before = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    balance_after = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    external_reference = models.CharField(max_length=100, blank=True, null=True)  # For M-Pesa receipt numbers
    callback_data = models.JSONField(blank=True, null=True)  # Store callback response data

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['reference']),
        ]

    def __str__(self):
        return f"{self.reference} - {self.transaction_type} - KES {self.amount}"

    @property
    def net_amount(self):
        """Calculate net amount after fees"""
        return self.amount - self.fee

    def mark_completed(self):
        """Mark transaction as completed and update wallet"""
        if self.status != 'COMPLETED':
            self.status = 'COMPLETED'
            self.balance_before = self.wallet.balance

            if self.transaction_type == 'DEPOSIT':
                self.wallet.credit(self.net_amount)
            elif self.transaction_type == 'WITHDRAWAL':
                self.wallet.debit(self.amount)  # Full amount including fees

            self.balance_after = self.wallet.balance
            self.save()

    def mark_failed(self, reason=""):
        """Mark transaction as failed"""
        self.status = 'FAILED'
        if reason:
            self.description = f"{self.description}\nFailure reason: {reason}".strip()
        self.save()

class MPesaTransaction(models.Model):
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE, related_name='mpesa_transaction')
    phone_number = models.CharField(max_length=15, help_text="Phone number in format 254XXXXXXXXX")
    mpesa_receipt_number = models.CharField(max_length=30, unique=True, null=True, blank=True)
    checkout_request_id = models.CharField(max_length=100, unique=True)
    merchant_request_id = models.CharField(max_length=100)
    result_code = models.CharField(max_length=5, null=True, blank=True)
    result_description = models.TextField(null=True, blank=True)
    account_reference = models.CharField(max_length=50, blank=True)  # Account reference sent to M-Pesa
    transaction_desc = models.CharField(max_length=100, blank=True)  # Transaction description
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['checkout_request_id']),
            models.Index(fields=['mpesa_receipt_number']),
            models.Index(fields=['phone_number', '-created_at']),
        ]

    def __str__(self):
        receipt = self.mpesa_receipt_number or "Pending"
        return f"M-Pesa {receipt} - {self.phone_number}"

    @property
    def is_successful(self):
        """Check if M-Pesa transaction was successful"""
        return self.result_code == '0'

    @property
    def formatted_phone(self):
        """Format phone number for display"""
        if self.phone_number.startswith('254'):
            return f"+{self.phone_number}"
        return self.phone_number

class PaymentLimit(models.Model):
    LIMIT_TYPES = [
        ('MIN_DEPOSIT', 'Minimum Deposit'),
        ('MAX_DEPOSIT', 'Maximum Deposit'),
        ('MIN_WITHDRAWAL', 'Minimum Withdrawal'),
        ('MAX_WITHDRAWAL', 'Maximum Withdrawal'),
        ('DAILY_DEPOSIT', 'Daily Deposit Limit'),
        ('DAILY_WITHDRAWAL', 'Daily Withdrawal Limit')
    ]

    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)
    limit_type = models.CharField(max_length=20, choices=LIMIT_TYPES)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['payment_method', 'limit_type']

    def __str__(self):
        return f"{self.payment_method.name} - {self.get_limit_type_display()}: KES {self.amount}"


class UserPaymentStats(models.Model):
    """Track daily payment statistics for users"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    date = models.DateField()
    total_deposits = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_withdrawals = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    deposit_count = models.IntegerField(default=0)
    withdrawal_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'date']
        indexes = [
            models.Index(fields=['user', 'date']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.date}"


class PaymentNotification(models.Model):
    """Store payment notifications and alerts"""
    NOTIFICATION_TYPES = [
        ('DEPOSIT_SUCCESS', 'Deposit Successful'),
        ('DEPOSIT_FAILED', 'Deposit Failed'),
        ('WITHDRAWAL_SUCCESS', 'Withdrawal Successful'),
        ('WITHDRAWAL_FAILED', 'Withdrawal Failed'),
        ('LOW_BALANCE', 'Low Balance Alert'),
        ('LIMIT_EXCEEDED', 'Limit Exceeded'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, null=True, blank=True)
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=100)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_notification_type_display()}"

    def __str__(self):
        return f"{self.payment_method.name} - {self.limit_type}"
