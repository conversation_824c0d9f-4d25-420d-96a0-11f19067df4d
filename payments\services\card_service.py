"""
Card Payment Service
Handles credit/debit card payments using Stripe or similar payment processor
"""

import requests
import json
import hashlib
import hmac
from datetime import datetime
from django.conf import settings
from django.core.cache import cache
from decimal import Decimal
import logging
import uuid

logger = logging.getLogger(__name__)


class CardPaymentService:
    """Service for handling card payments"""
    
    def __init__(self):
        self.environment = getattr(settings, 'CARD_PAYMENT_ENVIRONMENT', 'sandbox')
        self.api_key = getattr(settings, 'CARD_PAYMENT_API_KEY', '')
        self.secret_key = getattr(settings, 'CARD_PAYMENT_SECRET_KEY', '')
        self.merchant_id = getattr(settings, 'CARD_PAYMENT_MERCHANT_ID', '')
        
        if self.environment == 'production':
            self.base_url = 'https://api.stripe.com/v1'  # Example: Stripe production
        else:
            self.base_url = 'https://api.stripe.com/v1'  # Stripe uses same URL for test/live
    
    def create_payment_intent(self, amount, currency='KES', customer_email=None, description=None):
        """Create a payment intent for card payment"""
        try:
            url = f"{self.base_url}/payment_intents"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # Convert amount to cents (Stripe expects smallest currency unit)
            amount_cents = int(Decimal(str(amount)) * 100)
            
            payload = {
                'amount': amount_cents,
                'currency': currency.lower(),
                'automatic_payment_methods[enabled]': 'true',
                'description': description or 'ZBet Payment',
                'metadata[platform]': 'ZBet',
                'metadata[timestamp]': str(int(datetime.now().timestamp()))
            }
            
            if customer_email:
                payload['receipt_email'] = customer_email
            
            response = requests.post(url, headers=headers, data=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'payment_intent_id': result.get('id'),
                    'client_secret': result.get('client_secret'),
                    'amount': amount,
                    'currency': currency,
                    'status': result.get('status')
                }
            else:
                logger.error(f"Card payment intent creation failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Payment intent creation failed'
                }
                
        except Exception as e:
            logger.error(f"Error creating card payment intent: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def confirm_payment_intent(self, payment_intent_id, payment_method_id):
        """Confirm a payment intent with payment method"""
        try:
            url = f"{self.base_url}/payment_intents/{payment_intent_id}/confirm"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            payload = {
                'payment_method': payment_method_id,
                'return_url': f"{getattr(settings, 'SITE_URL', 'http://127.0.0.1:8000')}/payments/card/return/"
            }
            
            response = requests.post(url, headers=headers, data=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'payment_intent_id': result.get('id'),
                    'status': result.get('status'),
                    'client_secret': result.get('client_secret')
                }
            else:
                logger.error(f"Card payment confirmation failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Payment confirmation failed'
                }
                
        except Exception as e:
            logger.error(f"Error confirming card payment: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def retrieve_payment_intent(self, payment_intent_id):
        """Retrieve payment intent details"""
        try:
            url = f"{self.base_url}/payment_intents/{payment_intent_id}"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'data': result
                }
            else:
                logger.error(f"Card payment retrieval failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Payment retrieval failed'
                }
                
        except Exception as e:
            logger.error(f"Error retrieving card payment: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_refund(self, payment_intent_id, amount=None, reason=None):
        """Create a refund for a payment"""
        try:
            url = f"{self.base_url}/refunds"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            payload = {
                'payment_intent': payment_intent_id,
                'reason': reason or 'requested_by_customer'
            }
            
            if amount:
                # Convert amount to cents
                amount_cents = int(Decimal(str(amount)) * 100)
                payload['amount'] = amount_cents
            
            response = requests.post(url, headers=headers, data=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'refund_id': result.get('id'),
                    'amount': result.get('amount', 0) / 100,  # Convert back from cents
                    'status': result.get('status'),
                    'reason': result.get('reason')
                }
            else:
                logger.error(f"Card refund creation failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Refund creation failed'
                }
                
        except Exception as e:
            logger.error(f"Error creating card refund: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_webhook_signature(self, payload, signature, endpoint_secret):
        """Validate webhook signature for security"""
        try:
            # Stripe webhook signature validation
            elements = signature.split(',')
            
            timestamp = None
            signatures = []
            
            for element in elements:
                key, value = element.split('=')
                if key == 't':
                    timestamp = value
                elif key == 'v1':
                    signatures.append(value)
            
            if not timestamp or not signatures:
                return False
            
            # Create expected signature
            signed_payload = f"{timestamp}.{payload}"
            expected_signature = hmac.new(
                endpoint_secret.encode('utf-8'),
                signed_payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            return any(hmac.compare_digest(expected_signature, sig) for sig in signatures)
            
        except Exception as e:
            logger.error(f"Error validating webhook signature: {str(e)}")
            return False
    
    def process_webhook(self, payload, signature):
        """Process card payment webhook"""
        try:
            endpoint_secret = getattr(settings, 'CARD_PAYMENT_WEBHOOK_SECRET', '')
            
            if not self.validate_webhook_signature(payload, signature, endpoint_secret):
                return {
                    'success': False,
                    'error': 'Invalid webhook signature'
                }
            
            # Parse webhook data
            webhook_data = json.loads(payload)
            event_type = webhook_data.get('type')
            data_object = webhook_data.get('data', {}).get('object', {})
            
            return {
                'success': True,
                'event_type': event_type,
                'payment_intent_id': data_object.get('id'),
                'status': data_object.get('status'),
                'amount': data_object.get('amount', 0) / 100,  # Convert from cents
                'currency': data_object.get('currency', '').upper(),
                'webhook_data': webhook_data
            }
            
        except Exception as e:
            logger.error(f"Error processing card webhook: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_card_details(self, card_number, expiry_month, expiry_year, cvc):
        """Basic card validation (client-side validation should be primary)"""
        try:
            # Remove spaces and non-digits from card number
            card_number = ''.join(filter(str.isdigit, str(card_number)))
            
            # Basic length check
            if len(card_number) < 13 or len(card_number) > 19:
                return {'valid': False, 'error': 'Invalid card number length'}
            
            # Luhn algorithm check
            def luhn_check(card_num):
                def digits_of(n):
                    return [int(d) for d in str(n)]
                
                digits = digits_of(card_num)
                odd_digits = digits[-1::-2]
                even_digits = digits[-2::-2]
                checksum = sum(odd_digits)
                for d in even_digits:
                    checksum += sum(digits_of(d*2))
                return checksum % 10 == 0
            
            if not luhn_check(card_number):
                return {'valid': False, 'error': 'Invalid card number'}
            
            # Expiry validation
            current_year = datetime.now().year
            current_month = datetime.now().month
            
            try:
                exp_month = int(expiry_month)
                exp_year = int(expiry_year)
                
                if exp_month < 1 or exp_month > 12:
                    return {'valid': False, 'error': 'Invalid expiry month'}
                
                if exp_year < current_year or (exp_year == current_year and exp_month < current_month):
                    return {'valid': False, 'error': 'Card has expired'}
                    
            except ValueError:
                return {'valid': False, 'error': 'Invalid expiry date format'}
            
            # CVC validation
            if not cvc or len(str(cvc)) < 3 or len(str(cvc)) > 4:
                return {'valid': False, 'error': 'Invalid CVC'}
            
            return {'valid': True}
            
        except Exception as e:
            logger.error(f"Error validating card details: {str(e)}")
            return {'valid': False, 'error': 'Card validation failed'}


# Create a singleton instance
card_service = CardPaymentService()
