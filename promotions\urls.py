from django.urls import path
from . import views

app_name = 'promotions'

urlpatterns = [
    # Main promotion pages
    path('', views.promotions_home, name='home'),
    path('promotion/<slug:slug>/', views.promotion_detail, name='detail'),
    path('claim/<slug:slug>/', views.claim_promotion, name='claim'),
    path('use-promo-code/', views.use_promo_code, name='use_promo_code'),
    
    # User bonus management
    path('my-bonuses/', views.my_bonuses, name='my_bonuses'),
    path('forfeit-bonus/<uuid:bonus_id>/', views.forfeit_bonus, name='forfeit_bonus'),
    
    # Loyalty and referral programs
    path('loyalty/', views.loyalty_program, name='loyalty'),
    path('referral/', views.referral_program, name='referral'),
    path('cashback/', views.cashback_calculator, name='cashback'),
    
    # API endpoints
    path('api/promotions/', views.api_active_promotions, name='api_promotions'),
    path('api/banners/', views.api_promotional_banners, name='api_banners'),
    path('api/user-bonuses/', views.api_user_bonuses, name='api_user_bonuses'),
    path('api/add-wagering/', views.api_add_wagering, name='api_add_wagering'),
]
