# Generated by Django 5.2.4 on 2025-07-05 20:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("sports", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="match",
            name="statistics",
        ),
        migrations.AddField(
            model_name="match",
            name="stats_data",
            field=models.JSONField(
                blank=True, default=dict, help_text="Basic match statistics data"
            ),
        ),
        migrations.CreateModel(
            name="MatchStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_goals", models.PositiveIntegerField(default=0)),
                ("total_corners", models.PositiveIntegerField(default=0)),
                ("total_cards", models.PositiveIntegerField(default=0)),
                ("total_fouls", models.PositiveIntegerField(default=0)),
                ("total_offsides", models.PositiveIntegerField(default=0)),
                (
                    "home_possession",
                    models.PositiveIntegerField(
                        default=0, help_text="Possession percentage"
                    ),
                ),
                ("home_shots", models.PositiveIntegerField(default=0)),
                ("home_shots_on_target", models.PositiveIntegerField(default=0)),
                ("home_corners", models.PositiveIntegerField(default=0)),
                ("home_fouls", models.PositiveIntegerField(default=0)),
                ("home_yellow_cards", models.PositiveIntegerField(default=0)),
                ("home_red_cards", models.PositiveIntegerField(default=0)),
                ("home_offsides", models.PositiveIntegerField(default=0)),
                ("home_passes", models.PositiveIntegerField(default=0)),
                (
                    "home_pass_accuracy",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "away_possession",
                    models.PositiveIntegerField(
                        default=0, help_text="Possession percentage"
                    ),
                ),
                ("away_shots", models.PositiveIntegerField(default=0)),
                ("away_shots_on_target", models.PositiveIntegerField(default=0)),
                ("away_corners", models.PositiveIntegerField(default=0)),
                ("away_fouls", models.PositiveIntegerField(default=0)),
                ("away_yellow_cards", models.PositiveIntegerField(default=0)),
                ("away_red_cards", models.PositiveIntegerField(default=0)),
                ("away_offsides", models.PositiveIntegerField(default=0)),
                ("away_passes", models.PositiveIntegerField(default=0)),
                (
                    "away_pass_accuracy",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                (
                    "advanced_stats",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional sport-specific statistics",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "match",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="statistics",
                        to="sports.match",
                    ),
                ),
            ],
            options={
                "verbose_name": "Match Statistics",
                "verbose_name_plural": "Match Statistics",
                "db_table": "sports_match_statistics",
            },
        ),
        migrations.CreateModel(
            name="PlayerStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("player_name", models.CharField(max_length=200)),
                ("jersey_number", models.PositiveIntegerField(blank=True, null=True)),
                ("position", models.CharField(blank=True, max_length=50)),
                ("minutes_played", models.PositiveIntegerField(default=0)),
                ("is_starter", models.BooleanField(default=False)),
                ("substituted_in", models.PositiveIntegerField(blank=True, null=True)),
                ("substituted_out", models.PositiveIntegerField(blank=True, null=True)),
                ("goals", models.PositiveIntegerField(default=0)),
                ("assists", models.PositiveIntegerField(default=0)),
                ("shots", models.PositiveIntegerField(default=0)),
                ("shots_on_target", models.PositiveIntegerField(default=0)),
                ("passes", models.PositiveIntegerField(default=0)),
                (
                    "pass_accuracy",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("tackles", models.PositiveIntegerField(default=0)),
                ("interceptions", models.PositiveIntegerField(default=0)),
                ("fouls_committed", models.PositiveIntegerField(default=0)),
                ("fouls_suffered", models.PositiveIntegerField(default=0)),
                ("yellow_cards", models.PositiveIntegerField(default=0)),
                ("red_cards", models.PositiveIntegerField(default=0)),
                ("advanced_stats", models.JSONField(blank=True, default=dict)),
                (
                    "rating",
                    models.DecimalField(
                        blank=True, decimal_places=1, max_digits=3, null=True
                    ),
                ),
                (
                    "match",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="player_stats",
                        to="sports.match",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="player_stats",
                        to="sports.team",
                    ),
                ),
            ],
            options={
                "verbose_name": "Player Statistics",
                "verbose_name_plural": "Player Statistics",
                "db_table": "sports_player_statistics",
                "ordering": ["team", "jersey_number", "player_name"],
                "unique_together": {("match", "team", "player_name")},
            },
        ),
        migrations.CreateModel(
            name="TeamSeasonStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("season", models.CharField(max_length=20)),
                ("matches_played", models.PositiveIntegerField(default=0)),
                ("wins", models.PositiveIntegerField(default=0)),
                ("draws", models.PositiveIntegerField(default=0)),
                ("losses", models.PositiveIntegerField(default=0)),
                ("goals_for", models.PositiveIntegerField(default=0)),
                ("goals_against", models.PositiveIntegerField(default=0)),
                ("points", models.PositiveIntegerField(default=0)),
                ("position", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "form",
                    models.CharField(
                        blank=True, help_text="W/D/L for last 5 matches", max_length=5
                    ),
                ),
                ("clean_sheets", models.PositiveIntegerField(default=0)),
                ("failed_to_score", models.PositiveIntegerField(default=0)),
                (
                    "average_possession",
                    models.DecimalField(decimal_places=2, default=0, max_digits=5),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "league",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="team_stats",
                        to="sports.league",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="season_stats",
                        to="sports.team",
                    ),
                ),
            ],
            options={
                "verbose_name": "Team Season Statistics",
                "verbose_name_plural": "Team Season Statistics",
                "db_table": "sports_team_season_statistics",
                "ordering": ["league", "-points", "-goals_for"],
                "unique_together": {("team", "league", "season")},
            },
        ),
    ]
