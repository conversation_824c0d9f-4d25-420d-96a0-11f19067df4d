from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils import timezone
from .models import (
    User, UserProfile, KYCDocument, UserSession, SecurityLog,
    TwoFactorAuth, BetHistory, Transaction, ResponsibleGambling,
    UserDevice, LoginAttempt, SuspiciousActivity
)


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom User admin interface
    """
    list_display = [
        'email', 'username', 'full_name', 'phone_number',
        'country', 'is_verified', 'is_kyc_verified',
        'is_active', 'date_joined'
    ]
    list_filter = [
        'is_verified', 'is_kyc_verified', 'is_suspended',
        'country', 'preferred_currency', 'date_joined'
    ]
    search_fields = ['email', 'username', 'first_name', 'last_name', 'phone_number']
    ordering = ['-date_joined']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Personal Information', {
            'fields': (
                'phone_number', 'date_of_birth', 'country', 'city',
                'preferred_language', 'preferred_currency'
            )
        }),
        ('Verification Status', {
            'fields': (
                'is_verified', 'is_kyc_verified', 'is_suspended',
                'email_verified_at', 'phone_verified_at', 'kyc_verified_at'
            )
        }),
        ('Responsible Gambling', {
            'fields': (
                'daily_deposit_limit', 'daily_bet_limit', 'self_exclusion_until'
            )
        }),
        ('Marketing Preferences', {
            'fields': ('marketing_emails', 'marketing_sms')
        }),
        ('Referral System', {
            'fields': ('referral_code', 'referred_by')
        }),
    )

    readonly_fields = [
        'email_verified_at', 'phone_verified_at', 'kyc_verified_at',
        'referral_code', 'created_at', 'updated_at'
    ]

    actions = [
        'suspend_users', 'unsuspend_users', 'close_accounts',
        'verify_emails', 'verify_phones'
    ]

    def full_name(self, obj):
        return obj.full_name or '-'
    full_name.short_description = 'Full Name'

    def suspend_users(self, request, queryset):
        """Suspend selected users"""
        count = 0
        for user in queryset:
            if not user.is_suspended:
                user.is_suspended = True
                user.save()
                count += 1

                # Log security event
                from .views import log_security_event
                log_security_event(user, 'account_suspended', request, {
                    'suspended_by': request.user.email,
                    'reason': 'Admin action'
                })

        self.message_user(request, f'{count} users suspended successfully.')
    suspend_users.short_description = 'Suspend selected users'

    def unsuspend_users(self, request, queryset):
        """Unsuspend selected users"""
        count = 0
        for user in queryset:
            if user.is_suspended:
                user.is_suspended = False
                user.save()
                count += 1

                # Log security event
                from .views import log_security_event
                log_security_event(user, 'account_unsuspended', request, {
                    'unsuspended_by': request.user.email
                })

        self.message_user(request, f'{count} users unsuspended successfully.')
    unsuspend_users.short_description = 'Unsuspend selected users'

    def close_accounts(self, request, queryset):
        """Close selected user accounts"""
        count = 0
        for user in queryset:
            if user.is_active:
                user.is_active = False
                user.save()
                count += 1

                # Log security event
                from .views import log_security_event
                log_security_event(user, 'account_closed', request, {
                    'closed_by': request.user.email,
                    'reason': 'Admin action'
                })

        self.message_user(request, f'{count} accounts closed successfully.')
    close_accounts.short_description = 'Close selected accounts'

    def verify_emails(self, request, queryset):
        """Verify emails for selected users"""
        count = 0
        for user in queryset:
            if not user.is_verified:
                user.is_verified = True
                user.email_verified_at = timezone.now()
                user.save()
                count += 1

        self.message_user(request, f'{count} emails verified successfully.')
    verify_emails.short_description = 'Verify emails for selected users'

    def verify_phones(self, request, queryset):
        """Verify phones for selected users"""
        count = 0
        for user in queryset:
            if not user.phone_verified_at:
                user.phone_verified_at = timezone.now()
                user.save()
                count += 1

        self.message_user(request, f'{count} phones verified successfully.')
    verify_phones.short_description = 'Verify phones for selected users'


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    User Profile admin interface
    """
    list_display = [
        'user', 'gender', 'occupation', 'betting_experience',
        'total_bets_placed', 'win_rate_display', 'created_at'
    ]
    list_filter = ['gender', 'betting_experience', 'created_at']
    search_fields = ['user__email', 'user__username', 'occupation']
    readonly_fields = [
        'total_deposits', 'total_withdrawals',
        'total_bets_placed', 'total_bets_won', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('User Information', {
            'fields': ('user', 'avatar')
        }),
        ('Personal Details', {
            'fields': (
                'gender', 'occupation', 'address_line_1', 'address_line_2',
                'postal_code'
            )
        }),
        ('Emergency Contact', {
            'fields': (
                'emergency_contact_name', 'emergency_contact_phone',
                'emergency_contact_relationship'
            )
        }),
        ('Betting Preferences', {
            'fields': ('favorite_sports', 'betting_experience')
        }),
        ('Statistics', {
            'fields': (
                'total_deposits', 'total_withdrawals',
                'total_bets_placed', 'total_bets_won'
            )
        }),
    )

    def win_rate_display(self, obj):
        return f"{obj.win_rate:.1f}%"
    win_rate_display.short_description = 'Win Rate'


@admin.register(KYCDocument)
class KYCDocumentAdmin(admin.ModelAdmin):
    """
    KYC Document admin interface
    """
    list_display = [
        'user', 'document_type', 'status', 'uploaded_at',
        'verified_by', 'verified_at'
    ]
    list_filter = ['document_type', 'status', 'uploaded_at']
    search_fields = ['user__email', 'document_number']
    readonly_fields = ['uploaded_at', 'verified_at']

    fieldsets = (
        ('Document Information', {
            'fields': ('user', 'document_type', 'document_number', 'document_file')
        }),
        ('Verification', {
            'fields': ('status', 'verified_by', 'verification_notes', 'expires_at')
        }),
        ('Timestamps', {
            'fields': ('uploaded_at', 'verified_at')
        }),
    )

    def save_model(self, request, obj, form, change):
        if change and 'status' in form.changed_data:
            if obj.status == 'approved':
                obj.verified_by = request.user
                obj.verified_at = timezone.now()
        super().save_model(request, obj, form, change)


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """
    User Session admin interface
    """
    list_display = [
        'user', 'ip_address', 'country', 'city',
        'is_active', 'created_at', 'last_activity'
    ]
    list_filter = ['is_active', 'country', 'created_at']
    search_fields = ['user__email', 'ip_address']
    readonly_fields = ['created_at', 'last_activity', 'ended_at']

    fieldsets = (
        ('Session Information', {
            'fields': ('user', 'session_key', 'is_active')
        }),
        ('Device Information', {
            'fields': ('ip_address', 'user_agent', 'device_info')
        }),
        ('Location', {
            'fields': ('country', 'city')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'last_activity', 'ended_at')
        }),
    )


@admin.register(SecurityLog)
class SecurityLogAdmin(admin.ModelAdmin):
    """
    Security Log admin interface
    """
    list_display = [
        'user', 'event_type', 'ip_address', 'created_at'
    ]
    list_filter = ['event_type', 'created_at']
    search_fields = ['user__email', 'ip_address']
    readonly_fields = ['created_at']

    fieldsets = (
        ('Event Information', {
            'fields': ('user', 'event_type', 'details')
        }),
        ('Request Information', {
            'fields': ('ip_address', 'user_agent')
        }),
        ('Timestamp', {
            'fields': ('created_at',)
        }),
    )


@admin.register(TwoFactorAuth)
class TwoFactorAuthAdmin(admin.ModelAdmin):
    """
    Two-Factor Authentication admin interface
    """
    list_display = [
        'user', 'is_enabled', 'method', 'totp_verified',
        'sms_enabled', 'backup_codes_remaining', 'enabled_at'
    ]
    list_filter = ['is_enabled', 'method', 'totp_verified', 'sms_enabled']
    search_fields = ['user__email', 'user__username']
    readonly_fields = [
        'totp_secret', 'backup_codes', 'backup_codes_used',
        'enabled_at', 'last_used_at', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('2FA Settings', {
            'fields': ('is_enabled', 'method', 'enabled_at', 'last_used_at')
        }),
        ('TOTP Settings', {
            'fields': ('totp_secret', 'totp_verified')
        }),
        ('SMS Settings', {
            'fields': ('sms_enabled',)
        }),
        ('Backup Codes', {
            'fields': ('backup_codes', 'backup_codes_used')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def backup_codes_remaining(self, obj):
        return obj.backup_codes_remaining
    backup_codes_remaining.short_description = 'Backup Codes Left'


@admin.register(BetHistory)
class BetHistoryAdmin(admin.ModelAdmin):
    """
    Bet History admin interface
    """
    list_display = [
        'bet_id', 'user', 'bet_type', 'status', 'stake_amount',
        'potential_win', 'actual_win', 'placed_at'
    ]
    list_filter = ['status', 'bet_type', 'placed_at']
    search_fields = ['bet_id', 'user__email', 'user__username']
    readonly_fields = ['bet_id', 'placed_at', 'settled_at']

    fieldsets = (
        ('Bet Information', {
            'fields': ('bet_id', 'user', 'bet_type', 'status')
        }),
        ('Financial Details', {
            'fields': ('stake_amount', 'potential_win', 'actual_win', 'total_odds')
        }),
        ('Bet Details', {
            'fields': ('selections', 'match_details')
        }),
        ('Timestamps', {
            'fields': ('placed_at', 'settled_at')
        }),
    )


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    """
    Transaction admin interface
    """
    list_display = [
        'transaction_id', 'user', 'transaction_type', 'status',
        'amount', 'payment_method', 'created_at'
    ]
    list_filter = ['transaction_type', 'status', 'payment_method', 'created_at']
    search_fields = ['transaction_id', 'user__email', 'reference', 'external_reference']
    readonly_fields = ['transaction_id', 'created_at', 'completed_at']

    fieldsets = (
        ('Transaction Information', {
            'fields': ('transaction_id', 'user', 'transaction_type', 'status', 'payment_method')
        }),
        ('Financial Details', {
            'fields': ('amount', 'fee', 'balance_before', 'balance_after')
        }),
        ('Additional Details', {
            'fields': ('description', 'reference', 'external_reference', 'metadata')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'completed_at')
        }),
    )


@admin.register(ResponsibleGambling)
class ResponsibleGamblingAdmin(admin.ModelAdmin):
    """
    Responsible Gambling admin interface
    """
    list_display = [
        'user', 'is_self_excluded', 'self_exclusion_period',
        'daily_deposit_limit', 'daily_bet_limit', 'reality_check_enabled'
    ]
    list_filter = ['is_self_excluded', 'self_exclusion_period', 'reality_check_enabled']
    search_fields = ['user__email', 'user__username']
    readonly_fields = ['created_at', 'updated_at', 'self_exclusion_start', 'self_exclusion_end']

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Deposit Limits', {
            'fields': ('daily_deposit_limit', 'weekly_deposit_limit', 'monthly_deposit_limit')
        }),
        ('Betting Limits', {
            'fields': ('daily_bet_limit', 'weekly_bet_limit', 'monthly_bet_limit')
        }),
        ('Loss Limits', {
            'fields': ('daily_loss_limit', 'weekly_loss_limit', 'monthly_loss_limit')
        }),
        ('Session Limits', {
            'fields': ('session_time_limit', 'daily_session_limit')
        }),
        ('Self-Exclusion', {
            'fields': (
                'is_self_excluded', 'self_exclusion_period',
                'self_exclusion_start', 'self_exclusion_end', 'self_exclusion_reason'
            )
        }),
        ('Reality Checks', {
            'fields': ('reality_check_enabled', 'reality_check_interval')
        }),
        ('Account Restrictions', {
            'fields': ('account_restricted', 'restriction_reason', 'restriction_end')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        """Make certain fields readonly for non-superusers"""
        readonly = list(self.readonly_fields)
        if not request.user.is_superuser:
            readonly.extend(['is_self_excluded', 'self_exclusion_period'])
        return readonly


@admin.register(UserDevice)
class UserDeviceAdmin(admin.ModelAdmin):
    """
    User Device admin interface
    """
    list_display = [
        'user', 'device_name', 'device_type', 'ip_address',
        'is_trusted', 'is_blocked', 'last_login'
    ]
    list_filter = ['device_type', 'is_trusted', 'is_blocked', 'last_login']
    search_fields = ['user__email', 'device_name', 'ip_address']
    readonly_fields = ['device_id', 'first_seen', 'last_seen']

    fieldsets = (
        ('User & Device', {
            'fields': ('user', 'device_id', 'device_name', 'device_type')
        }),
        ('Browser Information', {
            'fields': ('user_agent', 'browser_name', 'browser_version', 'os_name', 'os_version')
        }),
        ('Location', {
            'fields': ('ip_address', 'country', 'city')
        }),
        ('Security', {
            'fields': ('is_trusted', 'is_blocked')
        }),
        ('Timestamps', {
            'fields': ('first_seen', 'last_seen', 'last_login')
        }),
    )


@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    """
    Login Attempt admin interface
    """
    list_display = [
        'username_attempted', 'user', 'ip_address', 'status',
        'attempted_at'
    ]
    list_filter = ['status', 'attempted_at']
    search_fields = ['username_attempted', 'user__email', 'ip_address']
    readonly_fields = ['attempted_at']

    fieldsets = (
        ('Attempt Details', {
            'fields': ('user', 'username_attempted', 'ip_address', 'status')
        }),
        ('Technical Details', {
            'fields': ('user_agent', 'failure_reason')
        }),
        ('Timestamp', {
            'fields': ('attempted_at',)
        }),
    )


@admin.register(SuspiciousActivity)
class SuspiciousActivityAdmin(admin.ModelAdmin):
    """
    Suspicious Activity admin interface
    """
    list_display = [
        'user', 'activity_type', 'risk_level', 'status',
        'detected_at', 'investigated_by'
    ]
    list_filter = ['activity_type', 'risk_level', 'status', 'detected_at']
    search_fields = ['user__email', 'description']
    readonly_fields = ['detected_at']

    fieldsets = (
        ('Activity Details', {
            'fields': ('user', 'activity_type', 'risk_level', 'status', 'description')
        }),
        ('Technical Details', {
            'fields': ('ip_address', 'user_agent', 'metadata')
        }),
        ('Investigation', {
            'fields': ('investigated_by', 'investigation_notes', 'investigated_at', 'resolved_at')
        }),
        ('Timestamp', {
            'fields': ('detected_at',)
        }),
    )

    actions = ['mark_as_resolved', 'mark_as_false_positive']

    def mark_as_resolved(self, request, queryset):
        """Mark activities as resolved"""
        count = queryset.update(
            status='resolved',
            investigated_by=request.user,
            resolved_at=timezone.now()
        )
        self.message_user(request, f'{count} activities marked as resolved.')
    mark_as_resolved.short_description = 'Mark as resolved'

    def mark_as_false_positive(self, request, queryset):
        """Mark activities as false positive"""
        count = queryset.update(
            status='false_positive',
            investigated_by=request.user,
            resolved_at=timezone.now()
        )
        self.message_user(request, f'{count} activities marked as false positive.')
    mark_as_false_positive.short_description = 'Mark as false positive'
