from django.contrib import admin
from django.utils import timezone
from .models import (
    BetType, Market, Selection, OddsHistory,
    Bet, BetSelection, BetSlip, BetSlipSelection,
    Settlement, SettlementHistory, BetSettlementRule,
    BetCancellation, BetCancellationRule
)


@admin.register(BetType)
class BetTypeAdmin(admin.ModelAdmin):
    """
    Bet Type admin interface
    """
    list_display = [
        'name', 'is_active', 'is_live_betting', 'display_order', 'margin_percentage'
    ]
    list_filter = ['is_active', 'is_live_betting', 'sports']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    filter_horizontal = ['sports']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description')
        }),
        ('Configuration', {
            'fields': ('sports', 'is_active', 'is_live_betting', 'display_order')
        }),
        ('Odds Settings', {
            'fields': ('margin_percentage',)
        }),
    )


class SelectionInline(admin.TabularInline):
    """
    Inline admin for market selections
    """
    model = Selection
    extra = 0
    fields = ['name', 'decimal_odds', 'status', 'min_bet', 'max_bet']


@admin.register(Market)
class MarketAdmin(admin.ModelAdmin):
    """
    Market admin interface
    """
    list_display = [
        'name', 'match', 'bet_type', 'status', 'selections_count'
    ]
    list_filter = [
        'status', 'bet_type', 'match__league__sport', 'match__status'
    ]
    search_fields = [
        'name', 'match__home_team__name', 'match__away_team__name'
    ]
    inlines = [SelectionInline]

    fieldsets = (
        ('Market Details', {
            'fields': ('match', 'bet_type', 'name', 'description')
        }),
        ('Status & Parameters', {
            'fields': ('status', 'parameters')
        }),
        ('Settlement', {
            'fields': ('winning_selection', 'settled_at')
        }),
    )

    def selections_count(self, obj):
        return obj.selections.count()
    selections_count.short_description = 'Selections'


@admin.register(Selection)
class SelectionAdmin(admin.ModelAdmin):
    """
    Selection admin interface
    """
    list_display = [
        'name', 'market', 'decimal_odds', 'status', 'total_bets', 'total_stake'
    ]
    list_filter = ['status', 'market__bet_type', 'market__match__league__sport']
    search_fields = ['name', 'market__name', 'market__match__home_team__name']

    fieldsets = (
        ('Selection Details', {
            'fields': ('market', 'name', 'short_name', 'status')
        }),
        ('Odds', {
            'fields': ('decimal_odds', 'fractional_odds', 'american_odds')
        }),
        ('Betting Limits', {
            'fields': ('min_bet', 'max_bet')
        }),
        ('Statistics', {
            'fields': ('total_bets', 'total_stake')
        }),
    )

    readonly_fields = ['fractional_odds', 'american_odds', 'total_bets', 'total_stake']


@admin.register(OddsHistory)
class OddsHistoryAdmin(admin.ModelAdmin):
    """
    Odds History admin interface
    """
    list_display = [
        'selection', 'old_decimal_odds', 'new_decimal_odds',
        'change_percentage', 'changed_by', 'changed_at'
    ]
    list_filter = ['changed_at', 'selection__market__bet_type']
    search_fields = [
        'selection__name', 'selection__market__name', 'change_reason'
    ]
    readonly_fields = ['changed_at']

    fieldsets = (
        ('Odds Change', {
            'fields': ('selection', 'old_decimal_odds', 'new_decimal_odds')
        }),
        ('Change Details', {
            'fields': ('change_reason', 'changed_by', 'changed_at')
        }),
    )


class BetSelectionInline(admin.TabularInline):
    """
    Inline admin for bet selections
    """
    model = BetSelection
    extra = 0
    fields = ['selection', 'odds_taken', 'status']
    readonly_fields = ['odds_taken']


@admin.register(Bet)
class BetAdmin(admin.ModelAdmin):
    """
    Bet admin interface
    """
    list_display = [
        'bet_id', 'user', 'bet_type', 'stake', 'total_odds',
        'potential_win', 'status', 'placed_at'
    ]
    list_filter = ['bet_type', 'status', 'placed_at']
    search_fields = ['bet_id', 'user__email', 'user__username']
    readonly_fields = ['bet_id', 'potential_win', 'placed_at']
    inlines = [BetSelectionInline]

    fieldsets = (
        ('Bet Information', {
            'fields': ('bet_id', 'user', 'bet_type', 'status')
        }),
        ('Financial Details', {
            'fields': ('stake', 'total_odds', 'potential_win', 'actual_win')
        }),
        ('Cashout', {
            'fields': ('cashout_value', 'cashout_at')
        }),
        ('Timestamps', {
            'fields': ('placed_at', 'settled_at')
        }),
    )

    actions = ['settle_as_won', 'settle_as_lost', 'void_bets']

    def settle_as_won(self, request, queryset):
        """Settle selected bets as won"""
        count = 0
        for bet in queryset.filter(status='pending'):
            bet.status = 'won'
            bet.actual_win = bet.potential_win
            bet.settled_at = timezone.now()
            bet.save()
            count += 1

        self.message_user(request, f'{count} bets settled as won.')
    settle_as_won.short_description = 'Settle selected bets as won'

    def settle_as_lost(self, request, queryset):
        """Settle selected bets as lost"""
        count = 0
        for bet in queryset.filter(status='pending'):
            bet.status = 'lost'
            bet.settled_at = timezone.now()
            bet.save()
            count += 1

        self.message_user(request, f'{count} bets settled as lost.')
    settle_as_lost.short_description = 'Settle selected bets as lost'

    def void_bets(self, request, queryset):
        """Void selected bets"""
        count = 0
        for bet in queryset.filter(status='pending'):
            bet.status = 'void'
            bet.settled_at = timezone.now()
            bet.save()
            count += 1

        self.message_user(request, f'{count} bets voided.')
    void_bets.short_description = 'Void selected bets'


@admin.register(BetSlip)
class BetSlipAdmin(admin.ModelAdmin):
    """
    Bet Slip admin interface
    """
    list_display = [
        'user', 'bet_type', 'selections_count', 'stake',
        'total_odds', 'potential_win', 'updated_at'
    ]
    list_filter = ['bet_type', 'updated_at']
    search_fields = ['user__email', 'user__username']

    def selections_count(self, obj):
        return obj.selections.count()
    selections_count.short_description = 'Selections'


class SettlementHistoryInline(admin.TabularInline):
    """
    Inline admin for settlement history
    """
    model = SettlementHistory
    extra = 0
    fields = ['action', 'old_status', 'new_status', 'old_amount', 'new_amount', 'reason', 'changed_by']
    readonly_fields = ['changed_at']


@admin.register(Settlement)
class SettlementAdmin(admin.ModelAdmin):
    """
    Settlement admin interface
    """
    list_display = [
        'settlement_id', 'bet', 'market', 'settlement_type', 'status',
        'settlement_amount', 'net_amount', 'settled_at'
    ]
    list_filter = ['settlement_type', 'status', 'settled_at']
    search_fields = ['settlement_id', 'bet__bet_id', 'market__name']
    readonly_fields = ['settlement_id', 'created_at']
    inlines = [SettlementHistoryInline]

    fieldsets = (
        ('Settlement Information', {
            'fields': ('settlement_id', 'bet', 'market', 'settlement_type', 'status')
        }),
        ('Settlement Results', {
            'fields': ('winning_selection', 'settlement_amount', 'tax_amount', 'net_amount')
        }),
        ('Settlement Details', {
            'fields': ('settlement_reason', 'settlement_notes', 'settlement_data')
        }),
        ('Staff Information', {
            'fields': ('settled_by', 'created_at', 'settled_at')
        }),
    )

    actions = ['settle_pending', 'void_settlements']

    def settle_pending(self, request, queryset):
        """Settle pending settlements"""
        count = 0
        for settlement in queryset.filter(status='pending'):
            if settlement.settle(settled_by=request.user):
                count += 1

        self.message_user(request, f'{count} settlements processed.')
    settle_pending.short_description = 'Settle pending settlements'

    def void_settlements(self, request, queryset):
        """Void selected settlements"""
        count = 0
        for settlement in queryset:
            settlement.void_settlement(reason='Admin void', voided_by=request.user)
            count += 1

        self.message_user(request, f'{count} settlements voided.')
    void_settlements.short_description = 'Void selected settlements'


@admin.register(SettlementHistory)
class SettlementHistoryAdmin(admin.ModelAdmin):
    """
    Settlement History admin interface
    """
    list_display = [
        'settlement', 'action', 'old_status', 'new_status',
        'old_amount', 'new_amount', 'changed_by', 'changed_at'
    ]
    list_filter = ['action', 'changed_at']
    search_fields = ['settlement__settlement_id', 'reason']
    readonly_fields = ['changed_at']

    fieldsets = (
        ('Settlement Change', {
            'fields': ('settlement', 'action', 'old_status', 'new_status')
        }),
        ('Amount Changes', {
            'fields': ('old_amount', 'new_amount')
        }),
        ('Change Details', {
            'fields': ('reason', 'notes', 'changed_by', 'changed_at')
        }),
    )


@admin.register(BetSettlementRule)
class BetSettlementRuleAdmin(admin.ModelAdmin):
    """
    Bet Settlement Rule admin interface
    """
    list_display = [
        'rule_name', 'bet_type', 'sport', 'rule_type', 'is_active', 'priority'
    ]
    list_filter = ['bet_type', 'sport', 'rule_type', 'is_active']
    search_fields = ['rule_name', 'rule_description']

    fieldsets = (
        ('Rule Information', {
            'fields': ('rule_name', 'rule_description', 'bet_type', 'sport')
        }),
        ('Rule Configuration', {
            'fields': ('rule_type', 'rule_config')
        }),
        ('Rule Settings', {
            'fields': ('is_active', 'priority')
        }),
    )


@admin.register(BetCancellationRule)
class BetCancellationRuleAdmin(admin.ModelAdmin):
    """
    Bet Cancellation Rule admin interface
    """
    list_display = [
        'rule_name', 'rule_type', 'is_active', 'priority',
        'cancellation_fee_percentage', 'minimum_fee'
    ]
    list_filter = ['rule_type', 'is_active']
    search_fields = ['rule_name', 'description']
    filter_horizontal = ['sports', 'bet_types']

    fieldsets = (
        ('Rule Information', {
            'fields': ('rule_name', 'rule_type', 'description')
        }),
        ('Rule Scope', {
            'fields': ('sports', 'bet_types')
        }),
        ('Rule Configuration', {
            'fields': ('rule_config',)
        }),
        ('Fees', {
            'fields': ('cancellation_fee_percentage', 'minimum_fee')
        }),
        ('Settings', {
            'fields': ('is_active', 'priority')
        }),
    )


@admin.register(BetCancellation)
class BetCancellationAdmin(admin.ModelAdmin):
    """
    Bet Cancellation admin interface
    """
    list_display = [
        'cancellation_id', 'bet', 'status', 'reason',
        'refund_amount', 'cancellation_fee', 'net_refund', 'requested_at'
    ]
    list_filter = ['status', 'reason', 'requested_at']
    search_fields = ['cancellation_id', 'bet__bet_id', 'bet__user__email']
    readonly_fields = ['cancellation_id', 'requested_at']

    fieldsets = (
        ('Cancellation Information', {
            'fields': ('cancellation_id', 'bet', 'status', 'reason')
        }),
        ('Cancellation Details', {
            'fields': ('reason_details', 'processing_notes')
        }),
        ('Financial Details', {
            'fields': ('refund_amount', 'cancellation_fee', 'net_refund')
        }),
        ('Processing Information', {
            'fields': ('processed_by', 'requested_at', 'processed_at')
        }),
    )

    actions = ['approve_cancellations', 'reject_cancellations']

    def approve_cancellations(self, request, queryset):
        """Approve selected cancellations"""
        count = 0
        for cancellation in queryset.filter(status='requested'):
            if cancellation.approve_cancellation(processed_by=request.user):
                count += 1

        self.message_user(request, f'{count} cancellations approved.')
    approve_cancellations.short_description = 'Approve selected cancellations'

    def reject_cancellations(self, request, queryset):
        """Reject selected cancellations"""
        count = 0
        for cancellation in queryset.filter(status='requested'):
            if cancellation.reject_cancellation(processed_by=request.user):
                count += 1

        self.message_user(request, f'{count} cancellations rejected.')
    reject_cancellations.short_description = 'Reject selected cancellations'
