from django.contrib import admin
from django.utils import timezone
from .models import (
    BetType, Market, Selection, OddsHistory,
    Bet, BetSelection, BetSlip, BetSlipSelection,
    Settlement, SettlementHistory, BetSettlementRule,
    BetCancellation, BetCancellationRule,
    OddsComparison, UserOddsAlert,
    UserFavorite, FavoriteNotification, FavoriteGroup, FavoriteGroupMembership,
    LiveBetNotification, UserNotificationPreference, NotificationDeliveryLog,
    LiveBetNotificationTemplate,
    VirtualSport, VirtualTeam, VirtualMatch, VirtualBetType, VirtualMarket,
    VirtualSelection, VirtualBet, VirtualBetSelection, VirtualSportsManager
)


@admin.register(BetType)
class BetTypeAdmin(admin.ModelAdmin):
    """
    Bet Type admin interface
    """
    list_display = [
        'name', 'is_active', 'is_live_betting', 'display_order', 'margin_percentage'
    ]
    list_filter = ['is_active', 'is_live_betting', 'sports']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    filter_horizontal = ['sports']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description')
        }),
        ('Configuration', {
            'fields': ('sports', 'is_active', 'is_live_betting', 'display_order')
        }),
        ('Odds Settings', {
            'fields': ('margin_percentage',)
        }),
    )


class SelectionInline(admin.TabularInline):
    """
    Inline admin for market selections
    """
    model = Selection
    extra = 0
    fields = ['name', 'decimal_odds', 'status', 'min_bet', 'max_bet']


@admin.register(Market)
class MarketAdmin(admin.ModelAdmin):
    """
    Market admin interface
    """
    list_display = [
        'name', 'match', 'bet_type', 'status', 'selections_count'
    ]
    list_filter = [
        'status', 'bet_type', 'match__league__sport', 'match__status'
    ]
    search_fields = [
        'name', 'match__home_team__name', 'match__away_team__name'
    ]
    inlines = [SelectionInline]

    fieldsets = (
        ('Market Details', {
            'fields': ('match', 'bet_type', 'name', 'description')
        }),
        ('Status & Parameters', {
            'fields': ('status', 'parameters')
        }),
        ('Settlement', {
            'fields': ('winning_selection', 'settled_at')
        }),
    )

    def selections_count(self, obj):
        return obj.selections.count()
    selections_count.short_description = 'Selections'


@admin.register(Selection)
class SelectionAdmin(admin.ModelAdmin):
    """
    Selection admin interface
    """
    list_display = [
        'name', 'market', 'decimal_odds', 'status', 'total_bets', 'total_stake'
    ]
    list_filter = ['status', 'market__bet_type', 'market__match__league__sport']
    search_fields = ['name', 'market__name', 'market__match__home_team__name']

    fieldsets = (
        ('Selection Details', {
            'fields': ('market', 'name', 'short_name', 'status')
        }),
        ('Odds', {
            'fields': ('decimal_odds', 'fractional_odds', 'american_odds')
        }),
        ('Betting Limits', {
            'fields': ('min_bet', 'max_bet')
        }),
        ('Statistics', {
            'fields': ('total_bets', 'total_stake')
        }),
    )

    readonly_fields = ['fractional_odds', 'american_odds', 'total_bets', 'total_stake']


@admin.register(OddsHistory)
class OddsHistoryAdmin(admin.ModelAdmin):
    """
    Odds History admin interface
    """
    list_display = [
        'selection', 'old_decimal_odds', 'new_decimal_odds',
        'change_percentage', 'changed_by', 'changed_at'
    ]
    list_filter = ['changed_at', 'selection__market__bet_type']
    search_fields = [
        'selection__name', 'selection__market__name', 'change_reason'
    ]
    readonly_fields = ['changed_at']

    fieldsets = (
        ('Odds Change', {
            'fields': ('selection', 'old_decimal_odds', 'new_decimal_odds')
        }),
        ('Change Details', {
            'fields': ('change_reason', 'changed_by', 'changed_at')
        }),
    )


class BetSelectionInline(admin.TabularInline):
    """
    Inline admin for bet selections
    """
    model = BetSelection
    extra = 0
    fields = ['selection', 'odds_taken', 'status']
    readonly_fields = ['odds_taken']


@admin.register(Bet)
class BetAdmin(admin.ModelAdmin):
    """
    Bet admin interface
    """
    list_display = [
        'bet_id', 'user', 'bet_type', 'stake', 'total_odds',
        'potential_win', 'status', 'placed_at'
    ]
    list_filter = ['bet_type', 'status', 'placed_at']
    search_fields = ['bet_id', 'user__email', 'user__username']
    readonly_fields = ['bet_id', 'potential_win', 'placed_at']
    inlines = [BetSelectionInline]

    fieldsets = (
        ('Bet Information', {
            'fields': ('bet_id', 'user', 'bet_type', 'status')
        }),
        ('Financial Details', {
            'fields': ('stake', 'total_odds', 'potential_win', 'actual_win')
        }),
        ('Cashout', {
            'fields': ('cashout_value', 'cashout_at')
        }),
        ('Timestamps', {
            'fields': ('placed_at', 'settled_at')
        }),
    )

    actions = ['settle_as_won', 'settle_as_lost', 'void_bets']

    def settle_as_won(self, request, queryset):
        """Settle selected bets as won"""
        count = 0
        for bet in queryset.filter(status='pending'):
            bet.status = 'won'
            bet.actual_win = bet.potential_win
            bet.settled_at = timezone.now()
            bet.save()
            count += 1

        self.message_user(request, f'{count} bets settled as won.')
    settle_as_won.short_description = 'Settle selected bets as won'

    def settle_as_lost(self, request, queryset):
        """Settle selected bets as lost"""
        count = 0
        for bet in queryset.filter(status='pending'):
            bet.status = 'lost'
            bet.settled_at = timezone.now()
            bet.save()
            count += 1

        self.message_user(request, f'{count} bets settled as lost.')
    settle_as_lost.short_description = 'Settle selected bets as lost'

    def void_bets(self, request, queryset):
        """Void selected bets"""
        count = 0
        for bet in queryset.filter(status='pending'):
            bet.status = 'void'
            bet.settled_at = timezone.now()
            bet.save()
            count += 1

        self.message_user(request, f'{count} bets voided.')
    void_bets.short_description = 'Void selected bets'


@admin.register(BetSlip)
class BetSlipAdmin(admin.ModelAdmin):
    """
    Bet Slip admin interface
    """
    list_display = [
        'user', 'bet_type', 'selections_count', 'stake',
        'total_odds', 'potential_win', 'updated_at'
    ]
    list_filter = ['bet_type', 'updated_at']
    search_fields = ['user__email', 'user__username']

    def selections_count(self, obj):
        return obj.selections.count()
    selections_count.short_description = 'Selections'


class SettlementHistoryInline(admin.TabularInline):
    """
    Inline admin for settlement history
    """
    model = SettlementHistory
    extra = 0
    fields = ['action', 'old_status', 'new_status', 'old_amount', 'new_amount', 'reason', 'changed_by']
    readonly_fields = ['changed_at']


@admin.register(Settlement)
class SettlementAdmin(admin.ModelAdmin):
    """
    Settlement admin interface
    """
    list_display = [
        'settlement_id', 'bet', 'market', 'settlement_type', 'status',
        'settlement_amount', 'net_amount', 'settled_at'
    ]
    list_filter = ['settlement_type', 'status', 'settled_at']
    search_fields = ['settlement_id', 'bet__bet_id', 'market__name']
    readonly_fields = ['settlement_id', 'created_at']
    inlines = [SettlementHistoryInline]

    fieldsets = (
        ('Settlement Information', {
            'fields': ('settlement_id', 'bet', 'market', 'settlement_type', 'status')
        }),
        ('Settlement Results', {
            'fields': ('winning_selection', 'settlement_amount', 'tax_amount', 'net_amount')
        }),
        ('Settlement Details', {
            'fields': ('settlement_reason', 'settlement_notes', 'settlement_data')
        }),
        ('Staff Information', {
            'fields': ('settled_by', 'created_at', 'settled_at')
        }),
    )

    actions = ['settle_pending', 'void_settlements']

    def settle_pending(self, request, queryset):
        """Settle pending settlements"""
        count = 0
        for settlement in queryset.filter(status='pending'):
            if settlement.settle(settled_by=request.user):
                count += 1

        self.message_user(request, f'{count} settlements processed.')
    settle_pending.short_description = 'Settle pending settlements'

    def void_settlements(self, request, queryset):
        """Void selected settlements"""
        count = 0
        for settlement in queryset:
            settlement.void_settlement(reason='Admin void', voided_by=request.user)
            count += 1

        self.message_user(request, f'{count} settlements voided.')
    void_settlements.short_description = 'Void selected settlements'


@admin.register(SettlementHistory)
class SettlementHistoryAdmin(admin.ModelAdmin):
    """
    Settlement History admin interface
    """
    list_display = [
        'settlement', 'action', 'old_status', 'new_status',
        'old_amount', 'new_amount', 'changed_by', 'changed_at'
    ]
    list_filter = ['action', 'changed_at']
    search_fields = ['settlement__settlement_id', 'reason']
    readonly_fields = ['changed_at']

    fieldsets = (
        ('Settlement Change', {
            'fields': ('settlement', 'action', 'old_status', 'new_status')
        }),
        ('Amount Changes', {
            'fields': ('old_amount', 'new_amount')
        }),
        ('Change Details', {
            'fields': ('reason', 'notes', 'changed_by', 'changed_at')
        }),
    )


@admin.register(BetSettlementRule)
class BetSettlementRuleAdmin(admin.ModelAdmin):
    """
    Bet Settlement Rule admin interface
    """
    list_display = [
        'rule_name', 'bet_type', 'sport', 'rule_type', 'is_active', 'priority'
    ]
    list_filter = ['bet_type', 'sport', 'rule_type', 'is_active']
    search_fields = ['rule_name', 'rule_description']

    fieldsets = (
        ('Rule Information', {
            'fields': ('rule_name', 'rule_description', 'bet_type', 'sport')
        }),
        ('Rule Configuration', {
            'fields': ('rule_type', 'rule_config')
        }),
        ('Rule Settings', {
            'fields': ('is_active', 'priority')
        }),
    )


@admin.register(BetCancellationRule)
class BetCancellationRuleAdmin(admin.ModelAdmin):
    """
    Bet Cancellation Rule admin interface
    """
    list_display = [
        'rule_name', 'rule_type', 'is_active', 'priority',
        'cancellation_fee_percentage', 'minimum_fee'
    ]
    list_filter = ['rule_type', 'is_active']
    search_fields = ['rule_name', 'description']
    filter_horizontal = ['sports', 'bet_types']

    fieldsets = (
        ('Rule Information', {
            'fields': ('rule_name', 'rule_type', 'description')
        }),
        ('Rule Scope', {
            'fields': ('sports', 'bet_types')
        }),
        ('Rule Configuration', {
            'fields': ('rule_config',)
        }),
        ('Fees', {
            'fields': ('cancellation_fee_percentage', 'minimum_fee')
        }),
        ('Settings', {
            'fields': ('is_active', 'priority')
        }),
    )


@admin.register(BetCancellation)
class BetCancellationAdmin(admin.ModelAdmin):
    """
    Bet Cancellation admin interface
    """
    list_display = [
        'cancellation_id', 'bet', 'status', 'reason',
        'refund_amount', 'cancellation_fee', 'net_refund', 'requested_at'
    ]
    list_filter = ['status', 'reason', 'requested_at']
    search_fields = ['cancellation_id', 'bet__bet_id', 'bet__user__email']
    readonly_fields = ['cancellation_id', 'requested_at']

    fieldsets = (
        ('Cancellation Information', {
            'fields': ('cancellation_id', 'bet', 'status', 'reason')
        }),
        ('Cancellation Details', {
            'fields': ('reason_details', 'processing_notes')
        }),
        ('Financial Details', {
            'fields': ('refund_amount', 'cancellation_fee', 'net_refund')
        }),
        ('Processing Information', {
            'fields': ('processed_by', 'requested_at', 'processed_at')
        }),
    )

    actions = ['approve_cancellations', 'reject_cancellations']

    def approve_cancellations(self, request, queryset):
        """Approve selected cancellations"""
        count = 0
        for cancellation in queryset.filter(status='requested'):
            if cancellation.approve_cancellation(processed_by=request.user):
                count += 1

        self.message_user(request, f'{count} cancellations approved.')
    approve_cancellations.short_description = 'Approve selected cancellations'

    def reject_cancellations(self, request, queryset):
        """Reject selected cancellations"""
        count = 0
        for cancellation in queryset.filter(status='requested'):
            if cancellation.reject_cancellation(processed_by=request.user):
                count += 1

        self.message_user(request, f'{count} cancellations rejected.')
    reject_cancellations.short_description = 'Reject selected cancellations'


@admin.register(OddsComparison)
class OddsComparisonAdmin(admin.ModelAdmin):
    """
    Odds Comparison admin interface
    """
    list_display = [
        'match', 'bet_type', 'best_home_odds', 'best_draw_odds',
        'best_away_odds', 'total_markets', 'last_updated'
    ]
    list_filter = ['bet_type', 'last_updated']
    search_fields = ['match__home_team__name', 'match__away_team__name', 'bet_type__name']
    readonly_fields = ['last_updated']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'match__home_team', 'match__away_team', 'bet_type'
        )


@admin.register(UserOddsAlert)
class UserOddsAlertAdmin(admin.ModelAdmin):
    """
    User Odds Alert admin interface
    """
    list_display = [
        'user', 'selection', 'alert_type', 'target_odds',
        'current_odds', 'status', 'created_at'
    ]
    list_filter = ['alert_type', 'status', 'created_at']
    search_fields = ['user__email', 'selection__name']
    readonly_fields = ['created_at', 'triggered_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'selection__market__match'
        )


@admin.register(UserFavorite)
class UserFavoriteAdmin(admin.ModelAdmin):
    """
    User Favorite admin interface
    """
    list_display = [
        'user', 'favorite_type', 'favorite_name', 'notify_on_odds_change',
        'notify_on_match_start', 'created_at'
    ]
    list_filter = ['favorite_type', 'notify_on_odds_change', 'notify_on_match_start', 'created_at']
    search_fields = ['user__email']
    readonly_fields = ['created_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'match__home_team', 'match__away_team', 'team', 'league', 'sport'
        )


class FavoriteGroupMembershipInline(admin.TabularInline):
    """
    Inline for favorite group memberships
    """
    model = FavoriteGroupMembership
    extra = 0
    readonly_fields = ['added_at', 'added_by']


@admin.register(FavoriteGroup)
class FavoriteGroupAdmin(admin.ModelAdmin):
    """
    Favorite Group admin interface
    """
    list_display = [
        'user', 'name', 'favorites_count', 'is_default',
        'is_public', 'created_at'
    ]
    list_filter = ['is_default', 'is_public', 'created_at']
    search_fields = ['user__email', 'name']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [FavoriteGroupMembershipInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(FavoriteNotification)
class FavoriteNotificationAdmin(admin.ModelAdmin):
    """
    Favorite Notification admin interface
    """
    list_display = [
        'user', 'notification_type', 'title', 'status',
        'delivery_method', 'created_at', 'sent_at'
    ]
    list_filter = ['notification_type', 'status', 'delivery_method', 'created_at']
    search_fields = ['user__email', 'title', 'message']
    readonly_fields = ['created_at', 'sent_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'favorite'
        )


class NotificationDeliveryLogInline(admin.TabularInline):
    """
    Inline for notification delivery logs
    """
    model = NotificationDeliveryLog
    extra = 0
    readonly_fields = ['created_at', 'sent_at', 'delivered_at', 'clicked_at']


@admin.register(LiveBetNotification)
class LiveBetNotificationAdmin(admin.ModelAdmin):
    """
    Live Bet Notification admin interface
    """
    list_display = [
        'user', 'notification_type', 'title', 'priority',
        'delivery_status', 'is_broadcast', 'created_at'
    ]
    list_filter = [
        'notification_type', 'priority', 'delivery_status',
        'is_broadcast', 'send_push', 'send_email', 'send_sms', 'created_at'
    ]
    search_fields = ['user__email', 'title', 'message']
    readonly_fields = ['created_at', 'sent_at', 'delivered_at']
    inlines = [NotificationDeliveryLogInline]

    fieldsets = (
        ('Target', {
            'fields': ('user', 'is_broadcast')
        }),
        ('Related Objects', {
            'fields': ('match', 'market', 'selection', 'bet')
        }),
        ('Notification Details', {
            'fields': ('notification_type', 'title', 'message', 'priority', 'data')
        }),
        ('Delivery Settings', {
            'fields': ('send_push', 'send_email', 'send_sms')
        }),
        ('Status & Timing', {
            'fields': ('delivery_status', 'scheduled_at', 'expires_at', 'created_at', 'sent_at', 'delivered_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'match', 'market', 'bet'
        )

    actions = ['mark_as_sent', 'mark_as_delivered']

    def mark_as_sent(self, request, queryset):
        """Mark selected notifications as sent"""
        count = 0
        for notification in queryset:
            if notification.delivery_status == 'pending':
                notification.mark_as_sent()
                count += 1

        self.message_user(request, f'{count} notifications marked as sent.')
    mark_as_sent.short_description = 'Mark selected notifications as sent'

    def mark_as_delivered(self, request, queryset):
        """Mark selected notifications as delivered"""
        count = 0
        for notification in queryset:
            if notification.delivery_status in ['pending', 'sent']:
                notification.mark_as_delivered()
                count += 1

        self.message_user(request, f'{count} notifications marked as delivered.')
    mark_as_delivered.short_description = 'Mark selected notifications as delivered'


@admin.register(UserNotificationPreference)
class UserNotificationPreferenceAdmin(admin.ModelAdmin):
    """
    User Notification Preference admin interface
    """
    list_display = [
        'user', 'enable_live_notifications', 'enable_push_notifications',
        'enable_email_notifications', 'max_notifications_per_hour', 'updated_at'
    ]
    list_filter = [
        'enable_live_notifications', 'enable_push_notifications',
        'enable_email_notifications', 'enable_sms_notifications',
        'only_favorite_matches', 'only_active_bets'
    ]
    search_fields = ['user__email']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('General Settings', {
            'fields': ('user', 'enable_live_notifications')
        }),
        ('Delivery Methods', {
            'fields': ('enable_push_notifications', 'enable_email_notifications', 'enable_sms_notifications')
        }),
        ('Notification Types', {
            'fields': (
                'notify_odds_changes', 'notify_new_markets', 'notify_market_suspensions',
                'notify_match_events', 'notify_bet_results', 'notify_cashout_opportunities',
                'notify_live_streams'
            )
        }),
        ('Frequency & Timing', {
            'fields': ('max_notifications_per_hour', 'quiet_hours_start', 'quiet_hours_end')
        }),
        ('Match Preferences', {
            'fields': ('only_favorite_matches', 'only_active_bets', 'minimum_odds_change')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(NotificationDeliveryLog)
class NotificationDeliveryLogAdmin(admin.ModelAdmin):
    """
    Notification Delivery Log admin interface
    """
    list_display = [
        'user', 'notification', 'delivery_method', 'delivery_status',
        'provider', 'created_at', 'sent_at'
    ]
    list_filter = [
        'delivery_method', 'delivery_status', 'provider', 'created_at'
    ]
    search_fields = ['user__email', 'recipient_address', 'provider_message_id']
    readonly_fields = ['created_at', 'sent_at', 'delivered_at', 'clicked_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'notification'
        )


@admin.register(LiveBetNotificationTemplate)
class LiveBetNotificationTemplateAdmin(admin.ModelAdmin):
    """
    Live Bet Notification Template admin interface
    """
    list_display = [
        'name', 'template_type', 'priority', 'is_active',
        'default_push', 'default_email', 'default_sms'
    ]
    list_filter = ['template_type', 'priority', 'is_active', 'default_push', 'default_email']
    search_fields = ['name', 'title_template', 'message_template']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'template_type', 'is_active')
        }),
        ('Template Content', {
            'fields': ('title_template', 'message_template', 'available_variables')
        }),
        ('Settings', {
            'fields': ('priority', 'default_push', 'default_email', 'default_sms')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


class VirtualTeamInline(admin.TabularInline):
    """
    Inline for virtual teams
    """
    model = VirtualTeam
    extra = 0
    fields = ['name', 'short_name', 'overall_rating', 'is_active']


class VirtualBetTypeInline(admin.TabularInline):
    """
    Inline for virtual bet types
    """
    model = VirtualBetType
    extra = 0
    fields = ['name', 'slug', 'is_active', 'display_order']


@admin.register(VirtualSport)
class VirtualSportAdmin(admin.ModelAdmin):
    """
    Virtual Sport admin interface
    """
    list_display = [
        'name', 'sport_type', 'is_active', 'matches_per_day',
        'interval_minutes', 'min_bet_amount', 'max_bet_amount'
    ]
    list_filter = ['sport_type', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [VirtualTeamInline, VirtualBetTypeInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'sport_type', 'description', 'is_active')
        }),
        ('Visual Settings', {
            'fields': ('logo', 'background_image')
        }),
        ('Game Settings', {
            'fields': ('match_duration_minutes', 'matches_per_day', 'interval_minutes')
        }),
        ('Betting Settings', {
            'fields': ('min_bet_amount', 'max_bet_amount')
        }),
        ('Algorithm Settings', {
            'fields': ('randomness_factor', 'home_advantage')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(VirtualTeam)
class VirtualTeamAdmin(admin.ModelAdmin):
    """
    Virtual Team admin interface
    """
    list_display = [
        'name', 'virtual_sport', 'overall_rating', 'matches_played',
        'wins', 'draws', 'losses', 'win_percentage', 'is_active'
    ]
    list_filter = ['virtual_sport', 'is_active']
    search_fields = ['name', 'short_name']
    readonly_fields = ['matches_played', 'wins', 'draws', 'losses', 'goals_for', 'goals_against', 'created_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('virtual_sport', 'name', 'short_name', 'is_active')
        }),
        ('Team Attributes', {
            'fields': ('attack_rating', 'defense_rating', 'midfield_rating', 'overall_rating')
        }),
        ('Visual Elements', {
            'fields': ('logo', 'primary_color', 'secondary_color')
        }),
        ('Statistics', {
            'fields': ('matches_played', 'wins', 'draws', 'losses', 'goals_for', 'goals_against')
        }),
        ('Timestamps', {
            'fields': ('created_at',)
        }),
    )


class VirtualSelectionInline(admin.TabularInline):
    """
    Inline for virtual selections
    """
    model = VirtualSelection
    extra = 0
    fields = ['name', 'selection_key', 'decimal_odds', 'status']


class VirtualMarketInline(admin.TabularInline):
    """
    Inline for virtual markets
    """
    model = VirtualMarket
    extra = 0
    fields = ['bet_type', 'name', 'status']


@admin.register(VirtualMatch)
class VirtualMatchAdmin(admin.ModelAdmin):
    """
    Virtual Match admin interface
    """
    list_display = [
        'match_id', 'virtual_sport', 'home_team', 'away_team',
        'start_time', 'status', 'home_score', 'away_score'
    ]
    list_filter = ['virtual_sport', 'status', 'start_time']
    search_fields = ['match_id', 'home_team__name', 'away_team__name']
    readonly_fields = ['match_id', 'created_at', 'end_time', 'events']
    inlines = [VirtualMarketInline]

    fieldsets = (
        ('Match Details', {
            'fields': ('match_id', 'virtual_sport', 'home_team', 'away_team')
        }),
        ('Timing', {
            'fields': ('start_time', 'end_time', 'status')
        }),
        ('Results', {
            'fields': ('home_score', 'away_score', 'events')
        }),
        ('Algorithm Data', {
            'fields': ('algorithm_seed', 'home_win_probability', 'draw_probability', 'away_win_probability')
        }),
        ('Betting', {
            'fields': ('betting_enabled', 'total_bets', 'total_stake')
        }),
        ('Timestamps', {
            'fields': ('created_at',)
        }),
    )

    actions = ['simulate_matches']

    def simulate_matches(self, request, queryset):
        """Simulate selected matches"""
        count = 0
        for match in queryset.filter(status='scheduled'):
            if match.simulate_match():
                count += 1

        self.message_user(request, f'{count} matches simulated successfully.')
    simulate_matches.short_description = 'Simulate selected matches'


@admin.register(VirtualMarket)
class VirtualMarketAdmin(admin.ModelAdmin):
    """
    Virtual Market admin interface
    """
    list_display = [
        'name', 'virtual_match', 'bet_type', 'status', 'winning_selection', 'settled_at'
    ]
    list_filter = ['bet_type', 'status', 'virtual_match__virtual_sport']
    search_fields = ['name', 'virtual_match__match_id']
    readonly_fields = ['settled_at', 'created_at']
    inlines = [VirtualSelectionInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'virtual_match', 'bet_type'
        )


class VirtualBetSelectionInline(admin.TabularInline):
    """
    Inline for virtual bet selections
    """
    model = VirtualBetSelection
    extra = 0
    readonly_fields = ['odds_at_placement', 'created_at']


@admin.register(VirtualBet)
class VirtualBetAdmin(admin.ModelAdmin):
    """
    Virtual Bet admin interface
    """
    list_display = [
        'bet_id', 'user', 'bet_type', 'stake', 'total_odds',
        'potential_winnings', 'status', 'created_at'
    ]
    list_filter = ['bet_type', 'status', 'created_at']
    search_fields = ['bet_id', 'user__email']
    readonly_fields = ['bet_id', 'potential_winnings', 'settled_at', 'created_at']
    inlines = [VirtualBetSelectionInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(VirtualSportsManager)
class VirtualSportsManagerAdmin(admin.ModelAdmin):
    """
    Virtual Sports Manager admin interface
    """
    list_display = [
        'virtual_sport', 'auto_generate_matches', 'auto_generate_odds',
        'generation_status', 'last_generation_time', 'total_matches_generated'
    ]
    list_filter = ['auto_generate_matches', 'auto_generate_odds', 'generation_status']
    readonly_fields = [
        'generation_status', 'last_generation_time', 'last_settlement_time',
        'total_matches_generated', 'total_matches_settled', 'total_bets_processed',
        'created_at', 'updated_at'
    ]

    fieldsets = (
        ('Virtual Sport', {
            'fields': ('virtual_sport',)
        }),
        ('Auto-generation Settings', {
            'fields': ('auto_generate_matches', 'auto_generate_odds', 'auto_settle_matches')
        }),
        ('Generation Parameters', {
            'fields': ('matches_ahead_hours', 'odds_margin')
        }),
        ('Status', {
            'fields': ('generation_status', 'last_error_message')
        }),
        ('Statistics', {
            'fields': (
                'total_matches_generated', 'total_matches_settled', 'total_bets_processed',
                'last_generation_time', 'last_settlement_time'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    actions = ['generate_matches', 'settle_matches']

    def generate_matches(self, request, queryset):
        """Generate matches for selected managers"""
        total_generated = 0
        for manager in queryset:
            try:
                count = manager.generate_upcoming_matches()
                total_generated += count
            except Exception as e:
                self.message_user(request, f'Error generating matches for {manager.virtual_sport}: {e}', level='ERROR')

        if total_generated > 0:
            self.message_user(request, f'{total_generated} matches generated successfully.')
    generate_matches.short_description = 'Generate upcoming matches'

    def settle_matches(self, request, queryset):
        """Settle finished matches for selected managers"""
        total_settled = 0
        for manager in queryset:
            try:
                count = manager.settle_finished_matches()
                total_settled += count
            except Exception as e:
                self.message_user(request, f'Error settling matches for {manager.virtual_sport}: {e}', level='ERROR')

        if total_settled > 0:
            self.message_user(request, f'{total_settled} markets settled successfully.')
    settle_matches.short_description = 'Settle finished matches'
