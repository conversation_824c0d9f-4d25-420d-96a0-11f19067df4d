{% extends 'base.html' %}

{% block title %}Upload Profile Picture - ZBet{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card">
                <div class="card-header zbet-primary text-white">
                    <h6 class="mb-0">Account Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'accounts:profile_update' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user"></i> Profile
                    </a>
                    <a href="{% url 'accounts:profile_picture_upload' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-camera"></i> Profile Picture
                    </a>
                    <a href="{% url 'accounts:change_email' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-at"></i> Change Email
                    </a>
                    <a href="{% url 'accounts:change_phone' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-mobile-alt"></i> Change Phone
                    </a>
                    <a href="{% url 'accounts:two_factor_settings' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> Two-Factor Authentication
                    </a>
                    <a href="{% url 'accounts:social_connections' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-link"></i> Social Connections
                    </a>
                    <a href="{% url 'accounts:notification_preferences' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card shadow">
                        <div class="card-header zbet-primary text-white text-center">
                            <h3 class="mb-0">Upload Profile Picture</h3>
                            <p class="mb-0">Personalize your account with a profile picture</p>
                        </div>
                        
                        <div class="card-body">
                            <!-- Current Profile Picture -->
                            <div class="text-center mb-4">
                                <div class="position-relative d-inline-block">
                                    {% if profile.avatar %}
                                        <img src="{{ profile.avatar.url }}" alt="Current Profile" 
                                             class="rounded-circle border border-3 border-primary" 
                                             width="150" height="150" id="currentAvatar">
                                    {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center border border-3 border-primary" 
                                             style="width: 150px; height: 150px;" id="currentAvatar">
                                            <i class="fas fa-user text-white fa-4x"></i>
                                        </div>
                                    {% endif %}
                                    
                                    <!-- Camera overlay -->
                                    <div class="position-absolute bottom-0 end-0">
                                        <div class="bg-primary rounded-circle p-2">
                                            <i class="fas fa-camera text-white"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <h5 class="mt-3">{{ user.first_name }} {{ user.last_name }}</h5>
                                <p class="text-muted">{{ user.email }}</p>
                            </div>
                            
                            <!-- Upload Form -->
                            <form method="post" enctype="multipart/form-data" id="avatarUploadForm">
                                {% csrf_token %}
                                
                                <!-- File Input -->
                                <div class="mb-4">
                                    <label for="{{ form.avatar.id_for_label }}" class="form-label">
                                        <strong>Choose Profile Picture</strong>
                                    </label>
                                    {{ form.avatar }}
                                    <div class="form-text">
                                        Supported formats: JPEG, PNG, GIF. Maximum size: 5MB.
                                    </div>
                                    {% if form.avatar.errors %}
                                        <div class="text-danger small">{{ form.avatar.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <!-- Preview Area -->
                                <div class="mb-4" id="previewArea" style="display: none;">
                                    <label class="form-label"><strong>Preview</strong></label>
                                    <div class="text-center">
                                        <img id="imagePreview" class="rounded-circle border border-2 border-success" 
                                             width="120" height="120" style="object-fit: cover;">
                                    </div>
                                </div>
                                
                                <!-- Upload Guidelines -->
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Photo Guidelines</h6>
                                    <ul class="mb-0">
                                        <li>Use a clear, recent photo of yourself</li>
                                        <li>Face should be clearly visible</li>
                                        <li>Avoid group photos or images with text</li>
                                        <li>Professional or casual photos work best</li>
                                    </ul>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button type="submit" class="btn btn-zbet btn-lg" id="uploadBtn" disabled>
                                        <i class="fas fa-upload"></i> Upload Picture
                                    </button>
                                    {% if profile.avatar %}
                                        <button type="button" class="btn btn-outline-danger btn-lg" id="removeBtn">
                                            <i class="fas fa-trash"></i> Remove Picture
                                        </button>
                                    {% endif %}
                                </div>
                            </form>
                            
                            <hr>
                            
                            <div class="text-center">
                                <a href="{% url 'accounts:profile_update' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Profile
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tips Card -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-lightbulb"></i> Tips for Great Profile Pictures</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <i class="fas fa-smile fa-2x text-success mb-2"></i>
                                    <h6>Good Lighting</h6>
                                    <small class="text-muted">Natural light works best</small>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                                    <h6>Clear Face</h6>
                                    <small class="text-muted">Face should be clearly visible</small>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-crop fa-2x text-warning mb-2"></i>
                                    <h6>Square Format</h6>
                                    <small class="text-muted">Square images work best</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Picture Modal -->
{% if profile.avatar %}
<div class="modal fade" id="removePictureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Profile Picture</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove your profile picture?</p>
                <p class="text-muted">You can always upload a new one later.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" name="remove_avatar" value="1">
                    <button type="submit" class="btn btn-danger">Remove Picture</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // File input change handler
    $('#{{ form.avatar.id_for_label }}').on('change', function(e) {
        const file = e.target.files[0];
        const uploadBtn = $('#uploadBtn');
        const previewArea = $('#previewArea');
        const imagePreview = $('#imagePreview');
        
        if (file) {
            // Validate file type
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!validTypes.includes(file.type)) {
                alert('Please select a valid image file (JPEG, PNG, or GIF).');
                this.value = '';
                uploadBtn.prop('disabled', true);
                previewArea.hide();
                return;
            }
            
            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB.');
                this.value = '';
                uploadBtn.prop('disabled', true);
                previewArea.hide();
                return;
            }
            
            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.attr('src', e.target.result);
                previewArea.show();
                uploadBtn.prop('disabled', false);
            };
            reader.readAsDataURL(file);
        } else {
            uploadBtn.prop('disabled', true);
            previewArea.hide();
        }
    });
    
    // Remove picture button
    $('#removeBtn').on('click', function() {
        $('#removePictureModal').modal('show');
    });
    
    // Form submission
    $('#avatarUploadForm').on('submit', function() {
        $('#uploadBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Uploading...');
    });
});
</script>
{% endblock %}
