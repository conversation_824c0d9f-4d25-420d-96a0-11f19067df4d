# Generated by Django 5.2.4 on 2025-07-05 21:06

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("betting", "0005_favoritegroup_userfavorite_favoritenotification_and_more"),
        ("sports", "0002_remove_match_statistics_match_stats_data_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="LiveBetNotificationTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("odds_change", "Odds Change"),
                            ("new_market", "New Market"),
                            ("market_suspended", "Market Suspended"),
                            ("goal_scored", "Goal Scored"),
                            ("bet_result", "Bet Result"),
                            ("cashout", "Cash Out"),
                            ("general", "General"),
                        ],
                        max_length=30,
                    ),
                ),
                ("title_template", models.Char<PERSON>ield(max_length=200)),
                ("message_template", models.TextField()),
                ("available_variables", models.JSONField(blank=True, default=list)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("default_push", models.BooleanField(default=True)),
                ("default_email", models.BooleanField(default=False)),
                ("default_sms", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Live Bet Notification Template",
                "verbose_name_plural": "Live Bet Notification Templates",
                "db_table": "betting_live_bet_notification_template",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="LiveBetNotification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_broadcast", models.BooleanField(default=False)),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("odds_change", "Odds Change"),
                            ("new_market", "New Market Available"),
                            ("market_suspended", "Market Suspended"),
                            ("market_reopened", "Market Reopened"),
                            ("goal_scored", "Goal Scored"),
                            ("red_card", "Red Card"),
                            ("penalty_awarded", "Penalty Awarded"),
                            ("match_event", "Match Event"),
                            ("bet_won", "Bet Won"),
                            ("bet_lost", "Bet Lost"),
                            ("cashout_available", "Cash Out Available"),
                            ("cashout_unavailable", "Cash Out Unavailable"),
                            ("live_stream_available", "Live Stream Available"),
                        ],
                        max_length=30,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("data", models.JSONField(blank=True, default=dict)),
                ("send_push", models.BooleanField(default=True)),
                ("send_email", models.BooleanField(default=False)),
                ("send_sms", models.BooleanField(default=False)),
                (
                    "delivery_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("scheduled_at", models.DateTimeField(blank=True, null=True)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("delivered_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "bet",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="betting.bet",
                    ),
                ),
                (
                    "market",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="betting.market",
                    ),
                ),
                (
                    "match",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="sports.match",
                    ),
                ),
                (
                    "selection",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="betting.selection",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="live_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Live Bet Notification",
                "verbose_name_plural": "Live Bet Notifications",
                "db_table": "betting_live_bet_notification",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="NotificationDeliveryLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delivery_method",
                    models.CharField(
                        choices=[
                            ("push", "Push Notification"),
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("websocket", "WebSocket"),
                            ("in_app", "In-App Notification"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "delivery_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("bounced", "Bounced"),
                            ("clicked", "Clicked"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("recipient_address", models.CharField(blank=True, max_length=255)),
                ("provider", models.CharField(blank=True, max_length=100)),
                ("provider_message_id", models.CharField(blank=True, max_length=255)),
                ("response_data", models.JSONField(blank=True, default=dict)),
                ("error_message", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("delivered_at", models.DateTimeField(blank=True, null=True)),
                ("clicked_at", models.DateTimeField(blank=True, null=True)),
                (
                    "notification",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="delivery_logs",
                        to="betting.livebetnotification",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_delivery_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Delivery Log",
                "verbose_name_plural": "Notification Delivery Logs",
                "db_table": "betting_notification_delivery_log",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserNotificationPreference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("enable_live_notifications", models.BooleanField(default=True)),
                ("enable_push_notifications", models.BooleanField(default=True)),
                ("enable_email_notifications", models.BooleanField(default=False)),
                ("enable_sms_notifications", models.BooleanField(default=False)),
                ("notify_odds_changes", models.BooleanField(default=True)),
                ("notify_new_markets", models.BooleanField(default=True)),
                ("notify_market_suspensions", models.BooleanField(default=True)),
                ("notify_match_events", models.BooleanField(default=True)),
                ("notify_bet_results", models.BooleanField(default=True)),
                ("notify_cashout_opportunities", models.BooleanField(default=True)),
                ("notify_live_streams", models.BooleanField(default=False)),
                ("max_notifications_per_hour", models.PositiveIntegerField(default=10)),
                ("quiet_hours_start", models.TimeField(blank=True, null=True)),
                ("quiet_hours_end", models.TimeField(blank=True, null=True)),
                ("only_favorite_matches", models.BooleanField(default=False)),
                ("only_active_bets", models.BooleanField(default=False)),
                (
                    "minimum_odds_change",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.10"), max_digits=5
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Notification Preference",
                "verbose_name_plural": "User Notification Preferences",
                "db_table": "betting_user_notification_preference",
            },
        ),
        migrations.AddIndex(
            model_name="livebetnotification",
            index=models.Index(
                fields=["user", "delivery_status"],
                name="betting_liv_user_id_c4df46_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="livebetnotification",
            index=models.Index(
                fields=["notification_type", "created_at"],
                name="betting_liv_notific_f6c725_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="livebetnotification",
            index=models.Index(
                fields=["match", "created_at"], name="betting_liv_match_i_c2966b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notificationdeliverylog",
            index=models.Index(
                fields=["user", "delivery_status"],
                name="betting_not_user_id_3ac211_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="notificationdeliverylog",
            index=models.Index(
                fields=["notification", "delivery_method"],
                name="betting_not_notific_21cb9c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="notificationdeliverylog",
            index=models.Index(
                fields=["delivery_status", "created_at"],
                name="betting_not_deliver_40df1d_idx",
            ),
        ),
    ]
