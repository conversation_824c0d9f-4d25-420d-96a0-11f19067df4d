{% extends 'base.html' %}

{% block title %}Reset Password - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Reset Password</h3>
                <p class="mb-0">Enter your email to reset your password</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-key fa-3x text-primary mb-3"></i>
                    <p class="text-muted">
                        Enter the email address associated with your account and we'll send you a link to reset your password.
                    </p>
                </div>
                
                <form method="post" id="passwordResetForm">
                    {% csrf_token %}
                    
                    <!-- Email -->
                    <div class="form-floating mb-3">
                        {{ form.email }}
                        <label for="{{ form.email.id_for_label }}">Email Address</label>
                        <div class="form-text">{{ form.email.help_text }}</div>
                        {% if form.email.errors %}
                            <div class="text-danger small">{{ form.email.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Non-field errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Send Reset Link
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-0">Remember your password?</p>
                    <a href="{% url 'accounts:login' %}" class="btn btn-outline-secondary">
                        Back to Login
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Security Notice -->
        <div class="card mt-3">
            <div class="card-body text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    For security reasons, we'll send reset instructions to your email even if the account doesn't exist.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on email field
    $('#{{ form.email.id_for_label }}').focus();
    
    // Form validation
    $('#passwordResetForm').on('submit', function(e) {
        const email = $('#{{ form.email.id_for_label }}').val().trim();
        
        if (!email) {
            e.preventDefault();
            alert('Please enter your email address.');
            $('#{{ form.email.id_for_label }}').focus();
            return false;
        }
        
        if (!email.includes('@')) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            $('#{{ form.email.id_for_label }}').focus();
            return false;
        }
    });
});
</script>
{% endblock %}
