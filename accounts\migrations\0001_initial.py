# Generated by Django 4.2.7 on 2025-07-04 14:31

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_countries.fields
import phonenumber_field.modelfields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.Char<PERSON>ield(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        help_text="User's email address", max_length=254, unique=True
                    ),
                ),
                (
                    "phone_number",
                    phonenumber_field.modelfields.PhoneNumberField(
                        help_text="User's phone number for M-Pesa and SMS verification",
                        max_length=128,
                        region=None,
                        unique=True,
                    ),
                ),
                (
                    "date_of_birth",
                    models.DateField(
                        blank=True,
                        help_text="User's date of birth for age verification",
                        null=True,
                    ),
                ),
                (
                    "country",
                    django_countries.fields.CountryField(
                        default="KE",
                        help_text="User's country of residence",
                        max_length=2,
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True, help_text="User's city", max_length=100
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user has completed email/phone verification",
                    ),
                ),
                (
                    "is_kyc_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user has completed KYC verification",
                    ),
                ),
                (
                    "is_suspended",
                    models.BooleanField(
                        default=False, help_text="Whether the user account is suspended"
                    ),
                ),
                ("email_verified_at", models.DateTimeField(blank=True, null=True)),
                ("phone_verified_at", models.DateTimeField(blank=True, null=True)),
                ("kyc_verified_at", models.DateTimeField(blank=True, null=True)),
                (
                    "preferred_language",
                    models.CharField(
                        choices=[
                            ("en", "English"),
                            ("sw", "Swahili"),
                            ("fr", "French"),
                        ],
                        default="en",
                        max_length=10,
                    ),
                ),
                (
                    "preferred_currency",
                    models.CharField(
                        choices=[
                            ("KES", "Kenyan Shilling"),
                            ("USD", "US Dollar"),
                            ("EUR", "Euro"),
                            ("GHS", "Ghanaian Cedi"),
                            ("NGN", "Nigerian Naira"),
                        ],
                        default="KES",
                        max_length=3,
                    ),
                ),
                (
                    "daily_deposit_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Daily deposit limit set by user",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "daily_bet_limit",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Daily betting limit set by user",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "self_exclusion_until",
                    models.DateTimeField(
                        blank=True, help_text="Self-exclusion end date", null=True
                    ),
                ),
                (
                    "marketing_emails",
                    models.BooleanField(
                        default=True,
                        help_text="Whether user wants to receive marketing emails",
                    ),
                ),
                (
                    "marketing_sms",
                    models.BooleanField(
                        default=True,
                        help_text="Whether user wants to receive marketing SMS",
                    ),
                ),
                (
                    "referral_code",
                    models.CharField(
                        blank=True,
                        help_text="User's unique referral code",
                        max_length=20,
                        unique=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("last_login_ip", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "referred_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who referred this user",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="referrals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "db_table": "accounts_user",
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="UserSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_key", models.CharField(max_length=40, unique=True)),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                ("device_info", models.JSONField(default=dict)),
                ("country", models.CharField(blank=True, max_length=100)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("last_activity", models.DateTimeField(auto_now=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Session",
                "verbose_name_plural": "User Sessions",
                "db_table": "accounts_user_session",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        help_text="User's profile picture",
                        null=True,
                        upload_to="avatars/",
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("M", "Male"),
                            ("F", "Female"),
                            ("O", "Other"),
                            ("N", "Prefer not to say"),
                        ],
                        max_length=10,
                    ),
                ),
                ("occupation", models.CharField(blank=True, max_length=100)),
                ("address_line_1", models.CharField(blank=True, max_length=255)),
                ("address_line_2", models.CharField(blank=True, max_length=255)),
                ("postal_code", models.CharField(blank=True, max_length=20)),
                (
                    "emergency_contact_name",
                    models.CharField(blank=True, max_length=100),
                ),
                (
                    "emergency_contact_phone",
                    phonenumber_field.modelfields.PhoneNumberField(
                        blank=True, max_length=128, region=None
                    ),
                ),
                (
                    "emergency_contact_relationship",
                    models.CharField(blank=True, max_length=50),
                ),
                (
                    "favorite_sports",
                    models.JSONField(
                        default=list, help_text="List of user's favorite sports"
                    ),
                ),
                (
                    "betting_experience",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("professional", "Professional"),
                        ],
                        default="beginner",
                        max_length=20,
                    ),
                ),
                (
                    "total_deposits",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "total_withdrawals",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("total_bets_placed", models.PositiveIntegerField(default=0)),
                ("total_bets_won", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "db_table": "accounts_user_profile",
            },
        ),
        migrations.CreateModel(
            name="SecurityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("login", "Login"),
                            ("logout", "Logout"),
                            ("password_change", "Password Change"),
                            ("password_reset", "Password Reset"),
                            ("email_change", "Email Change"),
                            ("phone_change", "Phone Change"),
                            ("failed_login", "Failed Login"),
                            ("account_locked", "Account Locked"),
                            ("suspicious_activity", "Suspicious Activity"),
                        ],
                        max_length=20,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField(blank=True)),
                ("details", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="security_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Security Log",
                "verbose_name_plural": "Security Logs",
                "db_table": "accounts_security_log",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="KYCDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("national_id", "National ID"),
                            ("passport", "Passport"),
                            ("driving_license", "Driving License"),
                            ("utility_bill", "Utility Bill"),
                            ("bank_statement", "Bank Statement"),
                            ("selfie", "Selfie with ID"),
                        ],
                        max_length=20,
                    ),
                ),
                ("document_number", models.CharField(blank=True, max_length=50)),
                (
                    "document_file",
                    models.FileField(
                        help_text="Upload clear image of the document",
                        upload_to="kyc_documents/",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("verification_notes", models.TextField(blank=True)),
                ("uploaded_at", models.DateTimeField(auto_now_add=True)),
                ("verified_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="kyc_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verified_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "KYC Document",
                "verbose_name_plural": "KYC Documents",
                "db_table": "accounts_kyc_document",
                "unique_together": {("user", "document_type")},
            },
        ),
    ]
