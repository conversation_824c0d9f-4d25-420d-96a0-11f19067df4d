{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label='payments' %}">Payments</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>{{ title }}</h1>
    
    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card pending">
            <h3>Pending Approvals</h3>
            <div class="stat-number">{{ stats.pending_count }}</div>
            <div class="stat-amount">KES {{ stats.pending_amount|floatformat:2 }}</div>
        </div>
        
        <div class="stat-card today">
            <h3>Today's Withdrawals</h3>
            <div class="stat-number">{{ stats.today_count }}</div>
            <div class="stat-amount">KES {{ stats.today_amount|floatformat:2 }}</div>
        </div>
        
        <div class="stat-card week">
            <h3>This Week</h3>
            <div class="stat-number">{{ stats.week_count }}</div>
            <div class="stat-amount">KES {{ stats.week_amount|floatformat:2 }}</div>
        </div>
    </div>

    <!-- Pending Withdrawals -->
    <div class="module">
        <h2>Pending Withdrawals Requiring Approval</h2>
        {% if pending_withdrawals %}
        <table>
            <thead>
                <tr>
                    <th>Reference</th>
                    <th>User</th>
                    <th>Amount</th>
                    <th>Payment Method</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for withdrawal in pending_withdrawals %}
                <tr>
                    <td>
                        <a href="{% url 'admin:payments_withdrawal_change' withdrawal.id %}">
                            {{ withdrawal.reference }}
                        </a>
                    </td>
                    <td>{{ withdrawal.user.username }}</td>
                    <td>KES {{ withdrawal.amount|floatformat:2 }}</td>
                    <td>{{ withdrawal.payment_method.name }}</td>
                    <td>{{ withdrawal.created_at|date:"M d, Y H:i" }}</td>
                    <td class="actions">
                        <a href="{% url 'payments:admin_approve_withdrawal' withdrawal.id %}" 
                           class="button approve-btn">Approve</a>
                        <a href="{% url 'payments:admin_reject_withdrawal' withdrawal.id %}" 
                           class="button reject-btn">Reject</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>No pending withdrawals requiring approval.</p>
        {% endif %}
    </div>

    <!-- Recent Activity -->
    <div class="module">
        <h2>Recent Approval Activity</h2>
        {% if recent_activity %}
        <table>
            <thead>
                <tr>
                    <th>Reference</th>
                    <th>User</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Processed By</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                {% for withdrawal in recent_activity %}
                <tr>
                    <td>
                        <a href="{% url 'admin:payments_withdrawal_change' withdrawal.id %}">
                            {{ withdrawal.reference }}
                        </a>
                    </td>
                    <td>{{ withdrawal.user.username }}</td>
                    <td>KES {{ withdrawal.amount|floatformat:2 }}</td>
                    <td>
                        <span class="status-badge status-{{ withdrawal.status|lower }}">
                            {{ withdrawal.get_status_display }}
                        </span>
                    </td>
                    <td>
                        {% if withdrawal.approved_by %}
                            {{ withdrawal.approved_by.username }}
                        {% elif withdrawal.rejected_by %}
                            {{ withdrawal.rejected_by.username }}
                        {% else %}
                            System
                        {% endif %}
                    </td>
                    <td>{{ withdrawal.updated_at|date:"M d, Y H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>No recent activity.</p>
        {% endif %}
    </div>
</div>

<style>
.dashboard {
    padding: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card.pending {
    border-left: 4px solid #ffc107;
}

.stat-card.today {
    border-left: 4px solid #28a745;
}

.stat-card.week {
    border-left: 4px solid #007bff;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-amount {
    font-size: 16px;
    color: #666;
}

.module table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.module table th,
.module table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.module table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.actions {
    white-space: nowrap;
}

.approve-btn {
    background-color: #28a745 !important;
    color: white !important;
    margin-right: 5px;
}

.reject-btn {
    background-color: #dc3545 !important;
    color: white !important;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-completed {
    background-color: #d1ecf1;
    color: #0c5460;
}
</style>
{% endblock %}
