# Generated by Django 5.2.4 on 2025-07-05 19:48

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Country",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "code",
                    models.CharField(
                        help_text="ISO 3166-1 alpha-3 code", max_length=3, unique=True
                    ),
                ),
                ("flag_emoji", models.CharField(blank=True, max_length=10)),
                ("is_active", models.BooleanField(default=True)),
            ],
            options={
                "verbose_name": "Country",
                "verbose_name_plural": "Countries",
                "db_table": "sports_country",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Sport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("slug", models.SlugField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.CharField(
                        blank=True, help_text="Font Awesome icon class", max_length=50
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_featured", models.BooleanField(default=False)),
                ("display_order", models.PositiveIntegerField(default=0)),
                (
                    "min_bet_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("10.00"), max_digits=10
                    ),
                ),
                (
                    "max_bet_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("100000.00"), max_digits=15
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Sport",
                "verbose_name_plural": "Sports",
                "db_table": "sports_sport",
                "ordering": ["display_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="League",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("slug", models.SlugField(max_length=200, unique=True)),
                ("short_name", models.CharField(blank=True, max_length=50)),
                (
                    "league_type",
                    models.CharField(
                        choices=[
                            ("domestic", "Domestic League"),
                            ("international", "International Competition"),
                            ("cup", "Cup Competition"),
                            ("friendly", "Friendly"),
                        ],
                        default="domestic",
                        max_length=20,
                    ),
                ),
                ("season", models.CharField(help_text="e.g., 2024/25", max_length=20)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_featured", models.BooleanField(default=False)),
                ("display_order", models.PositiveIntegerField(default=0)),
                (
                    "external_id",
                    models.CharField(
                        blank=True, help_text="External API ID", max_length=100
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="leagues",
                        to="sports.country",
                    ),
                ),
                (
                    "sport",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="leagues",
                        to="sports.sport",
                    ),
                ),
            ],
            options={
                "verbose_name": "League",
                "verbose_name_plural": "Leagues",
                "db_table": "sports_league",
                "ordering": ["sport", "display_order", "name"],
                "unique_together": {("sport", "name", "season")},
            },
        ),
        migrations.CreateModel(
            name="Match",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("start_time", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("live", "Live"),
                            ("halftime", "Half Time"),
                            ("finished", "Finished"),
                            ("postponed", "Postponed"),
                            ("cancelled", "Cancelled"),
                            ("suspended", "Suspended"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                ("round_number", models.PositiveIntegerField(blank=True, null=True)),
                ("venue", models.CharField(blank=True, max_length=200)),
                ("home_score", models.PositiveIntegerField(blank=True, null=True)),
                ("away_score", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "score_details",
                    models.JSONField(
                        blank=True, default=dict, help_text="Detailed score breakdown"
                    ),
                ),
                (
                    "statistics",
                    models.JSONField(
                        blank=True, default=dict, help_text="Match statistics"
                    ),
                ),
                (
                    "minute",
                    models.PositiveIntegerField(
                        blank=True, help_text="Current minute", null=True
                    ),
                ),
                (
                    "period",
                    models.CharField(
                        blank=True, help_text="Current period/half", max_length=20
                    ),
                ),
                (
                    "external_id",
                    models.CharField(
                        blank=True, help_text="External API ID", max_length=100
                    ),
                ),
                ("betting_enabled", models.BooleanField(default=True)),
                ("live_betting_enabled", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "league",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="matches",
                        to="sports.league",
                    ),
                ),
            ],
            options={
                "verbose_name": "Match",
                "verbose_name_plural": "Matches",
                "db_table": "sports_match",
                "ordering": ["start_time"],
            },
        ),
        migrations.CreateModel(
            name="Team",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("slug", models.SlugField(max_length=200, unique=True)),
                ("short_name", models.CharField(blank=True, max_length=50)),
                ("founded_year", models.PositiveIntegerField(blank=True, null=True)),
                ("stadium", models.CharField(blank=True, max_length=200)),
                ("city", models.CharField(blank=True, max_length=100)),
                (
                    "logo",
                    models.ImageField(blank=True, null=True, upload_to="teams/logos/"),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "external_id",
                    models.CharField(
                        blank=True, help_text="External API ID", max_length=100
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="teams",
                        to="sports.country",
                    ),
                ),
                (
                    "leagues",
                    models.ManyToManyField(
                        blank=True, related_name="teams", to="sports.league"
                    ),
                ),
                (
                    "sport",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="teams",
                        to="sports.sport",
                    ),
                ),
            ],
            options={
                "verbose_name": "Team",
                "verbose_name_plural": "Teams",
                "db_table": "sports_team",
                "ordering": ["name"],
                "unique_together": {("sport", "name")},
            },
        ),
        migrations.CreateModel(
            name="MatchEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("goal", "Goal"),
                            ("own_goal", "Own Goal"),
                            ("penalty_goal", "Penalty Goal"),
                            ("yellow_card", "Yellow Card"),
                            ("red_card", "Red Card"),
                            ("substitution", "Substitution"),
                            ("corner", "Corner"),
                            ("offside", "Offside"),
                            ("foul", "Foul"),
                            ("penalty_miss", "Penalty Miss"),
                            ("var_decision", "VAR Decision"),
                            ("injury", "Injury"),
                            ("timeout", "Timeout"),
                        ],
                        max_length=20,
                    ),
                ),
                ("minute", models.PositiveIntegerField()),
                ("period", models.CharField(default="1st Half", max_length=20)),
                ("player_name", models.CharField(blank=True, max_length=200)),
                (
                    "player_2_name",
                    models.CharField(
                        blank=True, help_text="For substitutions", max_length=200
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "match",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="events",
                        to="sports.match",
                    ),
                ),
                (
                    "team",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="match_events",
                        to="sports.team",
                    ),
                ),
            ],
            options={
                "verbose_name": "Match Event",
                "verbose_name_plural": "Match Events",
                "db_table": "sports_match_event",
                "ordering": ["minute", "created_at"],
            },
        ),
        migrations.AddField(
            model_name="match",
            name="away_team",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="away_matches",
                to="sports.team",
            ),
        ),
        migrations.AddField(
            model_name="match",
            name="home_team",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="home_matches",
                to="sports.team",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="match",
            unique_together={("home_team", "away_team", "start_time")},
        ),
    ]
