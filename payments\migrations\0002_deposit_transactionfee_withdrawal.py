# Generated by Django 5.2.4 on 2025-07-07 10:38

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Deposit",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "fee_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("net_amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("PROCESSING", "Processing"),
                            ("COMPLETED", "Completed"),
                            ("FAILED", "Failed"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("reference", models.CharField(max_length=100, unique=True)),
                (
                    "external_reference",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("description", models.TextField(blank=True)),
                ("callback_data", models.JSONField(blank=True, default=dict)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="payments.paymentmethod",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deposits",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deposits",
                        to="payments.wallet",
                    ),
                ),
            ],
            options={
                "db_table": "deposits",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "status"], name="deposits_user_id_a42880_idx"
                    ),
                    models.Index(
                        fields=["reference"], name="deposits_referen_30d081_idx"
                    ),
                    models.Index(
                        fields=["external_reference"],
                        name="deposits_externa_75468b_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="deposits_created_3e8490_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TransactionFee",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[("DEPOSIT", "Deposit"), ("WITHDRAWAL", "Withdrawal")],
                        max_length=20,
                    ),
                ),
                (
                    "fee_type",
                    models.CharField(
                        choices=[
                            ("FIXED", "Fixed Amount"),
                            ("PERCENTAGE", "Percentage"),
                            ("TIERED", "Tiered (Amount Range)"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "min_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "max_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "fee_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "min_fee",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "max_fee",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fees",
                        to="payments.paymentmethod",
                    ),
                ),
            ],
            options={
                "db_table": "transaction_fees",
                "ordering": ["payment_method", "transaction_type", "min_amount"],
                "indexes": [
                    models.Index(
                        fields=["payment_method", "transaction_type", "is_active"],
                        name="transaction_payment_aaf758_idx",
                    ),
                    models.Index(
                        fields=["min_amount", "max_amount"],
                        name="transaction_min_amo_bd8a78_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="Withdrawal",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "fee_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("net_amount", models.DecimalField(decimal_places=2, max_digits=15)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("PROCESSING", "Processing"),
                            ("APPROVED", "Approved"),
                            ("COMPLETED", "Completed"),
                            ("FAILED", "Failed"),
                            ("CANCELLED", "Cancelled"),
                            ("REJECTED", "Rejected"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("reference", models.CharField(max_length=100, unique=True)),
                (
                    "external_reference",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("phone_number", models.CharField(max_length=20)),
                ("description", models.TextField(blank=True)),
                ("callback_data", models.JSONField(blank=True, default=dict)),
                ("approval_required", models.BooleanField(default=False)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_withdrawals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="payments.paymentmethod",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="withdrawals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="withdrawals",
                        to="payments.wallet",
                    ),
                ),
            ],
            options={
                "db_table": "withdrawals",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "status"], name="withdrawals_user_id_38a9fa_idx"
                    ),
                    models.Index(
                        fields=["reference"], name="withdrawals_referen_0dd80e_idx"
                    ),
                    models.Index(
                        fields=["external_reference"],
                        name="withdrawals_externa_df943b_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="withdrawals_created_03b47f_idx"
                    ),
                    models.Index(
                        fields=["status", "approval_required"],
                        name="withdrawals_status_090c3e_idx",
                    ),
                ],
            },
        ),
    ]
