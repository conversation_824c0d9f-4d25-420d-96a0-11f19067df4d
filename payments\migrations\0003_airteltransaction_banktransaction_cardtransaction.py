# Generated by Django 5.2.4 on 2025-07-07 10:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payments", "0002_deposit_transactionfee_withdrawal"),
    ]

    operations = [
        migrations.CreateModel(
            name="AirtelTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "airtel_transaction_id",
                    models.CharField(max_length=100, unique=True),
                ),
                ("reference", models.CharField(max_length=100)),
                ("phone_number", models.Char<PERSON>ield(max_length=15)),
                ("status", models.<PERSON>r<PERSON>ield(blank=True, max_length=50, null=True)),
                (
                    "status_description",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
                ("updated_at", models.DateT<PERSON><PERSON><PERSON>(auto_now=True)),
                (
                    "transaction",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="airtel_details",
                        to="payments.transaction",
                    ),
                ),
            ],
            options={
                "db_table": "payments_airtel_transaction",
                "indexes": [
                    models.Index(
                        fields=["airtel_transaction_id"],
                        name="payments_ai_airtel__1ae0b7_idx",
                    ),
                    models.Index(
                        fields=["reference"], name="payments_ai_referen_7e0efe_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="payments_ai_created_07d276_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BankTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("order_tracking_id", models.CharField(max_length=100, unique=True)),
                ("merchant_reference", models.CharField(max_length=100)),
                ("bank_code", models.CharField(blank=True, max_length=10, null=True)),
                (
                    "account_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "account_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("customer_email", models.EmailField(max_length=254)),
                ("customer_phone", models.CharField(max_length=15)),
                ("redirect_url", models.URLField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "transaction",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bank_details",
                        to="payments.transaction",
                    ),
                ),
            ],
            options={
                "db_table": "payments_bank_transaction",
                "indexes": [
                    models.Index(
                        fields=["order_tracking_id"],
                        name="payments_ba_order_t_5f6da0_idx",
                    ),
                    models.Index(
                        fields=["merchant_reference"],
                        name="payments_ba_merchan_c5a409_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="payments_ba_created_123f6b_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="CardTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("payment_intent_id", models.CharField(max_length=100, unique=True)),
                (
                    "client_secret",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "payment_method_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "card_last_four",
                    models.CharField(blank=True, max_length=4, null=True),
                ),
                ("card_brand", models.CharField(blank=True, max_length=20, null=True)),
                (
                    "customer_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "transaction",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="card_details",
                        to="payments.transaction",
                    ),
                ),
            ],
            options={
                "db_table": "payments_card_transaction",
                "indexes": [
                    models.Index(
                        fields=["payment_intent_id"],
                        name="payments_ca_payment_d57e2e_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="payments_ca_created_ffdb2b_idx"
                    ),
                ],
            },
        ),
    ]
