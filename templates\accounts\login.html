{% extends 'base.html' %}

{% block title %}Login - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Welcome Back</h3>
                <p class="mb-0">Sign in to your ZBet account</p>
            </div>
            
            <div class="card-body">
                <form method="post" id="loginForm">
                    {% csrf_token %}
                    
                    <!-- Username/Email -->
                    <div class="form-floating mb-3">
                        {{ form.username }}
                        <label for="{{ form.username.id_for_label }}">Username or Email</label>
                        {% if form.username.errors %}
                            <div class="text-danger small">{{ form.username.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Password -->
                    <div class="form-floating mb-3">
                        {{ form.password }}
                        <label for="{{ form.password.id_for_label }}">Password</label>
                        {% if form.password.errors %}
                            <div class="text-danger small">{{ form.password.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Remember Me -->
                    <div class="form-check mb-3">
                        {{ form.remember_me }}
                        <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                            {{ form.remember_me.label }}
                        </label>
                    </div>
                    
                    <!-- Non-field errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Sign In
                        </button>
                    </div>
                </form>
                
                <!-- Additional Links -->
                <div class="text-center">
                    <a href="{% url 'accounts:password_reset' %}" class="text-decoration-none small">Forgot your password?</a>
                </div>
                
                <hr>

                <!-- Social Login (Temporarily Disabled) -->
                {% comment %}
                <div class="text-center mb-3">
                    <p class="text-muted">Or sign in with</p>

                    {% load socialaccount %}

                    <div class="d-grid gap-2">
                        <!-- Google Login -->
                        <a href="{% provider_login_url 'google' %}" class="btn btn-outline-danger">
                            <i class="fab fa-google"></i> Continue with Google
                        </a>

                        <!-- Facebook Login -->
                        <a href="{% provider_login_url 'facebook' %}" class="btn btn-outline-primary">
                            <i class="fab fa-facebook-f"></i> Continue with Facebook
                        </a>
                    </div>
                </div>
                {% endcomment %}

                <hr>

                <div class="text-center">
                    <p class="mb-0">Don't have an account?</p>
                    <a href="{% url 'accounts:register' %}" class="btn btn-outline-secondary">
                        Create Account
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Security Notice -->
        <div class="card mt-3">
            <div class="card-body text-center">
                <small class="text-muted">
                    <i class="fas fa-shield-alt"></i>
                    Your account is protected with advanced security measures.
                    Never share your login credentials with anyone.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on username field
    $('#{{ form.username.id_for_label }}').focus();
    
    // Show/hide password functionality
    $('<button type="button" class="btn btn-outline-secondary position-absolute end-0 top-50 translate-middle-y me-2" style="z-index: 10; border: none; background: none;" id="togglePassword"><i class="fas fa-eye"></i></button>')
        .insertAfter('#{{ form.password.id_for_label }}');
    
    $('#togglePassword').on('click', function() {
        const passwordField = $('#{{ form.password.id_for_label }}');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Form validation
    $('#loginForm').on('submit', function(e) {
        const username = $('#{{ form.username.id_for_label }}').val().trim();
        const password = $('#{{ form.password.id_for_label }}').val();
        
        if (!username) {
            e.preventDefault();
            alert('Please enter your username or email.');
            $('#{{ form.username.id_for_label }}').focus();
            return false;
        }
        
        if (!password) {
            e.preventDefault();
            alert('Please enter your password.');
            $('#{{ form.password.id_for_label }}').focus();
            return false;
        }
    });
});
</script>
{% endblock %}
