from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db import transaction
from django.core.paginator import Paginator
from decimal import Decimal
import json

from .models import Sport, League, Team, Match, MatchEvent
from betting.models import Market, Selection, BetType


def is_staff_user(user):
    """Check if user is staff"""
    return user.is_staff


@user_passes_test(is_staff_user)
def match_management_view(request):
    """
    Match management dashboard for staff
    """
    # Get filter parameters
    sport_filter = request.GET.get('sport')
    league_filter = request.GET.get('league')
    status_filter = request.GET.get('status')
    date_filter = request.GET.get('date')

    # Base queryset
    matches = Match.objects.select_related(
        'home_team', 'away_team', 'league', 'league__sport'
    ).order_by('-start_time')

    # Apply filters
    if sport_filter:
        matches = matches.filter(league__sport_id=sport_filter)

    if league_filter:
        matches = matches.filter(league_id=league_filter)

    if status_filter:
        matches = matches.filter(status=status_filter)

    if date_filter:
        from datetime import datetime
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            matches = matches.filter(start_time__date=filter_date)
        except ValueError:
            pass

    # Pagination
    paginator = Paginator(matches, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    sports = Sport.objects.filter(is_active=True)
    leagues = League.objects.filter(is_active=True).select_related('sport')

    context = {
        'page_obj': page_obj,
        'sports': sports,
        'leagues': leagues,
        'filters': {
            'sport': sport_filter,
            'league': league_filter,
            'status': status_filter,
            'date': date_filter,
        },
        'match_status_choices': Match.MATCH_STATUS_CHOICES,
    }

    return render(request, 'sports/match_management.html', context)


@user_passes_test(is_staff_user)
def match_detail_management_view(request, match_id):
    """
    Detailed match management view
    """
    match = get_object_or_404(Match, id=match_id)

    # Get match events
    events = MatchEvent.objects.filter(match=match).order_by('minute', 'created_at')

    # Get betting markets
    markets = Market.objects.filter(match=match).prefetch_related('selections')

    context = {
        'match': match,
        'events': events,
        'markets': markets,
        'event_type_choices': MatchEvent.EVENT_TYPE_CHOICES,
    }

    return render(request, 'sports/match_detail_management.html', context)


@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def update_match_status(request, match_id):
    """
    Update match status via AJAX
    """
    try:
        data = json.loads(request.body)
        new_status = data.get('status')

        match = get_object_or_404(Match, id=match_id)

        # Validate status
        valid_statuses = [choice[0] for choice in Match.MATCH_STATUS_CHOICES]
        if new_status not in valid_statuses:
            return JsonResponse({
                'success': False,
                'message': 'Invalid status.'
            })

        # Update match
        old_status = match.status
        match.status = new_status
        match.save()

        # Handle status-specific logic
        if new_status == 'finished':
            # Disable live betting
            match.live_betting_enabled = False
            match.save()

            # TODO: Settle betting markets

        elif new_status == 'live':
            # Enable live betting if configured
            if match.betting_enabled:
                match.live_betting_enabled = True
                match.save()

        return JsonResponse({
            'success': True,
            'message': f'Match status updated from {old_status} to {new_status}.',
            'new_status': new_status
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def update_match_score(request, match_id):
    """
    Update match score via AJAX
    """
    try:
        data = json.loads(request.body)
        home_score = data.get('home_score')
        away_score = data.get('away_score')
        minute = data.get('minute')

        match = get_object_or_404(Match, id=match_id)

        # Validate scores
        if home_score is not None:
            match.home_score = max(0, int(home_score))

        if away_score is not None:
            match.away_score = max(0, int(away_score))

        if minute is not None:
            match.minute = max(0, int(minute))

        match.save()

        return JsonResponse({
            'success': True,
            'message': 'Match score updated.',
            'home_score': match.home_score,
            'away_score': match.away_score,
            'minute': match.minute
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def add_match_event(request, match_id):
    """
    Add match event via AJAX
    """
    try:
        data = json.loads(request.body)
        event_type = data.get('event_type')
        team_id = data.get('team_id')
        minute = data.get('minute')
        player_name = data.get('player_name', '')
        description = data.get('description', '')

        match = get_object_or_404(Match, id=match_id)
        team = get_object_or_404(Team, id=team_id)

        # Validate event type
        valid_types = [choice[0] for choice in MatchEvent.EVENT_TYPE_CHOICES]
        if event_type not in valid_types:
            return JsonResponse({
                'success': False,
                'message': 'Invalid event type.'
            })

        # Create event
        event = MatchEvent.objects.create(
            match=match,
            team=team,
            event_type=event_type,
            minute=int(minute),
            player_name=player_name,
            description=description
        )

        return JsonResponse({
            'success': True,
            'message': 'Event added successfully.',
            'event': {
                'id': event.id,
                'type': event.get_event_type_display(),
                'team': team.name,
                'minute': event.minute,
                'player': event.player_name,
                'description': event.description
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


@user_passes_test(is_staff_user)
def create_match_markets(request, match_id):
    """
    Create betting markets for a match
    """
    match = get_object_or_404(Match, id=match_id)

    if request.method == 'POST':
        try:
            with transaction.atomic():
                # Get available bet types for this sport
                bet_types = BetType.objects.filter(
                    sports=match.league.sport,
                    is_active=True
                )

                markets_created = 0

                for bet_type in bet_types:
                    # Check if market already exists
                    if Market.objects.filter(match=match, bet_type=bet_type).exists():
                        continue

                    # Create market
                    market = Market.objects.create(
                        match=match,
                        bet_type=bet_type,
                        name=f"{bet_type.name}",
                        status='active'
                    )

                    # Create default selections based on bet type
                    if bet_type.slug == 'match-winner':
                        # Create home, draw, away selections
                        Selection.objects.create(
                            market=market,
                            name=match.home_team.name,
                            decimal_odds=Decimal('2.00')  # Default odds
                        )

                        if match.league.sport.name.lower() in ['football', 'soccer']:
                            Selection.objects.create(
                                market=market,
                                name='Draw',
                                decimal_odds=Decimal('3.00')
                            )

                        Selection.objects.create(
                            market=market,
                            name=match.away_team.name,
                            decimal_odds=Decimal('2.00')
                        )

                    elif bet_type.slug == 'over-under':
                        # Create over/under selections
                        Selection.objects.create(
                            market=market,
                            name='Over 2.5',
                            decimal_odds=Decimal('1.90')
                        )

                        Selection.objects.create(
                            market=market,
                            name='Under 2.5',
                            decimal_odds=Decimal('1.90')
                        )

                    markets_created += 1

                messages.success(request, f'{markets_created} markets created successfully.')

        except Exception as e:
            messages.error(request, f'Error creating markets: {str(e)}')

    return redirect('sports:match_detail_management', match_id=match.id)


@user_passes_test(is_staff_user)
@require_http_methods(["POST"])
def bulk_update_matches(request):
    """
    Bulk update multiple matches
    """
    try:
        data = json.loads(request.body)
        match_ids = data.get('match_ids', [])
        action = data.get('action')

        if not match_ids:
            return JsonResponse({
                'success': False,
                'message': 'No matches selected.'
            })

        matches = Match.objects.filter(id__in=match_ids)
        count = 0

        if action == 'enable_betting':
            count = matches.update(betting_enabled=True)
            message = f'{count} matches enabled for betting.'

        elif action == 'disable_betting':
            count = matches.update(betting_enabled=False)
            message = f'{count} matches disabled for betting.'

        elif action == 'enable_live_betting':
            count = matches.update(live_betting_enabled=True)
            message = f'{count} matches enabled for live betting.'

        elif action == 'disable_live_betting':
            count = matches.update(live_betting_enabled=False)
            message = f'{count} matches disabled for live betting.'

        elif action == 'mark_finished':
            count = matches.filter(status__in=['live', 'halftime']).update(
                status='finished',
                live_betting_enabled=False
            )
            message = f'{count} matches marked as finished.'

        else:
            return JsonResponse({
                'success': False,
                'message': 'Invalid action.'
            })

        return JsonResponse({
            'success': True,
            'message': message
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })


def match_statistics_view(request):
    """
    Display match statistics and analytics
    """
    from django.db.models import Count, Q

    # Get statistics
    total_matches = Match.objects.count()
    live_matches = Match.objects.filter(status__in=['live', 'halftime']).count()
    upcoming_matches = Match.objects.filter(
        status='scheduled',
        start_time__gte=timezone.now()
    ).count()

    # Matches by sport
    matches_by_sport = Match.objects.values(
        'league__sport__name'
    ).annotate(
        count=Count('id')
    ).order_by('-count')

    # Recent matches
    recent_matches = Match.objects.filter(
        status='finished'
    ).select_related(
        'home_team', 'away_team', 'league'
    ).order_by('-start_time')[:10]

    context = {
        'stats': {
            'total_matches': total_matches,
            'live_matches': live_matches,
            'upcoming_matches': upcoming_matches,
        },
        'matches_by_sport': matches_by_sport,
        'recent_matches': recent_matches,
    }

    return render(request, 'sports/match_statistics.html', context)
