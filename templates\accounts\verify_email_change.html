{% extends 'base.html' %}

{% block title %}Verify New Email - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Verify New Email</h3>
                <p class="mb-0">Complete your email change</p>
            </div>
            
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="fas fa-envelope-open fa-3x text-primary mb-3"></i>
                    <h5>Almost Done!</h5>
                    <p class="text-muted">
                        We've sent a verification code to your new email address:<br>
                        <strong>{{ new_email }}</strong>
                    </p>
                </div>
                
                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-info-circle"></i>
                        Your email will be changed from <strong>{{ user.email }}</strong> to <strong>{{ new_email }}</strong> after verification.
                    </small>
                </div>
                
                <form method="post" id="verifyChangeForm">
                    {% csrf_token %}
                    
                    <div class="form-floating mb-3">
                        {{ form.verification_code }}
                        <label for="{{ form.verification_code.id_for_label }}">Verification Code</label>
                        <div class="form-text">{{ form.verification_code.help_text }}</div>
                        {% if form.verification_code.errors %}
                            <div class="text-danger small">{{ form.verification_code.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Complete Email Change
                        </button>
                    </div>
                </form>
                
                <div class="text-center">
                    <p class="text-muted">Didn't receive the code?</p>
                    <a href="{% url 'accounts:change_email' %}" class="btn btn-outline-secondary">
                        Try Again
                    </a>
                </div>
                
                <div class="mt-4">
                    <small class="text-muted">
                        The verification code will expire in 10 minutes.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on verification code input
    $('#{{ form.verification_code.id_for_label }}').focus();
    
    // Format verification code input
    $('#{{ form.verification_code.id_for_label }}').on('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
        if (this.value.length === 6) {
            $('#verifyChangeForm').submit();
        }
    });
});
</script>
{% endblock %}
