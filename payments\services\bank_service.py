"""
Bank Transfer Service
Handles bank transfer payments and withdrawals for Kenyan banks
"""

import requests
import json
import hashlib
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from decimal import Decimal
import logging
import uuid

logger = logging.getLogger(__name__)


class BankTransferService:
    """Service for handling bank transfers"""
    
    def __init__(self):
        self.environment = getattr(settings, 'BANK_TRANSFER_ENVIRONMENT', 'sandbox')
        self.api_key = getattr(settings, 'BANK_TRANSFER_API_KEY', '')
        self.secret_key = getattr(settings, 'BANK_TRANSFER_SECRET_KEY', '')
        self.merchant_account = getattr(settings, 'BANK_TRANSFER_MERCHANT_ACCOUNT', '')
        
        if self.environment == 'production':
            self.base_url = 'https://api.pesapal.com/v3'  # Example: PesaPal production
        else:
            self.base_url = 'https://cybqa.pesapal.com/pesapalv3'  # PesaPal sandbox
    
    def get_access_token(self):
        """Get OAuth access token for bank transfer API"""
        cache_key = 'bank_transfer_access_token'
        token = cache.get(cache_key)
        
        if token:
            return token
        
        try:
            url = f"{self.base_url}/api/Auth/RequestToken"
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            payload = {
                "consumer_key": self.api_key,
                "consumer_secret": self.secret_key
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                access_token = result.get('token')
                expires_in = result.get('expiryDate')  # PesaPal returns expiry date
                
                # Cache token for 50 minutes (tokens usually last 1 hour)
                cache.set(cache_key, access_token, 3000)
                
                return access_token
            else:
                logger.error(f"Failed to get bank transfer access token: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting bank transfer access token: {str(e)}")
            return None
    
    def get_supported_banks(self):
        """Get list of supported banks"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            url = f"{self.base_url}/api/URLSetup/GetIpnList"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'banks': result
                }
            else:
                logger.error(f"Failed to get supported banks: {response.text}")
                return {
                    'success': False,
                    'error': 'Failed to retrieve supported banks'
                }
                
        except Exception as e:
            logger.error(f"Error getting supported banks: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def initiate_bank_deposit(self, amount, customer_email, customer_phone, reference, description="Bank Deposit"):
        """Initiate bank deposit payment"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            url = f"{self.base_url}/api/Transactions/SubmitOrderRequest"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "id": reference,
                "currency": "KES",
                "amount": float(amount),
                "description": description,
                "callback_url": f"{getattr(settings, 'SITE_URL', 'http://127.0.0.1:8000')}/payments/bank/callback/",
                "notification_id": reference,
                "billing_address": {
                    "email_address": customer_email,
                    "phone_number": customer_phone,
                    "country_code": "KE",
                    "first_name": "Customer",
                    "last_name": "ZBet"
                }
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('status') == '200':
                    return {
                        'success': True,
                        'order_tracking_id': result.get('order_tracking_id'),
                        'merchant_reference': result.get('merchant_reference'),
                        'redirect_url': result.get('redirect_url'),
                        'message': 'Bank deposit initiated successfully'
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('description', 'Bank deposit initiation failed')
                    }
            else:
                logger.error(f"Bank deposit request failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Bank deposit request failed'
                }
                
        except Exception as e:
            logger.error(f"Error initiating bank deposit: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def query_transaction_status(self, order_tracking_id):
        """Query bank transfer transaction status"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            url = f"{self.base_url}/api/Transactions/GetTransactionStatus"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "orderTrackingId": order_tracking_id
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'data': result
                }
            else:
                logger.error(f"Bank transaction status query failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Status query failed'
                }
                
        except Exception as e:
            logger.error(f"Error querying bank transaction status: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def initiate_bank_withdrawal(self, bank_code, account_number, account_name, amount, reference, description="Bank Withdrawal"):
        """Initiate bank withdrawal (transfer to customer account)"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            # This would typically use a different API endpoint for disbursements
            # Implementation depends on the specific bank transfer provider
            
            url = f"{self.base_url}/api/Disbursements/InitiateTransfer"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "reference": reference,
                "amount": float(amount),
                "currency": "KES",
                "destination": {
                    "bank_code": bank_code,
                    "account_number": account_number,
                    "account_name": account_name
                },
                "description": description,
                "callback_url": f"{getattr(settings, 'SITE_URL', 'http://127.0.0.1:8000')}/payments/bank/withdrawal-callback/"
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('status') == 'success':
                    return {
                        'success': True,
                        'transaction_id': result.get('transaction_id'),
                        'reference': reference,
                        'status': result.get('status'),
                        'message': 'Bank withdrawal initiated successfully'
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('message', 'Bank withdrawal initiation failed')
                    }
            else:
                logger.error(f"Bank withdrawal request failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Bank withdrawal request failed'
                }
                
        except Exception as e:
            logger.error(f"Error initiating bank withdrawal: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_bank_account(self, bank_code, account_number):
        """Validate bank account details"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}
            
            # This would use a bank account validation API
            # Implementation depends on the specific provider
            
            url = f"{self.base_url}/api/Validation/ValidateAccount"
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "bank_code": bank_code,
                "account_number": account_number
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('valid'):
                    return {
                        'success': True,
                        'valid': True,
                        'account_name': result.get('account_name'),
                        'bank_name': result.get('bank_name')
                    }
                else:
                    return {
                        'success': True,
                        'valid': False,
                        'error': result.get('message', 'Invalid bank account')
                    }
            else:
                logger.error(f"Bank account validation failed: {response.text}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: Account validation failed'
                }
                
        except Exception as e:
            logger.error(f"Error validating bank account: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def process_callback(self, callback_data):
        """Process bank transfer callback data"""
        try:
            # Extract relevant information from callback
            order_tracking_id = callback_data.get('OrderTrackingId')
            merchant_reference = callback_data.get('OrderMerchantReference')
            status = callback_data.get('OrderStatus')
            amount = callback_data.get('OrderAmount')
            currency = callback_data.get('OrderCurrency')
            
            return {
                'success': True,
                'order_tracking_id': order_tracking_id,
                'merchant_reference': merchant_reference,
                'status': status,
                'amount': amount,
                'currency': currency,
                'callback_data': callback_data
            }
            
        except Exception as e:
            logger.error(f"Error processing bank transfer callback: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


# Create a singleton instance
bank_service = BankTransferService()
