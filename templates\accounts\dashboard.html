{% extends 'base.html' %}

{% block title %}Dashboard - ZBet{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card">
                <div class="card-header zbet-primary text-white">
                    <h6 class="mb-0">Account Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'accounts:profile_update' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user"></i> Profile
                    </a>
                    <a href="{% url 'accounts:kyc_verification' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-id-card"></i> KYC Verification
                    </a>
                    <a href="{% url 'accounts:change_email' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-at"></i> Change Email
                    </a>
                    <a href="{% url 'accounts:change_phone' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-mobile-alt"></i> Change Phone
                    </a>
                    <a href="{% url 'accounts:two_factor_settings' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> Two-Factor Authentication
                    </a>
                    <a href="{% url 'accounts:social_connections' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-link"></i> Social Connections
                    </a>
                    <a href="{% url 'accounts:betting_history' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-history"></i> Betting History
                    </a>
                    <a href="{% url 'accounts:account_balance' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-wallet"></i> Wallet
                    </a>
                    <a href="{% url 'accounts:notification_preferences' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                    <a href="{% url 'accounts:responsible_gambling' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> Responsible Gambling
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Welcome Section -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    {% if profile.avatar %}
                                        <img src="{{ profile.avatar.url }}" alt="Profile" class="rounded-circle" width="60" height="60">
                                    {% else %}
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                            <i class="fas fa-user text-white fa-2x"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div>
                                    <h4 class="mb-1">Welcome back, {{ user.first_name }}!</h4>
                                    <p class="text-muted mb-0">{{ user.email }}</p>
                                    <small class="text-muted">Last login: {{ user.last_login|date:"M d, Y H:i" }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Profile Completion</h6>
                            <div class="progress mb-2" style="height: 10px;">
                                <div class="progress-bar bg-{% if completion_score >= 80 %}success{% elif completion_score >= 50 %}warning{% else %}danger{% endif %}" 
                                     style="width: {{ completion_score }}%"></div>
                            </div>
                            <span class="h5">{{ completion_score }}%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts Section -->
            {% if not security_status.email_verified %}
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <h6><i class="fas fa-exclamation-triangle"></i> Email Not Verified</h6>
                <p class="mb-2">Your email address is not verified. Please verify your email to access all features.</p>
                <a href="{% url 'accounts:request_email_verification' %}" class="btn btn-warning btn-sm">
                    Verify Email Now
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endif %}

            {% if not security_status.phone_verified %}
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <h6><i class="fas fa-mobile-alt"></i> Phone Not Verified</h6>
                <p class="mb-2">Your phone number is not verified. Verify your phone to enable M-Pesa transactions and SMS notifications.</p>
                <a href="{% url 'accounts:request_sms_verification' %}" class="btn btn-info btn-sm">
                    Verify Phone Now
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endif %}

            {% if not security_status.two_factor_enabled %}
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <h6><i class="fas fa-shield-alt"></i> Two-Factor Authentication Disabled</h6>
                <p class="mb-2">Enable 2FA to add an extra layer of security to your account and protect your funds.</p>
                <a href="{% url 'accounts:enable_2fa' %}" class="btn btn-warning btn-sm">
                    Enable 2FA Now
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endif %}

            <!-- Quick Actions -->
            {% if quick_actions %}
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for action in quick_actions %}
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card border-{{ action.color }} h-100">
                                <div class="card-body text-center">
                                    <i class="{{ action.icon }} fa-2x text-{{ action.color }} mb-2"></i>
                                    <h6 class="card-title">{{ action.title }}</h6>
                                    <p class="card-text small">{{ action.description }}</p>
                                    <a href="{% url action.url %}" class="btn btn-{{ action.color }} btn-sm">
                                        Take Action
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Account Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center bg-primary text-white">
                        <div class="card-body">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <h4 class="card-title">{{ account_stats.total_bets }}</h4>
                            <p class="card-text">Total Bets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-success text-white">
                        <div class="card-body">
                            <i class="fas fa-trophy fa-2x mb-2"></i>
                            <h4 class="card-title">{{ account_stats.total_wins }}</h4>
                            <p class="card-text">Total Wins</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-info text-white">
                        <div class="card-body">
                            <i class="fas fa-percentage fa-2x mb-2"></i>
                            <h4 class="card-title">{{ account_stats.win_rate }}%</h4>
                            <p class="card-text">Win Rate</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-warning text-white">
                        <div class="card-body">
                            <i class="fas fa-wallet fa-2x mb-2"></i>
                            <h4 class="card-title">KSh {{ account_stats.net_position|floatformat:0 }}</h4>
                            <p class="card-text">Net Position</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity and Security Status -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-clock"></i> Recent Activity</h6>
                        </div>
                        <div class="card-body">
                            {% if recent_activities %}
                                {% for activity in recent_activities %}
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <i class="{{ activity.icon }} text-{{ activity.color }}"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ activity.description }}</h6>
                                        <small class="text-muted">{{ activity.timestamp|timesince }} ago</small>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted">No recent activity to display.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-shield-alt"></i> Security Status</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">Email Verification</small>
                                <div class="float-end">
                                    {% if security_status.email_verified %}
                                        <span class="badge bg-success">Verified</span>
                                    {% else %}
                                        <span class="badge bg-danger">Not Verified</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Phone Verification</small>
                                <div class="float-end">
                                    {% if security_status.phone_verified %}
                                        <span class="badge bg-success">Verified</span>
                                    {% else %}
                                        <span class="badge bg-danger">Not Verified</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Two-Factor Auth</small>
                                <div class="float-end">
                                    {% if security_status.two_factor_enabled %}
                                        <span class="badge bg-success">Enabled</span>
                                    {% else %}
                                        <span class="badge bg-warning">Disabled</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">KYC Status</small>
                                <div class="float-end">
                                    {% if security_status.kyc_verified %}
                                        <span class="badge bg-success">Verified</span>
                                    {% else %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Social Accounts</small>
                                <div class="float-end">
                                    <span class="badge bg-info">{{ security_status.social_accounts_connected }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
