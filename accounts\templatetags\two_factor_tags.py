"""
Template tags for Two-Factor Authentication
"""

from django import template
from ..two_factor_service import two_factor_service

register = template.Library()


@register.filter
def is_2fa_enabled(user):
    """Check if 2FA is enabled for user"""
    return two_factor_service.is_2fa_enabled(user)


@register.filter
def get_2fa_status(user):
    """Get 2FA status for user"""
    return two_factor_service.get_2fa_status(user)


@register.filter
def get_2fa_methods(user):
    """Get available 2FA methods for user"""
    return two_factor_service.get_2fa_methods(user)


@register.simple_tag
def backup_codes_remaining(user):
    """Get number of remaining backup codes"""
    backup_info = two_factor_service.get_backup_codes_info(user)
    return backup_info['remaining'] if backup_info else 0
