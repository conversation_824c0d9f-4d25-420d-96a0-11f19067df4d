<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ZBet - Sports Betting Platform{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .zbet-primary { background-color: #1e40af; }
        .zbet-secondary { background-color: #f59e0b; }
        .verification-code-input {
            letter-spacing: 0.5em;
            font-size: 1.5em;
            text-align: center;
        }
        .form-floating > label {
            color: #6b7280;
        }
        .btn-zbet {
            background-color: #1e40af;
            border-color: #1e40af;
            color: white;
        }
        .btn-zbet:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
            color: white;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .footer {
            background-color: #1f2937;
            color: white;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body style="background-color: #0f1419; color: #ffffff;">
    <!-- Top Header -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #1a1a2e; border-bottom: 1px solid #2d3748;">
        <div class="container-fluid">
            <a class="navbar-brand" href="/" style="color: #ffc107; font-weight: bold; font-size: 1.8rem;">
                <strong>ZBet</strong>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'accounts:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'accounts:logout' %}">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link btn btn-outline-light btn-sm me-2" href="{% url 'accounts:login' %}">
                                Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-warning btn-sm" href="{% url 'accounts:register' %}" style="color: #000;">
                                Register
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>ZBet</h5>
                    <p class="mb-0">Your trusted sports betting platform</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 ZBet. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
