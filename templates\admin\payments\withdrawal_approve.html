{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label='payments' %}">Payments</a>
&rsaquo; <a href="{% url 'admin:payments_withdrawal_changelist' %}">Withdrawals</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>{{ title }}</h1>
    
    <div class="form-row">
        <div class="field-box">
            <h2>Withdrawal Details</h2>
            <table>
                <tr>
                    <th>Reference:</th>
                    <td>{{ withdrawal.reference }}</td>
                </tr>
                <tr>
                    <th>User:</th>
                    <td>{{ withdrawal.user.username }} ({{ withdrawal.user.email }})</td>
                </tr>
                <tr>
                    <th>Amount:</th>
                    <td>KES {{ withdrawal.amount|floatformat:2 }}</td>
                </tr>
                <tr>
                    <th>Fee:</th>
                    <td>KES {{ withdrawal.fee_amount|floatformat:2 }}</td>
                </tr>
                <tr>
                    <th>Net Amount:</th>
                    <td>KES {{ withdrawal.net_amount|floatformat:2 }}</td>
                </tr>
                <tr>
                    <th>Payment Method:</th>
                    <td>{{ withdrawal.payment_method.name }}</td>
                </tr>
                <tr>
                    <th>Phone Number:</th>
                    <td>{{ withdrawal.phone_number }}</td>
                </tr>
                <tr>
                    <th>Status:</th>
                    <td>{{ withdrawal.get_status_display }}</td>
                </tr>
                <tr>
                    <th>Created:</th>
                    <td>{{ withdrawal.created_at }}</td>
                </tr>
                <tr>
                    <th>Description:</th>
                    <td>{{ withdrawal.description|default:"N/A" }}</td>
                </tr>
            </table>
        </div>
    </div>

    {% if withdrawal.can_be_approved %}
    <form method="post" class="module">
        {% csrf_token %}
        <div class="form-row">
            <div class="field-box">
                <label for="notes">Admin Notes (Optional):</label>
                <textarea name="notes" id="notes" rows="4" cols="80" placeholder="Add any notes about this approval..."></textarea>
            </div>
        </div>
        
        <div class="submit-row">
            <input type="submit" value="Approve Withdrawal" class="default" />
            <a href="{% url 'admin:payments_withdrawal_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
    {% else %}
    <div class="form-row">
        <p class="errornote">This withdrawal cannot be approved in its current state.</p>
        <a href="{% url 'admin:payments_withdrawal_changelist' %}" class="button">Back to Withdrawals</a>
    </div>
    {% endif %}
</div>

<style>
.field-box table {
    width: 100%;
    border-collapse: collapse;
}
.field-box table th,
.field-box table td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}
.field-box table th {
    background-color: #f8f8f8;
    font-weight: bold;
    width: 150px;
}
</style>
{% endblock %}
