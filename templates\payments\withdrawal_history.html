{% extends 'base.html' %}
{% load static %}

{% block title %}Withdrawal History - ZBet{% endblock %}

{% block extra_css %}
<style>
    .history-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .history-header {
        background: linear-gradient(135deg, #1a1a2e, #2d3748);
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .history-title {
        color: white;
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .history-subtitle {
        color: #a0aec0;
        font-size: 16px;
    }
    
    .filters-section {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 30px;
    }
    
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    
    .filter-label {
        color: #a0aec0;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
    }
    
    .filter-input {
        background: #2d3748;
        border: 1px solid #4a5568;
        border-radius: 8px;
        padding: 10px 12px;
        color: white;
        font-size: 14px;
    }
    
    .filter-input:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
    
    .filter-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    
    .filter-btn {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .filter-btn:hover {
        background: linear-gradient(135deg, #3182ce, #2c5282);
    }
    
    .filter-btn.secondary {
        background: #4a5568;
    }
    
    .filter-btn.secondary:hover {
        background: #2d3748;
    }
    
    .history-table {
        background: #1a1a2e;
        border: 1px solid #2d3748;
        border-radius: 12px;
        overflow: hidden;
    }
    
    .table-header {
        background: #2d3748;
        padding: 20px 25px;
        border-bottom: 1px solid #4a5568;
    }
    
    .table-title {
        color: white;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .table-subtitle {
        color: #a0aec0;
        font-size: 14px;
    }
    
    .table-content {
        overflow-x: auto;
    }
    
    .history-table table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .history-table th {
        background: #2d3748;
        color: #a0aec0;
        font-weight: 600;
        font-size: 12px;
        text-transform: uppercase;
        padding: 15px 20px;
        text-align: left;
        border-bottom: 1px solid #4a5568;
    }
    
    .history-table td {
        padding: 20px;
        border-bottom: 1px solid #2d3748;
        color: white;
    }
    
    .history-table tr:hover {
        background: rgba(66, 153, 225, 0.05);
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-completed {
        background: rgba(72, 187, 120, 0.2);
        color: #48bb78;
    }
    
    .status-pending {
        background: rgba(237, 137, 54, 0.2);
        color: #ed8936;
    }
    
    .status-failed {
        background: rgba(245, 101, 101, 0.2);
        color: #f56565;
    }
    
    .status-approved {
        background: rgba(56, 178, 172, 0.2);
        color: #38b2ac;
    }
    
    .status-rejected {
        background: rgba(245, 101, 101, 0.2);
        color: #f56565;
    }
    
    .amount-negative {
        color: #f56565;
        font-weight: 600;
    }
    
    .reference-code {
        font-family: 'Courier New', monospace;
        background: #2d3748;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: #4299e1;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .action-btn {
        background: #4299e1;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .action-btn:hover {
        background: #3182ce;
        color: white;
        text-decoration: none;
    }
    
    .action-btn.secondary {
        background: #48bb78;
    }
    
    .action-btn.secondary:hover {
        background: #38a169;
    }
    
    .action-btn.danger {
        background: #f56565;
    }
    
    .action-btn.danger:hover {
        background: #e53e3e;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 30px;
    }
    
    .pagination-btn {
        background: #2d3748;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 15px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .pagination-btn:hover {
        background: #4299e1;
    }
    
    .pagination-btn.active {
        background: #4299e1;
    }
    
    .pagination-btn:disabled {
        background: #1a1a2e;
        color: #4a5568;
        cursor: not-allowed;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #a0aec0;
    }
    
    .empty-icon {
        font-size: 64px;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .empty-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
        color: white;
    }
    
    .empty-message {
        font-size: 16px;
        margin-bottom: 30px;
    }
    
    .empty-action {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .empty-action:hover {
        background: linear-gradient(135deg, #3182ce, #2c5282);
        color: white;
        text-decoration: none;
    }
    
    @media (max-width: 768px) {
        .filters-grid {
            grid-template-columns: 1fr;
        }
        
        .filter-buttons {
            justify-content: stretch;
        }
        
        .filter-btn {
            flex: 1;
        }
        
        .table-content {
            font-size: 14px;
        }
        
        .history-table th,
        .history-table td {
            padding: 10px 15px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="history-container">
    <!-- Header -->
    <div class="history-header">
        <h1 class="history-title">Withdrawal History</h1>
        <p class="history-subtitle">Track all your withdrawal requests and their approval status</p>
    </div>
    
    <!-- Filters -->
    <div class="filters-section">
        <form method="GET" id="filter-form">
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Date From</label>
                    <input type="date" name="date_from" class="filter-input" value="{{ request.GET.date_from }}">
                </div>
                <div class="filter-group">
                    <label class="filter-label">Date To</label>
                    <input type="date" name="date_to" class="filter-input" value="{{ request.GET.date_to }}">
                </div>
                <div class="filter-group">
                    <label class="filter-label">Status</label>
                    <select name="status" class="filter-input">
                        <option value="">All Statuses</option>
                        <option value="PENDING" {% if request.GET.status == 'PENDING' %}selected{% endif %}>Pending</option>
                        <option value="APPROVED" {% if request.GET.status == 'APPROVED' %}selected{% endif %}>Approved</option>
                        <option value="COMPLETED" {% if request.GET.status == 'COMPLETED' %}selected{% endif %}>Completed</option>
                        <option value="REJECTED" {% if request.GET.status == 'REJECTED' %}selected{% endif %}>Rejected</option>
                        <option value="FAILED" {% if request.GET.status == 'FAILED' %}selected{% endif %}>Failed</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Payment Method</label>
                    <select name="payment_method" class="filter-input">
                        <option value="">All Methods</option>
                        {% for method in payment_methods %}
                        <option value="{{ method.code }}" {% if request.GET.payment_method == method.code %}selected{% endif %}>{{ method.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Search Reference</label>
                    <input type="text" name="search" class="filter-input" placeholder="Transaction ID or M-Pesa code" value="{{ request.GET.search }}">
                </div>
            </div>
            <div class="filter-buttons">
                <button type="button" class="filter-btn secondary" onclick="clearFilters()">Clear</button>
                <button type="submit" class="filter-btn">Apply Filters</button>
            </div>
        </form>
    </div>

    <!-- History Table -->
    <div class="history-table">
        <div class="table-header">
            <h2 class="table-title">Withdrawal Transactions</h2>
            <p class="table-subtitle">{{ withdrawals.count }} total withdrawals found</p>
        </div>

        {% if withdrawals %}
        <div class="table-content">
            <table>
                <thead>
                    <tr>
                        <th>Date & Time</th>
                        <th>Amount</th>
                        <th>Payment Method</th>
                        <th>Reference</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for withdrawal in withdrawals %}
                    <tr>
                        <td>
                            <div>{{ withdrawal.created_at|date:"M d, Y" }}</div>
                            <div style="font-size: 12px; color: #a0aec0;">{{ withdrawal.created_at|time:"H:i" }}</div>
                        </td>
                        <td>
                            <span class="amount-negative">-KES {{ withdrawal.amount|floatformat:2 }}</span>
                        </td>
                        <td>
                            <div>{{ withdrawal.payment_method.name }}</div>
                            {% if withdrawal.payment_method.code == 'MPESA' %}
                            <div style="font-size: 12px; color: #a0aec0;">{{ withdrawal.phone_number }}</div>
                            {% endif %}
                        </td>
                        <td>
                            {% if withdrawal.external_reference %}
                            <span class="reference-code">{{ withdrawal.external_reference }}</span>
                            {% else %}
                            <span style="color: #a0aec0;">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge status-{{ withdrawal.status|lower }}">
                                {{ withdrawal.get_status_display }}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                {% if withdrawal.status == 'COMPLETED' %}
                                <a href="{% url 'payments:download_withdrawal_receipt' withdrawal.id %}" class="action-btn">
                                    📄 Receipt
                                </a>
                                {% endif %}
                                {% if withdrawal.status == 'PENDING' %}
                                <button class="action-btn danger" onclick="cancelWithdrawal('{{ withdrawal.id }}')">
                                    ❌ Cancel
                                </button>
                                {% endif %}
                                <button class="action-btn secondary" onclick="viewDetails('{{ withdrawal.id }}')">
                                    👁️ Details
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="pagination">
            {% if page_obj.has_previous %}
                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1" class="pagination-btn">First</a>
                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" class="pagination-btn">Previous</a>
            {% endif %}

            <span class="pagination-info" style="color: #a0aec0; margin: 0 15px;">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" class="pagination-btn">Next</a>
                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}" class="pagination-btn">Last</a>
            {% endif %}
        </div>
        {% endif %}

        {% else %}
        <div class="empty-state">
            <div class="empty-icon">💸</div>
            <h3 class="empty-title">No Withdrawals Found</h3>
            <p class="empty-message">
                {% if request.GET %}
                No withdrawals match your current filters. Try adjusting your search criteria.
                {% else %}
                You haven't made any withdrawal requests yet.
                {% endif %}
            </p>
            {% if not request.GET %}
            <a href="{% url 'payments:withdrawal' %}" class="empty-action">Request Withdrawal</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Details Modal -->
<div id="details-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Transaction Details</h3>
            <button onclick="closeDetailsModal()" class="text-gray-400 hover:text-white">
                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
            </button>
        </div>
        <div id="details-content">
            <!-- Details will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearFilters() {
    // Clear all form inputs
    document.getElementById('filter-form').reset();
    // Redirect to clean URL
    window.location.href = window.location.pathname;
}

function viewDetails(withdrawalId) {
    // Show modal
    document.getElementById('details-modal').classList.remove('hidden');
    document.getElementById('details-modal').classList.add('flex');

    // Load details via AJAX
    fetch(`/payments/withdrawal/${withdrawalId}/details/`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            document.getElementById('details-content').innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm text-gray-400">Transaction ID</label>
                            <div class="text-white font-mono text-sm">${data.withdrawal.id}</div>
                        </div>
                        <div>
                            <label class="text-sm text-gray-400">Amount</label>
                            <div class="text-red-400 font-bold">KES ${data.withdrawal.amount}</div>
                        </div>
                        <div>
                            <label class="text-sm text-gray-400">Status</label>
                            <div class="text-white">${data.withdrawal.status}</div>
                        </div>
                        <div>
                            <label class="text-sm text-gray-400">Payment Method</label>
                            <div class="text-white">${data.withdrawal.payment_method}</div>
                        </div>
                        <div>
                            <label class="text-sm text-gray-400">Created</label>
                            <div class="text-white">${data.withdrawal.created_at}</div>
                        </div>
                        <div>
                            <label class="text-sm text-gray-400">Updated</label>
                            <div class="text-white">${data.withdrawal.updated_at}</div>
                        </div>
                    </div>
                    ${data.withdrawal.external_reference ? `
                    <div>
                        <label class="text-sm text-gray-400">External Reference</label>
                        <div class="text-blue-400 font-mono text-sm">${data.withdrawal.external_reference}</div>
                    </div>
                    ` : ''}
                    ${data.withdrawal.phone_number ? `
                    <div>
                        <label class="text-sm text-gray-400">Phone Number</label>
                        <div class="text-white">${data.withdrawal.phone_number}</div>
                    </div>
                    ` : ''}
                    ${data.withdrawal.admin_notes ? `
                    <div>
                        <label class="text-sm text-gray-400">Admin Notes</label>
                        <div class="text-white">${data.withdrawal.admin_notes}</div>
                    </div>
                    ` : ''}
                    ${data.withdrawal.rejection_reason ? `
                    <div>
                        <label class="text-sm text-gray-400">Rejection Reason</label>
                        <div class="text-red-400">${data.withdrawal.rejection_reason}</div>
                    </div>
                    ` : ''}
                </div>
            `;
        } else {
            document.getElementById('details-content').innerHTML = `
                <div class="text-red-400">Error loading details: ${data.message}</div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('details-content').innerHTML = `
            <div class="text-red-400">Error loading details</div>
        `;
    });
}

function cancelWithdrawal(withdrawalId) {
    if (confirm('Are you sure you want to cancel this withdrawal request?')) {
        fetch(`/payments/withdrawal/${withdrawalId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Withdrawal cancelled successfully');
                location.reload();
            } else {
                alert('Error cancelling withdrawal: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error cancelling withdrawal');
        });
    }
}

function closeDetailsModal() {
    document.getElementById('details-modal').classList.add('hidden');
    document.getElementById('details-modal').classList.remove('flex');
}

// Close modal when clicking outside
document.getElementById('details-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDetailsModal();
    }
});
</script>
{% endblock %}
