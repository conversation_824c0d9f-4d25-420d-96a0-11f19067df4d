from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Deposit and withdrawal
    path('deposit/', views.initiate_deposit, name='deposit'),
    path('withdrawal/', views.withdrawal_request, name='withdrawal'),

    # Transaction status and monitoring
    path('transaction/status/<str:transaction_id>/', views.transaction_status, name='transaction_status'),
    path('stk/status/<str:transaction_id>/', views.check_stk_status, name='check_stk_status'),

    # M-Pesa integration
    path('mpesa/callback/', views.mpesa_callback, name='mpesa_callback'),
    path('mpesa/b2c-result/', views.mpesa_b2c_result, name='mpesa_b2c_result'),
    path('mpesa/b2c-timeout/', views.mpesa_b2c_timeout, name='mpesa_b2c_timeout'),
    path('mpesa/reconciliation-result/', views.mpesa_reconciliation_result, name='mpesa_reconciliation_result'),
    path('mpesa/reconciliation-timeout/', views.mpesa_reconciliation_timeout, name='mpesa_reconciliation_timeout'),

    # Alternative payment method callbacks
    path('airtel/callback/', views.airtel_callback, name='airtel_callback'),
    path('card/webhook/', views.card_webhook, name='card_webhook'),
    path('bank/callback/', views.bank_callback, name='bank_callback'),

    # Wallet and transaction history
    path('wallet/', views.wallet_dashboard, name='wallet_dashboard'),
    path('transactions/', views.transaction_history, name='transaction_history'),
]