from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Deposit and withdrawal
    path('deposit/', views.initiate_deposit, name='deposit'),
    path('withdrawal/', views.withdrawal_request, name='withdrawal_request'),

    # Transaction status and monitoring
    path('transaction/status/<str:transaction_id>/', views.transaction_status, name='transaction_status'),
    path('stk/status/<str:transaction_id>/', views.check_stk_status, name='check_stk_status'),

    # M-Pesa integration
    path('mpesa/callback/', views.mpesa_callback, name='mpesa_callback'),
    path('mpesa/b2c-result/', views.mpesa_b2c_result, name='mpesa_b2c_result'),
    path('mpesa/b2c-timeout/', views.mpesa_b2c_timeout, name='mpesa_b2c_timeout'),
    path('mpesa/reconciliation-result/', views.mpesa_reconciliation_result, name='mpesa_reconciliation_result'),
    path('mpesa/reconciliation-timeout/', views.mpesa_reconciliation_timeout, name='mpesa_reconciliation_timeout'),

    # Alternative payment method callbacks
    path('airtel/callback/', views.airtel_callback, name='airtel_callback'),
    path('card/webhook/', views.card_webhook, name='card_webhook'),
    path('bank/callback/', views.bank_callback, name='bank_callback'),

    # Receipt downloads
    path('deposit/<uuid:deposit_id>/receipt/', views.download_deposit_receipt, name='download_deposit_receipt'),
    path('withdrawal/<uuid:withdrawal_id>/receipt/', views.download_withdrawal_receipt, name='download_withdrawal_receipt'),

    # Notifications
    path('notifications/', views.notifications, name='notifications'),
    path('notifications/<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('notifications/unread-count/', views.get_unread_notifications_count, name='unread_notifications_count'),
    path('settings/low-balance-threshold/', views.update_low_balance_threshold, name='update_low_balance_threshold'),

    # Wallet and transaction history
    path('wallet/', views.wallet_dashboard, name='wallet_dashboard'),
    path('transactions/', views.transaction_history, name='transaction_history'),

    # Real-time wallet updates API
    path('api/wallet/balance/', views.get_wallet_balance, name='wallet_balance'),
    path('api/wallet/stats/', views.get_wallet_stats, name='wallet_stats'),

    # History pages
    path('deposit/history/', views.deposit_history, name='deposit_history'),
    path('withdrawal/history/', views.withdrawal_history, name='withdrawal_history'),

    # AJAX detail views
    path('deposit/<uuid:deposit_id>/details/', views.deposit_details, name='deposit_details'),
    path('withdrawal/<uuid:withdrawal_id>/details/', views.withdrawal_details, name='withdrawal_details'),

    # API endpoints for withdrawal form
    path('api/payment-limits/<str:method_code>/', views.get_payment_limits, name='payment_limits'),
    path('api/recent-withdrawals/', views.get_recent_withdrawals, name='recent_withdrawals'),
]