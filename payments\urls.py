from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Deposit and withdrawal
    path('deposit/', views.initiate_deposit, name='deposit'),
    path('withdrawal/', views.withdrawal_request, name='withdrawal'),

    # Transaction status and monitoring
    path('transaction/status/<str:transaction_id>/', views.transaction_status, name='transaction_status'),
    path('stk/status/<str:transaction_id>/', views.check_stk_status, name='check_stk_status'),

    # M-Pesa integration
    path('mpesa/callback/', views.mpesa_callback, name='mpesa_callback'),

    # Wallet and transaction history
    path('wallet/', views.wallet_dashboard, name='wallet_dashboard'),
    path('transactions/', views.transaction_history, name='transaction_history'),
]