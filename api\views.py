from rest_framework import generics, status, permissions, filters
from rest_framework.decorators import api_view, permission_classes, throttle_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth import get_user_model
from django.db.models import Q, Sum, Count
from django.utils import timezone
from decimal import Decimal
import logging

# Import models
from accounts.models import UserProfile
from sports.models import Sport, League, Team, Match
from betting.models import Bet, BetSlip, BetSelection, Market, Selection
from payments.models import Wallet, Transaction, Deposit, Withdrawal
from promotions.models import Promotion, Bonus, LoyaltyProgram, UserLoyaltyStatus
from casino.models import Game, GameSession

# Import serializers
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    SportSerializer, LeagueSerializer, TeamSerializer, MatchSerializer, MatchListSerializer,
    SelectionSerializer, BetSerializer, PlaceBetSerializer, BetSelectionSerializer,
    WalletSerializer, TransactionSerializer, DepositSerializer, CreateDepositSerializer,
    WithdrawalSerializer, CreateWithdrawalSerializer,
    PromotionSerializer, BonusSerializer, LoyaltyProgramSerializer, UserLoyaltyStatusSerializer,
    GameSerializer, GameSessionSerializer
)

# Import utilities
from payments.services.mpesa_service import mpesa_service

User = get_user_model()
logger = logging.getLogger(__name__)


# ===== AUTHENTICATION VIEWS =====

class UserRegistrationView(generics.CreateAPIView):
    """User registration endpoint"""
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    throttle_classes = [AnonRateThrottle]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Generate tokens
        refresh = RefreshToken.for_user(user)

        return Response({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
            },
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        }, status=status.HTTP_201_CREATED)


class UserLoginView(TokenObtainPairView):
    """Enhanced login view with user data"""
    permission_classes = [permissions.AllowAny]
    throttle_classes = [AnonRateThrottle]

    def post(self, request, *args, **kwargs):
        serializer = UserLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']

        # Generate tokens
        refresh = RefreshToken.for_user(user)

        # Get user profile
        try:
            profile = user.userprofile
            profile_data = UserProfileSerializer(profile).data
        except UserProfile.DoesNotExist:
            profile_data = None

        # Get wallet balance
        try:
            wallet = user.wallet
            wallet_data = WalletSerializer(wallet).data
        except Wallet.DoesNotExist:
            wallet_data = None

        return Response({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'profile': profile_data,
                'wallet': wallet_data,
            },
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        })


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile management"""
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile


# ===== SPORTS API VIEWS =====

class SportListView(generics.ListAPIView):
    """List all active sports"""
    queryset = Sport.objects.filter(is_active=True)
    serializer_class = SportSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name']
    ordering_fields = ['name']
    ordering = ['name']


class LeagueListView(generics.ListAPIView):
    """List leagues by sport"""
    serializer_class = LeagueSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['sport', 'country']
    search_fields = ['name']

    def get_queryset(self):
        return League.objects.filter(is_active=True)


class TeamListView(generics.ListAPIView):
    """List teams by sport"""
    serializer_class = TeamSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['sport', 'country']
    search_fields = ['name']

    def get_queryset(self):
        return Team.objects.filter(is_active=True)


class MatchListView(generics.ListAPIView):
    """List matches with filtering"""
    serializer_class = MatchListSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['sport', 'league', 'status']
    search_fields = ['home_team__name', 'away_team__name']
    ordering_fields = ['start_time']
    ordering = ['start_time']

    def get_queryset(self):
        queryset = Match.objects.select_related(
            'sport', 'league', 'home_team', 'away_team'
        ).prefetch_related('markets__selections')

        # Filter by date range
        date_filter = self.request.query_params.get('date_filter')
        if date_filter == 'today':
            queryset = queryset.filter(start_time__date=timezone.now().date())
        elif date_filter == 'upcoming':
            queryset = queryset.filter(start_time__gte=timezone.now())
        elif date_filter == 'live':
            queryset = queryset.filter(status='live')

        return queryset


class MatchDetailView(generics.RetrieveAPIView):
    """Match detail with selections"""
    queryset = Match.objects.all()
    serializer_class = MatchSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        return Match.objects.select_related(
            'sport', 'league', 'home_team', 'away_team'
        ).prefetch_related('markets__selections')


class SelectionListView(generics.ListAPIView):
    """List selections for a match"""
    serializer_class = SelectionSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['market__bet_type', 'status']

    def get_queryset(self):
        match_id = self.kwargs.get('match_id')
        return Selection.objects.filter(market__match_id=match_id, status='active')


# ===== BETTING API VIEWS =====

class UserBetsView(generics.ListAPIView):
    """List user's bets"""
    serializer_class = BetSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'bet_type']
    ordering_fields = ['placed_at']
    ordering = ['-placed_at']

    def get_queryset(self):
        return Bet.objects.filter(user=self.request.user).prefetch_related('selections')


class PlaceBetView(APIView):
    """Place a new bet"""
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [UserRateThrottle]

    def post(self, request):
        serializer = PlaceBetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        selections_data = serializer.validated_data['selections']
        stake_amount = serializer.validated_data['stake_amount']
        bet_type = serializer.validated_data['bet_type']

        try:
            # Check wallet balance
            wallet = user.wallet
            if wallet.balance < stake_amount:
                return Response({
                    'error': 'Insufficient balance'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Calculate total odds
            total_odds = Decimal('1.00')
            selections = []

            for selection_data in selections_data:
                match = Match.objects.get(id=selection_data['match_id'])
                selection = Selection.objects.get(id=selection_data['selection_id'], market__match=match, status='active')

                selections.append({
                    'match': match,
                    'selection': selection,
                })

                if bet_type == 'MULTIPLE':
                    total_odds *= selection.decimal_odds
                else:
                    total_odds = selection.decimal_odds

            # Calculate potential winnings
            potential_winnings = stake_amount * total_odds

            # Create bet
            bet = Bet.objects.create(
                user=user,
                bet_type=bet_type,
                stake_amount=stake_amount,
                total_odds=total_odds,
                potential_winnings=potential_winnings,
                status='PENDING'
            )

            # Create bet selections
            for selection in selections:
                BetSelection.objects.create(
                    bet=bet,
                    selection=selection['selection'],
                    odds_taken=selection['selection'].decimal_odds
                )

            # Deduct from wallet
            wallet.balance -= stake_amount
            wallet.save()

            # Create transaction record
            Transaction.objects.create(
                user=user,
                transaction_type='BET_PLACED',
                amount=stake_amount,
                status='COMPLETED',
                description=f'Bet placed - {bet.id}'
            )

            return Response({
                'bet_id': bet.id,
                'message': 'Bet placed successfully',
                'potential_winnings': potential_winnings,
                'remaining_balance': wallet.balance
            }, status=status.HTTP_201_CREATED)

        except Match.DoesNotExist:
            return Response({
                'error': 'Invalid match'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Selection.DoesNotExist:
            return Response({
                'error': 'Invalid selection or selection no longer available'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error placing bet: {str(e)}")
            return Response({
                'error': 'Failed to place bet'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ===== WALLET & PAYMENT API VIEWS =====

class WalletBalanceView(generics.RetrieveAPIView):
    """Get user wallet balance"""
    serializer_class = WalletSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        wallet, created = Wallet.objects.get_or_create(user=self.request.user)
        return wallet


class TransactionHistoryView(generics.ListAPIView):
    """User transaction history"""
    serializer_class = TransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['transaction_type', 'status']
    ordering_fields = ['created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        return Transaction.objects.filter(user=self.request.user)


class DepositHistoryView(generics.ListAPIView):
    """User deposit history"""
    serializer_class = DepositSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'payment_method']
    ordering_fields = ['created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        return Deposit.objects.filter(user=self.request.user)


class CreateDepositView(APIView):
    """Create M-Pesa deposit"""
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [UserRateThrottle]

    def post(self, request):
        serializer = CreateDepositSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        amount = serializer.validated_data['amount']
        phone_number = serializer.validated_data['phone_number']
        payment_method = serializer.validated_data['payment_method']

        try:
            # Create deposit record
            deposit = Deposit.objects.create(
                user=user,
                amount=amount,
                phone_number=phone_number,
                payment_method=payment_method,
                status='PENDING'
            )

            # Initiate M-Pesa STK push
            response = mpesa_service.initiate_stk_push(
                phone_number=phone_number,
                amount=amount,
                account_reference=f"DEPOSIT-{deposit.id}",
                transaction_desc=f"ZBet Deposit - {amount}"
            )
            success = response.get('success', False)

            if success:
                deposit.mpesa_checkout_request_id = response.get('CheckoutRequestID')
                deposit.save()

                return Response({
                    'deposit_id': deposit.id,
                    'checkout_request_id': response.get('CheckoutRequestID'),
                    'message': 'M-Pesa payment request sent. Please check your phone.',
                    'status': 'PENDING'
                }, status=status.HTTP_201_CREATED)
            else:
                deposit.status = 'FAILED'
                deposit.save()

                return Response({
                    'error': response.get('errorMessage', 'Payment failed'),
                    'deposit_id': deposit.id
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error creating deposit: {str(e)}")
            return Response({
                'error': 'Failed to initiate deposit'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DepositStatusView(APIView):
    """Check deposit status"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, deposit_id):
        try:
            deposit = Deposit.objects.get(id=deposit_id, user=request.user)
            serializer = DepositSerializer(deposit)
            return Response(serializer.data)
        except Deposit.DoesNotExist:
            return Response({
                'error': 'Deposit not found'
            }, status=status.HTTP_404_NOT_FOUND)


class WithdrawalHistoryView(generics.ListAPIView):
    """User withdrawal history"""
    serializer_class = WithdrawalSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering_fields = ['requested_at']
    ordering = ['-requested_at']

    def get_queryset(self):
        return Withdrawal.objects.filter(user=self.request.user)


class CreateWithdrawalView(APIView):
    """Create withdrawal request"""
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [UserRateThrottle]

    def post(self, request):
        serializer = CreateWithdrawalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        amount = serializer.validated_data['amount']
        phone_number = serializer.validated_data['phone_number']

        try:
            # Check wallet balance
            wallet = user.wallet
            if wallet.balance < amount:
                return Response({
                    'error': 'Insufficient balance'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create withdrawal request
            withdrawal = Withdrawal.objects.create(
                user=user,
                amount=amount,
                phone_number=phone_number,
                status='PENDING'
            )

            # Deduct from wallet (will be refunded if withdrawal fails)
            wallet.balance -= amount
            wallet.save()

            # Create transaction record
            Transaction.objects.create(
                user=user,
                transaction_type='WITHDRAWAL_REQUEST',
                amount=amount,
                status='PENDING',
                description=f'Withdrawal request - {withdrawal.id}'
            )

            return Response({
                'withdrawal_id': withdrawal.id,
                'message': 'Withdrawal request submitted successfully',
                'status': 'PENDING',
                'remaining_balance': wallet.balance
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error creating withdrawal: {str(e)}")
            return Response({
                'error': 'Failed to create withdrawal request'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ===== PROMOTION API VIEWS =====

class PromotionListView(generics.ListAPIView):
    """List active promotions"""
    serializer_class = PromotionSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['promotion_type', 'is_featured']
    ordering_fields = ['start_date', 'priority']
    ordering = ['-is_featured', '-priority']

    def get_queryset(self):
        return Promotion.objects.filter(
            status='ACTIVE',
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        )


class UserBonusesView(generics.ListAPIView):
    """List user bonuses"""
    serializer_class = BonusSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering_fields = ['awarded_at']
    ordering = ['-awarded_at']

    def get_queryset(self):
        return Bonus.objects.filter(user=self.request.user)


class UserLoyaltyStatusView(generics.RetrieveAPIView):
    """Get user loyalty status"""
    serializer_class = UserLoyaltyStatusSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        loyalty_status, created = UserLoyaltyStatus.objects.get_or_create(
            user=self.request.user
        )
        return loyalty_status


# ===== CASINO API VIEWS =====

class GameListView(generics.ListAPIView):
    """List casino games"""
    serializer_class = GameSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['game_type', 'provider']
    search_fields = ['name']
    ordering_fields = ['name', 'rtp']
    ordering = ['name']

    def get_queryset(self):
        return Game.objects.filter(is_active=True)


class UserGameSessionsView(generics.ListAPIView):
    """List user game sessions"""
    serializer_class = GameSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['game', 'status']
    ordering_fields = ['started_at']
    ordering = ['-started_at']

    def get_queryset(self):
        return GameSession.objects.filter(user=self.request.user)


# ===== UTILITY API VIEWS =====

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_dashboard_data(request):
    """Get comprehensive user dashboard data"""
    user = request.user

    try:
        # Wallet data
        wallet = user.wallet
        wallet_data = WalletSerializer(wallet).data

        # Recent bets
        recent_bets = Bet.objects.filter(user=user).order_by('-placed_at')[:5]
        bets_data = BetSerializer(recent_bets, many=True).data

        # Active bonuses
        active_bonuses = Bonus.objects.filter(
            user=user,
            status__in=['ACTIVE', 'WAGERING']
        )
        bonuses_data = BonusSerializer(active_bonuses, many=True).data

        # Recent transactions
        recent_transactions = Transaction.objects.filter(user=user).order_by('-created_at')[:10]
        transactions_data = TransactionSerializer(recent_transactions, many=True).data

        # Loyalty status
        try:
            loyalty_status = user.userloyaltystatus
            loyalty_data = UserLoyaltyStatusSerializer(loyalty_status).data
        except UserLoyaltyStatus.DoesNotExist:
            loyalty_data = None

        return Response({
            'wallet': wallet_data,
            'recent_bets': bets_data,
            'active_bonuses': bonuses_data,
            'recent_transactions': transactions_data,
            'loyalty_status': loyalty_data,
            'stats': {
                'total_bets': Bet.objects.filter(user=user).count(),
                'total_winnings': Bet.objects.filter(
                    user=user, status='WON'
                ).aggregate(total=Sum('potential_winnings'))['total'] or 0,
                'total_deposits': Deposit.objects.filter(
                    user=user, status='COMPLETED'
                ).aggregate(total=Sum('amount'))['total'] or 0,
            }
        })

    except Exception as e:
        logger.error(f"Error getting dashboard data: {str(e)}")
        return Response({
            'error': 'Failed to load dashboard data'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def app_config(request):
    """Get app configuration data"""
    return Response({
        'sports': Sport.objects.filter(is_active=True).values('id', 'name', 'slug', 'icon'),
        'game_types': [
            {'value': 'SLOT', 'label': 'Slots'},
            {'value': 'TABLE', 'label': 'Table Games'},
            {'value': 'LIVE', 'label': 'Live Casino'},
            {'value': 'JACKPOT', 'label': 'Jackpots'},
        ],
        'bet_types': [
            {'value': 'SINGLE', 'label': 'Single Bet'},
            {'value': 'MULTIPLE', 'label': 'Multiple Bet'},
        ],
        'limits': {
            'min_bet': '1.00',
            'max_bet': '100000.00',
            'min_deposit': '10.00',
            'max_deposit': '150000.00',
            'min_withdrawal': '50.00',
        }
    })
