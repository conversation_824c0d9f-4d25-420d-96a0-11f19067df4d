{% extends 'base.html' %}

{% block title %}Disable Two-Factor Authentication - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow border-danger">
            <div class="card-header bg-danger text-white text-center">
                <h3 class="mb-0">Disable Two-Factor Authentication</h3>
                <p class="mb-0">Remove 2FA protection from your account</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x text-danger mb-3"></i>
                    <h5 class="text-danger">Security Warning</h5>
                    <p class="text-muted">
                        You are about to disable two-factor authentication for your account.
                    </p>
                </div>
                
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Important Consequences</h6>
                    <ul class="mb-0">
                        <li>Your account will be less secure</li>
                        <li>You'll lose protection against unauthorized access</li>
                        <li>Your backup codes will become invalid</li>
                        <li>Some features may require re-enabling 2FA</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Alternative Options</h6>
                    <p class="mb-2">Instead of disabling 2FA completely, consider:</p>
                    <ul class="mb-0">
                        <li>Changing your 2FA method (SMS ↔ Authenticator App)</li>
                        <li>Regenerating backup codes if you've lost them</li>
                        <li>Setting up a new authenticator app</li>
                    </ul>
                </div>
                
                <form method="post" id="disable2FAForm">
                    {% csrf_token %}
                    
                    <!-- Password Confirmation -->
                    <div class="form-floating mb-3">
                        {{ form.password }}
                        <label for="{{ form.password.id_for_label }}">Current Password</label>
                        <div class="form-text">{{ form.password.help_text }}</div>
                        {% if form.password.errors %}
                            <div class="text-danger small">{{ form.password.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Confirmation Checkbox -->
                    <div class="form-check mb-3">
                        {{ form.confirmation }}
                        <label class="form-check-label" for="{{ form.confirmation.id_for_label }}">
                            {{ form.confirmation.label }}
                        </label>
                        {% if form.confirmation.errors %}
                            <div class="text-danger small">{{ form.confirmation.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-danger btn-lg" id="disableBtn" disabled>
                            <i class="fas fa-times"></i> Disable Two-Factor Authentication
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:two_factor_settings' %}" class="btn btn-outline-secondary">
                        Cancel - Keep 2FA Enabled
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Security Reminder -->
        <div class="card mt-3 border-warning">
            <div class="card-body text-center">
                <small class="text-muted">
                    <i class="fas fa-lightbulb text-warning"></i>
                    <strong>Security Tip:</strong> Two-factor authentication is one of the best ways to protect your account. 
                    We strongly recommend keeping it enabled for your security and peace of mind.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Enable/disable submit button based on confirmation checkbox
    $('#{{ form.confirmation.id_for_label }}').on('change', function() {
        const isChecked = $(this).is(':checked');
        const password = $('#{{ form.password.id_for_label }}').val();
        
        $('#disableBtn').prop('disabled', !(isChecked && password));
    });
    
    // Check password field
    $('#{{ form.password.id_for_label }}').on('input', function() {
        const password = $(this).val();
        const isChecked = $('#{{ form.confirmation.id_for_label }}').is(':checked');
        
        $('#disableBtn').prop('disabled', !(isChecked && password));
    });
    
    // Form validation and confirmation
    $('#disable2FAForm').on('submit', function(e) {
        const password = $('#{{ form.password.id_for_label }}').val();
        const isChecked = $('#{{ form.confirmation.id_for_label }}').is(':checked');
        
        if (!password) {
            e.preventDefault();
            alert('Please enter your current password.');
            $('#{{ form.password.id_for_label }}').focus();
            return false;
        }
        
        if (!isChecked) {
            e.preventDefault();
            alert('Please confirm that you understand the security implications.');
            return false;
        }
        
        // Final confirmation
        if (!confirm('Are you absolutely sure you want to disable two-factor authentication? This will make your account less secure.')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
