"""
SMS Service for ZBet Platform
Handles SMS sending using Twilio and other providers
"""

from django.conf import settings
from django.core.cache import cache
import logging
import random
import string

logger = logging.getLogger(__name__)


class SMSService:
    """
    SMS service class to handle SMS operations
    """
    
    def __init__(self):
        self.twilio_enabled = self._check_twilio_config()
        if self.twilio_enabled:
            self._init_twilio()
    
    def _check_twilio_config(self):
        """Check if <PERSON><PERSON><PERSON> is properly configured"""
        return all([
            getattr(settings, 'TWILIO_ACCOUNT_SID', None),
            getattr(settings, 'TWILIO_AUTH_TOKEN', None),
            getattr(settings, 'TWILIO_PHONE_NUMBER', None)
        ])
    
    def _init_twilio(self):
        """Initialize Twilio client"""
        try:
            from twilio.rest import Client
            self.twilio_client = Client(
                settings.TWILIO_ACCOUNT_SID,
                settings.TWILIO_AUTH_TOKEN
            )
            self.twilio_phone = settings.TWILIO_PHONE_NUMBER
        except Exception as e:
            logger.error(f"Failed to initialize Twilio client: {e}")
            self.twilio_enabled = False
    
    def generate_verification_code(self, length=6):
        """Generate a numeric verification code"""
        return ''.join(random.choices(string.digits, k=length))
    
    def send_sms(self, to_phone, message):
        """
        Send SMS using available provider
        
        Args:
            to_phone (str): Phone number to send SMS to
            message (str): Message content
            
        Returns:
            dict: Result with success status and message
        """
        if self.twilio_enabled:
            return self._send_twilio_sms(to_phone, message)
        else:
            return self._send_mock_sms(to_phone, message)
    
    def _send_twilio_sms(self, to_phone, message):
        """Send SMS using Twilio"""
        try:
            message_obj = self.twilio_client.messages.create(
                body=message,
                from_=self.twilio_phone,
                to=str(to_phone)
            )
            
            logger.info(f"SMS sent successfully to {to_phone}. SID: {message_obj.sid}")
            
            return {
                'success': True,
                'message': 'SMS sent successfully',
                'provider': 'twilio',
                'sid': message_obj.sid
            }
            
        except Exception as e:
            logger.error(f"Failed to send SMS via Twilio to {to_phone}: {e}")
            return {
                'success': False,
                'message': f'Failed to send SMS: {str(e)}',
                'provider': 'twilio'
            }
    
    def _send_mock_sms(self, to_phone, message):
        """Mock SMS sending for development/testing"""
        logger.info(f"MOCK SMS to {to_phone}: {message}")
        print(f"📱 MOCK SMS to {to_phone}: {message}")
        
        return {
            'success': True,
            'message': 'SMS sent successfully (mock)',
            'provider': 'mock'
        }
    
    def send_verification_code(self, user, phone_number=None, purpose='verification'):
        """
        Send verification code to user's phone
        
        Args:
            user: User object
            phone_number: Optional phone number (uses user's phone if not provided)
            purpose: Purpose of verification ('verification', 'change', '2fa')
            
        Returns:
            dict: Result with success status and code (for testing)
        """
        phone = phone_number or user.phone_number
        if not phone:
            return {
                'success': False,
                'message': 'No phone number provided'
            }
        
        # Generate verification code
        code = self.generate_verification_code()
        
        # Store code in cache (10 minutes expiry)
        cache_key = f'sms_verification_{user.id}_{purpose}'
        cache.set(cache_key, code, 600)
        
        # Prepare message based on purpose
        messages = {
            'verification': f'Your ZBet verification code is: {code}. This code expires in 10 minutes.',
            'change': f'Your ZBet phone change verification code is: {code}. This code expires in 10 minutes.',
            '2fa': f'Your ZBet login code is: {code}. This code expires in 10 minutes.',
            'password_reset': f'Your ZBet password reset code is: {code}. This code expires in 10 minutes.'
        }
        
        message = messages.get(purpose, f'Your ZBet verification code is: {code}')
        
        # Send SMS
        result = self.send_sms(phone, message)
        
        # Add code to result for testing (remove in production)
        if not self.twilio_enabled:
            result['code'] = code
        
        return result
    
    def verify_code(self, user, entered_code, purpose='verification'):
        """
        Verify SMS code
        
        Args:
            user: User object
            entered_code: Code entered by user
            purpose: Purpose of verification
            
        Returns:
            bool: True if code is valid
        """
        cache_key = f'sms_verification_{user.id}_{purpose}'
        stored_code = cache.get(cache_key)
        
        if stored_code and entered_code == stored_code:
            # Clear the code after successful verification
            cache.delete(cache_key)
            return True
        
        return False
    
    def resend_verification_code(self, user, phone_number=None, purpose='verification'):
        """
        Resend verification code with rate limiting
        
        Args:
            user: User object
            phone_number: Optional phone number
            purpose: Purpose of verification
            
        Returns:
            dict: Result with success status
        """
        # Check rate limiting (1 SMS per minute)
        rate_limit_key = f'sms_rate_limit_{user.id}_{purpose}'
        if cache.get(rate_limit_key):
            return {
                'success': False,
                'message': 'Please wait before requesting another code'
            }
        
        # Send new code
        result = self.send_verification_code(user, phone_number, purpose)
        
        if result['success']:
            # Set rate limit (1 minute)
            cache.set(rate_limit_key, True, 60)
        
        return result


# Global SMS service instance
sms_service = SMSService()
