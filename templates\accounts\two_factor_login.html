{% extends 'base.html' %}

{% block title %}Two-Factor Authentication - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Two-Factor Authentication</h3>
                <p class="mb-0">Enter your verification code</p>
            </div>
            
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                    <h5>Security Verification</h5>
                    <p class="text-muted">
                        Hello <strong>{{ user.first_name }}</strong>,<br>
                        Please enter your 2FA code to complete login.
                    </p>
                </div>
                
                <!-- Available Methods -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Available Methods:</h6>
                    <ul class="list-unstyled mb-0">
                        {% if 'sms' in methods %}
                            <li><i class="fas fa-sms"></i> SMS to {{ user.phone_number }}</li>
                        {% endif %}
                        {% if 'totp' in methods %}
                            <li><i class="fas fa-mobile-alt"></i> Authenticator App</li>
                        {% endif %}
                        <li><i class="fas fa-key"></i> Backup Code</li>
                    </ul>
                </div>
                
                <form method="post" id="twoFactorForm">
                    {% csrf_token %}
                    
                    <div class="form-floating mb-3">
                        {{ form.verification_code }}
                        <label for="{{ form.verification_code.id_for_label }}">Verification Code</label>
                        <div class="form-text">{{ form.verification_code.help_text }}</div>
                        {% if form.verification_code.errors %}
                            <div class="text-danger small">{{ form.verification_code.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Verify & Login
                        </button>
                    </div>
                </form>
                
                <!-- Resend SMS -->
                {% if 'sms' in methods %}
                <div class="text-center mb-3">
                    <p class="text-muted">Didn't receive SMS?</p>
                    <button type="button" class="btn btn-outline-secondary" id="resend2FABtn">
                        Resend SMS Code
                    </button>
                </div>
                {% endif %}
                
                <!-- Help -->
                <div class="mt-4">
                    <small class="text-muted">
                        <strong>Having trouble?</strong><br>
                        • Use your authenticator app (Google Authenticator, Authy, etc.)<br>
                        • Use an 8-character backup code<br>
                        • Contact support if you've lost access
                    </small>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:login' %}" class="btn btn-outline-secondary">
                        Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on verification code input
    $('#{{ form.verification_code.id_for_label }}').focus();
    
    // Format verification code input
    $('#{{ form.verification_code.id_for_label }}').on('input', function() {
        const value = this.value;
        
        // Allow both 6-digit codes and 8-character backup codes
        if (value.length === 6 && /^\d{6}$/.test(value)) {
            // Auto-submit for 6-digit codes
            $('#twoFactorForm').submit();
        } else if (value.length === 8 && /^[A-Z0-9]{8}$/.test(value.toUpperCase())) {
            // Auto-submit for 8-character backup codes
            this.value = value.toUpperCase();
            $('#twoFactorForm').submit();
        }
    });
    
    {% if 'sms' in methods %}
    // Resend 2FA SMS code
    $('#resend2FABtn').on('click', function() {
        const btn = $(this);
        btn.prop('disabled', true).text('Sending...');
        
        $.post('{% url "accounts:resend_2fa_sms" %}', {
            csrfmiddlewaretoken: $('[name=csrfmiddlewaretoken]').val()
        }, function(data) {
            if (data.success) {
                alert('2FA code sent successfully!');
                // Start countdown
                let countdown = 60;
                const interval = setInterval(function() {
                    btn.text('Resend SMS (' + countdown + 's)');
                    countdown--;
                    if (countdown < 0) {
                        clearInterval(interval);
                        btn.prop('disabled', false).text('Resend SMS Code');
                    }
                }, 1000);
            } else {
                alert('Failed to send 2FA code: ' + data.error);
                btn.prop('disabled', false).text('Resend SMS Code');
            }
        }).fail(function() {
            alert('An error occurred. Please try again.');
            btn.prop('disabled', false).text('Resend SMS Code');
        });
    });
    {% endif %}
});
</script>
{% endblock %}
