from django.urls import path
from . import views

app_name = 'sports'

urlpatterns = [
    # Match management (staff only)
    path('management/', views.match_management_view, name='match_management'),
    path('management/match/<int:match_id>/', views.match_detail_management_view, name='match_detail_management'),
    path('management/statistics/', views.match_statistics_view, name='match_statistics'),
    
    # AJAX endpoints for match management
    path('api/match/<int:match_id>/status/', views.update_match_status, name='update_match_status'),
    path('api/match/<int:match_id>/score/', views.update_match_score, name='update_match_score'),
    path('api/match/<int:match_id>/event/', views.add_match_event, name='add_match_event'),
    path('api/match/<int:match_id>/markets/', views.create_match_markets, name='create_match_markets'),
    path('api/matches/bulk-update/', views.bulk_update_matches, name='bulk_update_matches'),
]
