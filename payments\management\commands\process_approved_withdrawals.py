"""
Management command to process approved withdrawals
Run this periodically to auto-process approved withdrawals
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from payments.models import Withdrawal
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Process approved withdrawals that are pending processing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--limit',
            type=int,
            default=50,
            help='Maximum number of withdrawals to process in one run'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually processing'
        )

    def handle(self, *args, **options):
        limit = options['limit']
        dry_run = options['dry_run']
        
        # Get approved withdrawals that haven't been processed yet
        approved_withdrawals = Withdrawal.objects.filter(
            status='APPROVED'
        ).order_by('approved_at')[:limit]

        if not approved_withdrawals.exists():
            self.stdout.write(
                self.style.SUCCESS('No approved withdrawals to process.')
            )
            return

        processed_count = 0
        failed_count = 0

        for withdrawal in approved_withdrawals:
            try:
                if dry_run:
                    self.stdout.write(
                        f'Would process: {withdrawal.reference} - '
                        f'KES {withdrawal.amount} for {withdrawal.user.username}'
                    )
                    processed_count += 1
                else:
                    # Process the withdrawal
                    withdrawal.process_approved_withdrawal()
                    processed_count += 1
                    
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Processed: {withdrawal.reference} - '
                            f'KES {withdrawal.amount} for {withdrawal.user.username}'
                        )
                    )
                    
            except Exception as e:
                failed_count += 1
                logger.error(f'Failed to process withdrawal {withdrawal.reference}: {str(e)}')
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to process: {withdrawal.reference} - {str(e)}'
                    )
                )

        # Summary
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: Would process {processed_count} withdrawals'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully processed {processed_count} withdrawals'
                )
            )
            if failed_count > 0:
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to process {failed_count} withdrawals'
                    )
                )
