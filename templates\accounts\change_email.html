{% extends 'base.html' %}

{% block title %}Change Email - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Change Email Address</h3>
                <p class="mb-0">Update your email address</p>
            </div>
            
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-at fa-3x text-primary mb-3"></i>
                    <h5>Update Your Email</h5>
                    <p class="text-muted">
                        Current email: <strong>{{ user.email }}</strong>
                    </p>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Important:</h6>
                    <ul class="list-unstyled mb-0">
                        <li>• You'll need to verify your new email address</li>
                        <li>• Your current password is required for security</li>
                        <li>• You'll receive notifications at the new address</li>
                    </ul>
                </div>
                
                <form method="post" id="changeEmailForm">
                    {% csrf_token %}
                    
                    <!-- New Email -->
                    <div class="form-floating mb-3">
                        {{ form.new_email }}
                        <label for="{{ form.new_email.id_for_label }}">New Email Address</label>
                        <div class="form-text">{{ form.new_email.help_text }}</div>
                        {% if form.new_email.errors %}
                            <div class="text-danger small">{{ form.new_email.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Current Password -->
                    <div class="form-floating mb-3">
                        {{ form.password }}
                        <label for="{{ form.password.id_for_label }}">Current Password</label>
                        <div class="form-text">{{ form.password.help_text }}</div>
                        {% if form.password.errors %}
                            <div class="text-danger small">{{ form.password.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Non-field errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-zbet btn-lg">
                            Change Email Address
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:dashboard' %}" class="btn btn-outline-secondary">
                        Cancel
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Security Notice -->
        <div class="card mt-3">
            <div class="card-body text-center">
                <small class="text-muted">
                    <i class="fas fa-shield-alt"></i>
                    For security reasons, you'll need to verify your new email address before the change takes effect.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-focus on new email field
    $('#{{ form.new_email.id_for_label }}').focus();
    
    // Form validation
    $('#changeEmailForm').on('submit', function(e) {
        const newEmail = $('#{{ form.new_email.id_for_label }}').val().trim();
        const password = $('#{{ form.password.id_for_label }}').val();
        
        if (!newEmail) {
            e.preventDefault();
            alert('Please enter a new email address.');
            $('#{{ form.new_email.id_for_label }}').focus();
            return false;
        }
        
        if (!newEmail.includes('@')) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            $('#{{ form.new_email.id_for_label }}').focus();
            return false;
        }
        
        if (!password) {
            e.preventDefault();
            alert('Please enter your current password.');
            $('#{{ form.password.id_for_label }}').focus();
            return false;
        }
    });
});
</script>
{% endblock %}
