"""
Two-Factor Authentication Service for ZBet Platform
Handles TOTP and SMS-based 2FA
"""

import pyotp
import qrcode
from io import BytesIO
import base64
from django.core.cache import cache
from django.utils import timezone
from .models import TwoFactorAuth
from .sms_service import sms_service
import logging

logger = logging.getLogger(__name__)


class TwoFactorService:
    """
    Service class for handling 2FA operations
    """
    
    def __init__(self):
        pass
    
    def get_or_create_2fa(self, user):
        """Get or create 2FA settings for user"""
        two_factor, created = TwoFactorAuth.objects.get_or_create(
            user=user,
            defaults={
                'is_enabled': False,
                'method': 'sms',
                'sms_enabled': True,
            }
        )
        return two_factor
    
    def enable_2fa(self, user, method='sms'):
        """Enable 2FA for user"""
        two_factor = self.get_or_create_2fa(user)
        
        # Generate TOTP secret if using TOTP
        if method in ['totp', 'both']:
            if not two_factor.totp_secret:
                two_factor.generate_totp_secret()
        
        # Generate backup codes
        if not two_factor.backup_codes:
            two_factor.generate_backup_codes()
        
        two_factor.method = method
        two_factor.is_enabled = True
        two_factor.enabled_at = timezone.now()
        two_factor.save()
        
        return two_factor
    
    def disable_2fa(self, user):
        """Disable 2FA for user"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            two_factor.is_enabled = False
            two_factor.totp_verified = False
            two_factor.enabled_at = None
            two_factor.save()
            return True
        except TwoFactorAuth.DoesNotExist:
            return False
    
    def generate_qr_code(self, user):
        """Generate QR code for TOTP setup"""
        two_factor = self.get_or_create_2fa(user)
        
        if not two_factor.totp_secret:
            two_factor.generate_totp_secret()
            two_factor.save()
        
        # Generate QR code
        totp_uri = two_factor.get_totp_uri()
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64 for display
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        qr_code_data = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            'qr_code': f"data:image/png;base64,{qr_code_data}",
            'secret': two_factor.totp_secret,
            'uri': totp_uri
        }
    
    def verify_totp_setup(self, user, code):
        """Verify TOTP setup during initial configuration"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            if two_factor.verify_totp_code(code):
                two_factor.totp_verified = True
                two_factor.save()
                return True
            return False
        except TwoFactorAuth.DoesNotExist:
            return False
    
    def send_sms_2fa_code(self, user):
        """Send SMS 2FA code"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            if not two_factor.is_enabled or not two_factor.sms_enabled:
                return {'success': False, 'message': 'SMS 2FA not enabled'}
            
            # Send SMS using SMS service
            result = sms_service.send_verification_code(user, purpose='2fa')
            return result
            
        except TwoFactorAuth.DoesNotExist:
            return {'success': False, 'message': '2FA not configured'}
    
    def verify_2fa_code(self, user, code):
        """Verify 2FA code (TOTP, SMS, or backup code)"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            if not two_factor.is_enabled:
                return False
            
            # Check if it's a backup code (8 characters)
            if len(code) == 8:
                if two_factor.use_backup_code(code.upper()):
                    two_factor.last_used_at = timezone.now()
                    two_factor.save()
                    return True
                return False
            
            # Check if it's a 6-digit code
            if len(code) == 6:
                # Try TOTP first if enabled
                if two_factor.method in ['totp', 'both'] and two_factor.totp_verified:
                    if two_factor.verify_totp_code(code):
                        two_factor.last_used_at = timezone.now()
                        two_factor.save()
                        return True
                
                # Try SMS if enabled
                if two_factor.method in ['sms', 'both'] and two_factor.sms_enabled:
                    if sms_service.verify_code(user, code, purpose='2fa'):
                        two_factor.last_used_at = timezone.now()
                        two_factor.save()
                        return True
            
            return False
            
        except TwoFactorAuth.DoesNotExist:
            return False
    
    def is_2fa_enabled(self, user):
        """Check if 2FA is enabled for user"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            return two_factor.is_enabled
        except TwoFactorAuth.DoesNotExist:
            return False
    
    def get_2fa_methods(self, user):
        """Get available 2FA methods for user"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            if not two_factor.is_enabled:
                return []
            
            methods = []
            if two_factor.method in ['sms', 'both'] and two_factor.sms_enabled:
                methods.append('sms')
            if two_factor.method in ['totp', 'both'] and two_factor.totp_verified:
                methods.append('totp')
            
            return methods
            
        except TwoFactorAuth.DoesNotExist:
            return []
    
    def regenerate_backup_codes(self, user):
        """Regenerate backup codes for user"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            codes = two_factor.generate_backup_codes()
            two_factor.save()
            return codes
        except TwoFactorAuth.DoesNotExist:
            return None
    
    def get_backup_codes_info(self, user):
        """Get backup codes information"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            return {
                'total': len(two_factor.backup_codes),
                'used': len(two_factor.backup_codes_used),
                'remaining': two_factor.backup_codes_remaining,
                'codes': two_factor.available_backup_codes
            }
        except TwoFactorAuth.DoesNotExist:
            return None
    
    def require_2fa_for_login(self, user):
        """Check if 2FA is required for login"""
        return self.is_2fa_enabled(user)
    
    def get_2fa_status(self, user):
        """Get comprehensive 2FA status for user"""
        try:
            two_factor = TwoFactorAuth.objects.get(user=user)
            return {
                'enabled': two_factor.is_enabled,
                'method': two_factor.method,
                'totp_verified': two_factor.totp_verified,
                'sms_enabled': two_factor.sms_enabled,
                'backup_codes_remaining': two_factor.backup_codes_remaining,
                'last_used': two_factor.last_used_at,
                'enabled_at': two_factor.enabled_at,
            }
        except TwoFactorAuth.DoesNotExist:
            return {
                'enabled': False,
                'method': None,
                'totp_verified': False,
                'sms_enabled': False,
                'backup_codes_remaining': 0,
                'last_used': None,
                'enabled_at': None,
            }


# Global 2FA service instance
two_factor_service = TwoFactorService()
