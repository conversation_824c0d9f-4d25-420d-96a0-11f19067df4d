from django import forms
from django.contrib.auth.forms import (
    UserCreationForm, AuthenticationForm,
    PasswordResetForm, SetPasswordForm
)
from django.contrib.auth import authenticate
from django.core.exceptions import ValidationError
from django.utils import timezone
from django_countries.fields import CountryField
from phonenumber_field.formfields import PhoneNumberField
from .models import User, UserProfile, ResponsibleGambling, KYCDocument
import re


class UserRegistrationForm(UserCreationForm):
    """
    User registration form with additional fields for betting platform
    """
    
    # Basic Information
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address'
        }),
        help_text='We will send verification email to this address'
    )
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'First Name'
        })
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Last Name'
        })
    )
    
    phone_number = PhoneNumberField(
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+254700000000'
        }),
        help_text='Enter your phone number for M-Pesa transactions and SMS verification'
    )
    
    date_of_birth = forms.DateField(
        required=True,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        help_text='You must be 18 or older to register'
    )
    
    country = CountryField().formfield(
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        initial='KE'
    )
    
    city = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'City (Optional)'
        })
    )
    
    # Referral Code
    referral_code_used = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Referral Code (Optional)'
        }),
        help_text='Enter referral code if you were referred by someone'
    )
    
    # Terms and Conditions
    terms_accepted = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I accept the Terms and Conditions and Privacy Policy'
    )
    
    marketing_consent = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I agree to receive promotional emails and SMS'
    )
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'first_name', 'last_name', 
            'phone_number', 'date_of_birth', 'country', 'city',
            'password1', 'password2'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Customize username field
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Choose a username'
        })
        
        # Customize password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Create a strong password'
        })
        
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirm your password'
        })
        
        # Add help text for username
        self.fields['username'].help_text = 'Username must be unique and contain only letters, numbers, and @/./+/-/_ characters.'
    
    def clean_email(self):
        """Validate email uniqueness"""
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email=email).exists():
            raise ValidationError('A user with this email already exists.')
        return email
    
    def clean_phone_number(self):
        """Validate phone number uniqueness"""
        phone_number = self.cleaned_data.get('phone_number')
        if phone_number and User.objects.filter(phone_number=phone_number).exists():
            raise ValidationError('A user with this phone number already exists.')
        return phone_number
    
    def clean_date_of_birth(self):
        """Validate age requirement"""
        date_of_birth = self.cleaned_data.get('date_of_birth')
        if date_of_birth:
            today = timezone.now().date()
            age = today.year - date_of_birth.year - (
                (today.month, today.day) < (date_of_birth.month, date_of_birth.day)
            )
            if age < 18:
                raise ValidationError('You must be at least 18 years old to register.')
        return date_of_birth
    
    def clean_referral_code_used(self):
        """Validate referral code if provided"""
        referral_code = self.cleaned_data.get('referral_code_used')
        if referral_code:
            try:
                User.objects.get(referral_code=referral_code)
            except User.DoesNotExist:
                raise ValidationError('Invalid referral code.')
        return referral_code
    
    def clean_username(self):
        """Additional username validation"""
        username = self.cleaned_data.get('username')
        if username:
            # Check for inappropriate usernames
            inappropriate_words = ['admin', 'root', 'test', 'bet', 'casino', 'support']
            if any(word in username.lower() for word in inappropriate_words):
                raise ValidationError('This username is not allowed.')
            
            # Check username pattern
            if not re.match(r'^[a-zA-Z0-9@.+_-]+$', username):
                raise ValidationError('Username contains invalid characters.')
        
        return username
    
    def save(self, commit=True):
        """Save user with additional fields"""
        user = super().save(commit=False)
        
        # Set additional fields
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.phone_number = self.cleaned_data['phone_number']
        user.date_of_birth = self.cleaned_data['date_of_birth']
        user.country = self.cleaned_data['country']
        user.city = self.cleaned_data['city']
        
        # Set marketing preferences
        user.marketing_emails = self.cleaned_data.get('marketing_consent', False)
        user.marketing_sms = self.cleaned_data.get('marketing_consent', False)
        
        # Handle referral
        referral_code = self.cleaned_data.get('referral_code_used')
        if referral_code:
            try:
                referrer = User.objects.get(referral_code=referral_code)
                user.referred_by = referrer
            except User.DoesNotExist:
                pass
        
        if commit:
            user.save()
            
            # Create user profile
            UserProfile.objects.create(user=user)
        
        return user


class EmailVerificationForm(forms.Form):
    """
    Form for email verification code
    """
    verification_code = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'maxlength': '6',
            'style': 'letter-spacing: 0.5em; font-size: 1.5em;'
        }),
        help_text='Enter the 6-digit code sent to your email'
    )
    
    def clean_verification_code(self):
        """Validate verification code format"""
        code = self.cleaned_data.get('verification_code')
        if code and not code.isdigit():
            raise ValidationError('Verification code must contain only numbers.')
        return code


class PhoneVerificationForm(forms.Form):
    """
    Form for phone number verification code
    """
    verification_code = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'maxlength': '6',
            'style': 'letter-spacing: 0.5em; font-size: 1.5em;'
        }),
        help_text='Enter the 6-digit code sent to your phone'
    )
    
    def clean_verification_code(self):
        """Validate verification code format"""
        code = self.cleaned_data.get('verification_code')
        if code and not code.isdigit():
            raise ValidationError('Verification code must contain only numbers.')
        return code


class ResendVerificationForm(forms.Form):
    """
    Form for resending verification codes
    """
    verification_type = forms.ChoiceField(
        choices=[
            ('email', 'Email'),
            ('phone', 'Phone'),
        ],
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )


class UserLoginForm(AuthenticationForm):
    """
    Custom login form with enhanced styling and validation
    """
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Username or Email',
            'autofocus': True
        }),
        help_text='Enter your username or email address'
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Password'
        })
    )

    remember_me = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='Remember me for 30 days'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Remove the default help text
        self.fields['username'].help_text = None

    def clean_username(self):
        """Allow login with email or username"""
        username = self.cleaned_data.get('username')

        if '@' in username:
            # User entered email, try to find the username
            try:
                user = User.objects.get(email=username)
                return user.username
            except User.DoesNotExist:
                pass

        return username

    def clean(self):
        """Custom authentication with additional security checks"""
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')

        if username and password:
            # Check if user exists
            try:
                if '@' in username:
                    user = User.objects.get(email=username)
                else:
                    user = User.objects.get(username=username)

                # Check if account is suspended
                if user.is_suspended:
                    raise ValidationError(
                        'Your account has been suspended. Please contact support.'
                    )

                # Check if account is active
                if not user.is_active:
                    raise ValidationError(
                        'Your account is inactive. Please contact support.'
                    )

                # Authenticate user
                if '@' in username:
                    auth_user = authenticate(
                        username=user.username,
                        password=password
                    )
                else:
                    auth_user = authenticate(
                        username=username,
                        password=password
                    )

                if auth_user is None:
                    raise ValidationError(
                        'Invalid username/email or password.'
                    )

                self.user_cache = auth_user

            except User.DoesNotExist:
                raise ValidationError(
                    'Invalid username/email or password.'
                )

        return self.cleaned_data


class CustomPasswordResetForm(PasswordResetForm):
    """
    Custom password reset form with enhanced styling
    """
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address',
            'autofocus': True
        }),
        help_text='Enter the email address associated with your account'
    )

    def clean_email(self):
        """Validate that email exists in the system"""
        email = self.cleaned_data.get('email')

        if email:
            try:
                user = User.objects.get(email=email)
                if not user.is_active:
                    raise ValidationError(
                        'This account is inactive. Please contact support.'
                    )
                if user.is_suspended:
                    raise ValidationError(
                        'This account is suspended. Please contact support.'
                    )
            except User.DoesNotExist:
                # Don't reveal that the email doesn't exist for security
                pass

        return email


class CustomSetPasswordForm(SetPasswordForm):
    """
    Custom set password form with enhanced styling and validation
    """
    new_password1 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter new password'
        }),
        label='New Password',
        help_text='Password must be at least 8 characters long and contain letters and numbers'
    )

    new_password2 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm new password'
        }),
        label='Confirm New Password'
    )

    def clean_new_password1(self):
        """Additional password validation"""
        password = self.cleaned_data.get('new_password1')

        if password:
            # Check minimum length
            if len(password) < 8:
                raise ValidationError('Password must be at least 8 characters long.')

            # Check for at least one letter and one number
            if not any(c.isalpha() for c in password):
                raise ValidationError('Password must contain at least one letter.')

            if not any(c.isdigit() for c in password):
                raise ValidationError('Password must contain at least one number.')

        return password


class EmailChangeForm(forms.Form):
    """
    Form for changing user email address
    """
    new_email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter new email address'
        }),
        help_text='Enter your new email address'
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Current password'
        }),
        help_text='Enter your current password to confirm the change'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_new_email(self):
        """Validate new email"""
        new_email = self.cleaned_data.get('new_email')

        if new_email:
            # Check if email is different from current
            if new_email == self.user.email:
                raise ValidationError('This is already your current email address.')

            # Check if email is already taken
            if User.objects.filter(email=new_email).exists():
                raise ValidationError('This email address is already in use.')

        return new_email

    def clean_password(self):
        """Validate current password"""
        password = self.cleaned_data.get('password')

        if password and not self.user.check_password(password):
            raise ValidationError('Incorrect password.')

        return password


class RequestEmailVerificationForm(forms.Form):
    """
    Form for requesting email verification
    """
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter email address to verify',
            'readonly': True
        }),
        help_text='This is the email address that will be verified'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        # Pre-fill with user's current email
        self.fields['email'].initial = user.email


class PhoneChangeForm(forms.Form):
    """
    Form for changing user phone number
    """
    new_phone = PhoneNumberField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+254700000000'
        }),
        help_text='Enter your new phone number'
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Current password'
        }),
        help_text='Enter your current password to confirm the change'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_new_phone(self):
        """Validate new phone number"""
        new_phone = self.cleaned_data.get('new_phone')

        if new_phone:
            # Check if phone is different from current
            if new_phone == self.user.phone_number:
                raise ValidationError('This is already your current phone number.')

            # Check if phone is already taken
            if User.objects.filter(phone_number=new_phone).exists():
                raise ValidationError('This phone number is already in use.')

        return new_phone

    def clean_password(self):
        """Validate current password"""
        password = self.cleaned_data.get('password')

        if password and not self.user.check_password(password):
            raise ValidationError('Incorrect password.')

        return password


class RequestSMSVerificationForm(forms.Form):
    """
    Form for requesting SMS verification
    """
    phone_number = PhoneNumberField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+254700000000',
            'readonly': True
        }),
        help_text='This is the phone number that will be verified'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
        # Pre-fill with user's current phone
        self.fields['phone_number'].initial = user.phone_number


class SMSVerificationForm(forms.Form):
    """
    Form for SMS verification code
    """
    verification_code = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'maxlength': '6',
            'style': 'letter-spacing: 0.5em; font-size: 1.5em;'
        }),
        help_text='Enter the 6-digit code sent to your phone'
    )

    def clean_verification_code(self):
        """Validate verification code format"""
        code = self.cleaned_data.get('verification_code')
        if code and not code.isdigit():
            raise ValidationError('Verification code must contain only numbers.')
        return code


class Enable2FAForm(forms.Form):
    """
    Form for enabling 2FA
    """
    method = forms.ChoiceField(
        choices=[
            ('sms', 'SMS - Receive codes via text message'),
            ('totp', 'Authenticator App - Use Google Authenticator, Authy, etc.'),
            ('both', 'Both SMS and Authenticator App'),
        ],
        widget=forms.RadioSelect(attrs={
            'class': 'form-check-input'
        }),
        help_text='Choose your preferred 2FA method'
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Current password'
        }),
        help_text='Enter your current password to enable 2FA'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_password(self):
        """Validate current password"""
        password = self.cleaned_data.get('password')

        if password and not self.user.check_password(password):
            raise ValidationError('Incorrect password.')

        return password


class Verify2FASetupForm(forms.Form):
    """
    Form for verifying 2FA setup
    """
    verification_code = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'maxlength': '6',
            'style': 'letter-spacing: 0.5em; font-size: 1.5em;'
        }),
        help_text='Enter the 6-digit code from your authenticator app or SMS'
    )

    def clean_verification_code(self):
        """Validate verification code format"""
        code = self.cleaned_data.get('verification_code')
        if code and not code.isdigit():
            raise ValidationError('Verification code must contain only numbers.')
        return code


class TwoFactorLoginForm(forms.Form):
    """
    Form for 2FA login verification
    """
    verification_code = forms.CharField(
        max_length=8,  # Allow backup codes which are 8 characters
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'style': 'letter-spacing: 0.5em; font-size: 1.5em;'
        }),
        help_text='Enter your 6-digit 2FA code or 8-character backup code'
    )

    def clean_verification_code(self):
        """Validate verification code format"""
        code = self.cleaned_data.get('verification_code')
        if code:
            # Allow both 6-digit codes and 8-character backup codes
            if len(code) == 6 and not code.isdigit():
                raise ValidationError('6-digit codes must contain only numbers.')
            elif len(code) == 8 and not code.isalnum():
                raise ValidationError('8-character backup codes must contain only letters and numbers.')
            elif len(code) not in [6, 8]:
                raise ValidationError('Code must be 6 digits or 8 characters.')
        return code


class Disable2FAForm(forms.Form):
    """
    Form for disabling 2FA
    """
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Current password'
        }),
        help_text='Enter your current password to disable 2FA'
    )

    confirmation = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I understand that disabling 2FA will make my account less secure'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_password(self):
        """Validate current password"""
        password = self.cleaned_data.get('password')

        if password and not self.user.check_password(password):
            raise ValidationError('Incorrect password.')

        return password


class RegenerateBackupCodesForm(forms.Form):
    """
    Form for regenerating backup codes
    """
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Current password'
        }),
        help_text='Enter your current password to regenerate backup codes'
    )

    confirmation = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I understand that old backup codes will no longer work'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_password(self):
        """Validate current password"""
        password = self.cleaned_data.get('password')

        if password and not self.user.check_password(password):
            raise ValidationError('Incorrect password.')

        return password


class UserProfileUpdateForm(forms.ModelForm):
    """
    Form for updating user profile information
    """
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'date_of_birth',
            'country', 'city'
        ]
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'First Name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Last Name'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'country': forms.Select(attrs={
                'class': 'form-control'
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'City'
            }),
        }

    def clean_date_of_birth(self):
        """Validate date of birth"""
        dob = self.cleaned_data.get('date_of_birth')
        if dob:
            from datetime import date
            today = date.today()
            age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))

            if age < 18:
                raise ValidationError('You must be at least 18 years old to register.')
            if age > 120:
                raise ValidationError('Please enter a valid date of birth.')

        return dob


class UserProfileDetailsForm(forms.ModelForm):
    """
    Form for updating detailed user profile information
    """
    class Meta:
        model = UserProfile
        fields = [
            'gender', 'occupation', 'address_line_1', 'address_line_2',
            'postal_code', 'emergency_contact_name', 'emergency_contact_phone',
            'betting_experience', 'favorite_sports', 'bio'
        ]
        widgets = {
            'gender': forms.Select(attrs={
                'class': 'form-control'
            }),
            'occupation': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Your occupation'
            }),
            'address_line_1': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Address Line 1'
            }),
            'address_line_2': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Address Line 2 (Optional)'
            }),
            'postal_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Postal Code'
            }),
            'emergency_contact_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Emergency Contact Name'
            }),
            'emergency_contact_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+254700000000'
            }),
            'betting_experience': forms.Select(attrs={
                'class': 'form-control'
            }),
            'favorite_sports': forms.CheckboxSelectMultiple(),
            'bio': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Tell us about yourself...'
            }),
        }


class ProfilePictureForm(forms.ModelForm):
    """
    Form for uploading profile picture
    """
    class Meta:
        model = UserProfile
        fields = ['avatar']
        widgets = {
            'avatar': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }

    def clean_avatar(self):
        """Validate profile picture"""
        avatar = self.cleaned_data.get('avatar')

        if avatar:
            # Check file size (max 5MB)
            if avatar.size > 5 * 1024 * 1024:
                raise ValidationError('Image file too large. Maximum size is 5MB.')

            # Check file type
            valid_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if avatar.content_type not in valid_types:
                raise ValidationError('Invalid image format. Please use JPEG, PNG, or GIF.')

        return avatar


class NotificationPreferencesForm(forms.ModelForm):
    """
    Form for managing notification preferences
    """
    class Meta:
        model = UserProfile
        fields = [
            'email_notifications', 'sms_notifications', 'push_notifications',
            'marketing_emails', 'bet_notifications', 'promotion_notifications'
        ]
        widgets = {
            'email_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'sms_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'push_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'marketing_emails': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'bet_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'promotion_notifications': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }


class ResponsibleGamblingForm(forms.ModelForm):
    """
    Form for managing responsible gambling settings
    """
    class Meta:
        model = ResponsibleGambling
        fields = [
            'daily_deposit_limit', 'weekly_deposit_limit', 'monthly_deposit_limit',
            'daily_bet_limit', 'weekly_bet_limit', 'monthly_bet_limit',
            'daily_loss_limit', 'weekly_loss_limit', 'monthly_loss_limit',
            'session_time_limit', 'daily_session_limit',
            'reality_check_enabled', 'reality_check_interval'
        ]
        widgets = {
            'daily_deposit_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'weekly_deposit_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'monthly_deposit_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'daily_bet_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'weekly_bet_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'monthly_bet_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'daily_loss_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'weekly_loss_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'monthly_loss_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'session_time_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Minutes'
            }),
            'daily_session_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Minutes'
            }),
            'reality_check_enabled': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'reality_check_interval': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Minutes'
            }),
        }

    def clean(self):
        """Validate responsible gambling limits"""
        cleaned_data = super().clean()

        # Validate deposit limits
        daily_deposit = cleaned_data.get('daily_deposit_limit')
        weekly_deposit = cleaned_data.get('weekly_deposit_limit')
        monthly_deposit = cleaned_data.get('monthly_deposit_limit')

        if daily_deposit and weekly_deposit and daily_deposit * 7 > weekly_deposit:
            raise ValidationError('Weekly deposit limit should be at least 7 times the daily limit.')

        if weekly_deposit and monthly_deposit and weekly_deposit * 4 > monthly_deposit:
            raise ValidationError('Monthly deposit limit should be at least 4 times the weekly limit.')

        return cleaned_data


class SelfExclusionForm(forms.ModelForm):
    """
    Form for self-exclusion
    """
    confirmation = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I understand that self-exclusion will prevent me from accessing my account for the selected period'
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Current password'
        }),
        help_text='Enter your current password to confirm self-exclusion'
    )

    class Meta:
        model = ResponsibleGambling
        fields = ['self_exclusion_period', 'self_exclusion_reason']
        widgets = {
            'self_exclusion_period': forms.Select(attrs={
                'class': 'form-control'
            }),
            'self_exclusion_reason': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Please tell us why you want to self-exclude (optional)'
            }),
        }

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_password(self):
        """Validate current password"""
        password = self.cleaned_data.get('password')

        if password and not self.user.check_password(password):
            raise ValidationError('Incorrect password.')

        return password


class AccountClosureForm(forms.Form):
    """
    Form for account closure request
    """
    CLOSURE_REASON_CHOICES = [
        ('gambling_problem', 'Gambling Problem'),
        ('financial_reasons', 'Financial Reasons'),
        ('no_longer_interested', 'No Longer Interested'),
        ('poor_service', 'Poor Service'),
        ('technical_issues', 'Technical Issues'),
        ('other', 'Other'),
    ]

    closure_reason = forms.ChoiceField(
        choices=CLOSURE_REASON_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        help_text='Please select the main reason for closing your account'
    )

    additional_comments = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Please provide any additional comments (optional)'
        }),
        help_text='Any additional feedback would be appreciated'
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Current password'
        }),
        help_text='Enter your current password to confirm account closure'
    )

    confirmation = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I understand that closing my account is permanent and cannot be undone'
    )

    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)

    def clean_password(self):
        """Validate current password"""
        password = self.cleaned_data.get('password')

        if password and not self.user.check_password(password):
            raise ValidationError('Incorrect password.')

        return password


class KYCDocumentUploadForm(forms.ModelForm):
    """
    Form for uploading KYC documents
    """
    class Meta:
        model = KYCDocument
        fields = ['document_type', 'document_number', 'document_file']
        widgets = {
            'document_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'document_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter document number (optional)'
            }),
            'document_file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf'
            }),
        }

    def clean_document_file(self):
        """Validate document file"""
        document_file = self.cleaned_data.get('document_file')

        if document_file:
            # Check file size (max 10MB)
            if document_file.size > 10 * 1024 * 1024:
                raise ValidationError('File size too large. Maximum size is 10MB.')

            # Check file type
            valid_types = [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
                'application/pdf'
            ]
            if document_file.content_type not in valid_types:
                raise ValidationError('Invalid file format. Please use JPEG, PNG, GIF, or PDF.')

        return document_file

    def clean(self):
        """Additional validation"""
        cleaned_data = super().clean()
        document_type = cleaned_data.get('document_type')
        document_number = cleaned_data.get('document_number')

        # Require document number for certain document types
        if document_type in ['national_id', 'passport', 'driving_license']:
            if not document_number:
                raise ValidationError(f'Document number is required for {document_type}.')

        return cleaned_data


class KYCVerificationForm(forms.ModelForm):
    """
    Form for admin to verify KYC documents
    """
    class Meta:
        model = KYCDocument
        fields = ['status', 'verification_notes']
        widgets = {
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'verification_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Add verification notes (required for rejection)'
            }),
        }

    def clean(self):
        """Validate verification"""
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        verification_notes = cleaned_data.get('verification_notes')

        # Require notes for rejection
        if status == 'rejected' and not verification_notes:
            raise ValidationError('Verification notes are required when rejecting a document.')

        return cleaned_data
