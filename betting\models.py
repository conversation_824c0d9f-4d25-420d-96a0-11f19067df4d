from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth import get_user_model
from decimal import Decimal
from sports.models import Match, Team, Sport

User = get_user_model()


class BetType(models.Model):
    """
    Types of bets available (Match Winner, Over/Under, etc.)
    """
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)

    # Sport compatibility
    sports = models.ManyToManyField(Sport, related_name='bet_types')

    # Bet configuration
    is_active = models.BooleanField(default=True)
    is_live_betting = models.BooleanField(default=False, help_text="Available for live betting")
    display_order = models.PositiveIntegerField(default=0)

    # Odds calculation
    margin_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('5.00'),
        help_text="House margin percentage"
    )

    class Meta:
        db_table = 'betting_bet_type'
        ordering = ['display_order', 'name']
        verbose_name = 'Bet Type'
        verbose_name_plural = 'Bet Types'

    def __str__(self):
        return self.name


class Market(models.Model):
    """
    Betting markets for specific matches
    """
    MARKET_STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('settled', 'Settled'),
        ('cancelled', 'Cancelled'),
    ]

    match = models.ForeignKey(Match, on_delete=models.CASCADE, related_name='markets')
    bet_type = models.ForeignKey(BetType, on_delete=models.CASCADE, related_name='markets')

    # Market details
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=MARKET_STATUS_CHOICES, default='active')

    # Market parameters (e.g., handicap value, over/under line)
    parameters = models.JSONField(default=dict, blank=True)

    # Settlement
    winning_selection = models.CharField(max_length=200, blank=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_market'
        ordering = ['match__start_time', 'bet_type__display_order']
        verbose_name = 'Market'
        verbose_name_plural = 'Markets'
        unique_together = ['match', 'bet_type', 'parameters']

    def __str__(self):
        return f"{self.match} - {self.name}"

    @property
    def is_active(self):
        """Check if market is active for betting"""
        return self.status == 'active' and self.match.betting_enabled

    @property
    def is_live(self):
        """Check if market is available for live betting"""
        return (self.is_active and
                self.match.is_live and
                self.match.live_betting_enabled and
                self.bet_type.is_live_betting)


class Selection(models.Model):
    """
    Individual betting selections within a market
    """
    SELECTION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
    ]

    market = models.ForeignKey(Market, on_delete=models.CASCADE, related_name='selections')

    # Selection details
    name = models.CharField(max_length=200)
    short_name = models.CharField(max_length=50, blank=True)
    status = models.CharField(max_length=20, choices=SELECTION_STATUS_CHOICES, default='active')

    # Odds
    decimal_odds = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('1.01'))]
    )
    fractional_odds = models.CharField(max_length=20, blank=True)
    american_odds = models.IntegerField(null=True, blank=True)

    # Betting limits
    min_bet = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('10.00'))
    max_bet = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('10000.00'))

    # Statistics
    total_bets = models.PositiveIntegerField(default=0)
    total_stake = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_selection'
        ordering = ['market', 'name']
        verbose_name = 'Selection'
        verbose_name_plural = 'Selections'

    def __str__(self):
        return f"{self.market.name} - {self.name} ({self.decimal_odds})"

    @property
    def is_active(self):
        """Check if selection is active for betting"""
        return self.status == 'active' and self.market.is_active

    def calculate_fractional_odds(self):
        """Calculate fractional odds from decimal odds"""
        if self.decimal_odds <= 1:
            return "0/1"

        decimal_part = float(self.decimal_odds) - 1

        # Convert to fraction
        from fractions import Fraction
        frac = Fraction(decimal_part).limit_denominator(100)
        return f"{frac.numerator}/{frac.denominator}"

    def calculate_american_odds(self):
        """Calculate American odds from decimal odds"""
        decimal = float(self.decimal_odds)

        if decimal >= 2.0:
            return int((decimal - 1) * 100)
        else:
            return int(-100 / (decimal - 1))

    def save(self, *args, **kwargs):
        """Auto-calculate other odds formats"""
        if not self.fractional_odds:
            self.fractional_odds = self.calculate_fractional_odds()

        if not self.american_odds:
            self.american_odds = self.calculate_american_odds()

        super().save(*args, **kwargs)


class OddsHistory(models.Model):
    """
    Track odds changes over time
    """
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE, related_name='odds_history')

    # Previous odds
    old_decimal_odds = models.DecimalField(max_digits=10, decimal_places=2)
    new_decimal_odds = models.DecimalField(max_digits=10, decimal_places=2)

    # Change details
    change_reason = models.CharField(max_length=200, blank=True)
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Timestamp
    changed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_odds_history'
        ordering = ['-changed_at']
        verbose_name = 'Odds History'
        verbose_name_plural = 'Odds History'

    def __str__(self):
        return f"{self.selection} - {self.old_decimal_odds} → {self.new_decimal_odds}"

    @property
    def change_percentage(self):
        """Calculate percentage change in odds"""
        if self.old_decimal_odds == 0:
            return 0

        change = ((self.new_decimal_odds - self.old_decimal_odds) / self.old_decimal_odds) * 100
        return round(change, 2)


class Bet(models.Model):
    """
    Individual bets placed by users
    """
    BET_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
        ('cashout', 'Cashed Out'),
    ]

    BET_TYPE_CHOICES = [
        ('single', 'Single Bet'),
        ('multiple', 'Multiple Bet'),
        ('system', 'System Bet'),
    ]

    # User and identification
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bets')
    bet_id = models.CharField(max_length=20, unique=True)

    # Bet details
    bet_type = models.CharField(max_length=20, choices=BET_TYPE_CHOICES, default='single')
    status = models.CharField(max_length=20, choices=BET_STATUS_CHOICES, default='pending')

    # Financial details
    stake = models.DecimalField(max_digits=10, decimal_places=2)
    total_odds = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    potential_win = models.DecimalField(max_digits=15, decimal_places=2)
    actual_win = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Cashout
    cashout_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    cashout_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    placed_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_bet'
        ordering = ['-placed_at']
        verbose_name = 'Bet'
        verbose_name_plural = 'Bets'

    def __str__(self):
        return f"Bet {self.bet_id} - {self.user.email} - {self.stake}"

    @property
    def is_active(self):
        """Check if bet is still active"""
        return self.status == 'pending'

    @property
    def profit_loss(self):
        """Calculate profit or loss"""
        if self.status == 'won':
            return self.actual_win - self.stake
        elif self.status == 'lost':
            return -self.stake
        elif self.status == 'cashout':
            return self.cashout_value - self.stake if self.cashout_value else Decimal('0.00')
        return Decimal('0.00')

    def calculate_potential_win(self):
        """Calculate potential winnings"""
        return self.stake * self.total_odds

    def save(self, *args, **kwargs):
        """Auto-calculate potential win"""
        if not self.potential_win:
            self.potential_win = self.calculate_potential_win()

        # Generate bet ID if not provided
        if not self.bet_id:
            import uuid
            self.bet_id = str(uuid.uuid4())[:8].upper()

        super().save(*args, **kwargs)


class BetSelection(models.Model):
    """
    Individual selections within a bet
    """
    SELECTION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
    ]

    bet = models.ForeignKey(Bet, on_delete=models.CASCADE, related_name='selections')
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE, related_name='bet_selections')

    # Odds at time of bet
    odds_taken = models.DecimalField(max_digits=10, decimal_places=2)

    # Status
    status = models.CharField(max_length=20, choices=SELECTION_STATUS_CHOICES, default='pending')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_bet_selection'
        ordering = ['created_at']
        verbose_name = 'Bet Selection'
        verbose_name_plural = 'Bet Selections'

    def __str__(self):
        return f"{self.bet.bet_id} - {self.selection.name} @ {self.odds_taken}"


class BetSlip(models.Model):
    """
    Temporary storage for bet slips before placement
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bet_slips')
    session_key = models.CharField(max_length=40, blank=True)

    # Bet slip details
    selections = models.ManyToManyField(Selection, through='BetSlipSelection')
    bet_type = models.CharField(max_length=20, default='single')
    stake = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Calculated values
    total_odds = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    potential_win = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_bet_slip'
        ordering = ['-updated_at']
        verbose_name = 'Bet Slip'
        verbose_name_plural = 'Bet Slips'

    def __str__(self):
        return f"Bet Slip - {self.user.email} - {self.selections.count()} selections"

    def calculate_total_odds(self):
        """Calculate total odds for multiple selections"""
        if self.bet_type == 'single':
            # For single bets, return the odds of the first selection
            first_selection = self.betslipselection_set.first()
            return first_selection.selection.decimal_odds if first_selection else Decimal('1.00')
        else:
            # For multiple bets, multiply all odds
            total = Decimal('1.00')
            for bet_slip_selection in self.betslipselection_set.all():
                total *= bet_slip_selection.selection.decimal_odds
            return total

    def calculate_potential_win(self):
        """Calculate potential winnings"""
        return self.stake * self.total_odds

    def update_calculations(self):
        """Update calculated values"""
        self.total_odds = self.calculate_total_odds()
        self.potential_win = self.calculate_potential_win()
        self.save()


class BetSlipSelection(models.Model):
    """
    Through model for bet slip selections
    """
    bet_slip = models.ForeignKey(BetSlip, on_delete=models.CASCADE)
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE)

    # Order in bet slip
    order = models.PositiveIntegerField(default=0)

    # Timestamps
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_bet_slip_selection'
        ordering = ['order', 'added_at']
        unique_together = ['bet_slip', 'selection']
        verbose_name = 'Bet Slip Selection'
        verbose_name_plural = 'Bet Slip Selections'

    def __str__(self):
        return f"{self.bet_slip} - {self.selection.name}"


class Settlement(models.Model):
    """
    Bet settlement tracking and management
    """
    SETTLEMENT_STATUS_CHOICES = [
        ('pending', 'Pending Settlement'),
        ('settled', 'Settled'),
        ('voided', 'Voided'),
        ('disputed', 'Disputed'),
        ('reviewed', 'Under Review'),
    ]

    SETTLEMENT_TYPE_CHOICES = [
        ('automatic', 'Automatic Settlement'),
        ('manual', 'Manual Settlement'),
        ('correction', 'Settlement Correction'),
        ('void', 'Void Settlement'),
    ]

    # Settlement identification
    settlement_id = models.CharField(max_length=20, unique=True)

    # Related objects
    bet = models.ForeignKey(Bet, on_delete=models.CASCADE, related_name='settlements')
    market = models.ForeignKey(Market, on_delete=models.CASCADE, related_name='settlements')

    # Settlement details
    settlement_type = models.CharField(max_length=20, choices=SETTLEMENT_TYPE_CHOICES, default='automatic')
    status = models.CharField(max_length=20, choices=SETTLEMENT_STATUS_CHOICES, default='pending')

    # Settlement results
    winning_selection = models.ForeignKey(
        Selection,
        on_delete=models.CASCADE,
        related_name='winning_settlements',
        null=True,
        blank=True
    )

    # Financial details
    settlement_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    net_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Settlement metadata
    settlement_reason = models.TextField(blank=True)
    settlement_notes = models.TextField(blank=True)
    settlement_data = models.JSONField(default=dict, blank=True, help_text="Additional settlement data")

    # Staff information
    settled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='settlements_made'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_settlement'
        ordering = ['-created_at']
        verbose_name = 'Settlement'
        verbose_name_plural = 'Settlements'

    def __str__(self):
        return f"Settlement {self.settlement_id} - {self.bet.bet_id}"

    @property
    def is_winning(self):
        """Check if this is a winning settlement"""
        return self.settlement_amount > 0

    @property
    def is_settled(self):
        """Check if settlement is completed"""
        return self.status == 'settled'

    def calculate_settlement_amount(self):
        """Calculate settlement amount based on bet selections"""
        if not self.winning_selection:
            return Decimal('0.00')

        # Check if user's bet selection matches winning selection
        user_selection = BetSelection.objects.filter(
            bet=self.bet,
            selection__market=self.market
        ).first()

        if user_selection and user_selection.selection == self.winning_selection:
            # Calculate winnings
            if self.bet.bet_type == 'single':
                return self.bet.stake * user_selection.odds_taken
            else:
                # For multiple bets, need to check all selections
                all_selections = self.bet.selections.all()
                all_won = True

                for bet_selection in all_selections:
                    # Check if this selection won (simplified logic)
                    selection_settlement = Settlement.objects.filter(
                        bet=self.bet,
                        market=bet_selection.selection.market,
                        winning_selection=bet_selection.selection,
                        status='settled'
                    ).exists()

                    if not selection_settlement:
                        all_won = False
                        break

                if all_won:
                    return self.bet.potential_win

        return Decimal('0.00')

    def settle(self, winning_selection=None, settlement_type='automatic', settled_by=None):
        """Settle the bet"""
        if self.status == 'settled':
            return False

        self.winning_selection = winning_selection
        self.settlement_type = settlement_type
        self.settled_by = settled_by

        # Calculate settlement amount
        self.settlement_amount = self.calculate_settlement_amount()

        # Calculate tax (if applicable)
        tax_rate = Decimal('0.20')  # 20% tax rate (configurable)
        if self.settlement_amount > 0:
            self.tax_amount = self.settlement_amount * tax_rate
            self.net_amount = self.settlement_amount - self.tax_amount
        else:
            self.tax_amount = Decimal('0.00')
            self.net_amount = Decimal('0.00')

        # Update status
        self.status = 'settled'
        self.settled_at = timezone.now()

        # Update bet status
        if self.settlement_amount > 0:
            self.bet.status = 'won'
            self.bet.actual_win = self.net_amount
        else:
            self.bet.status = 'lost'
            self.bet.actual_win = Decimal('0.00')

        self.bet.settled_at = timezone.now()
        self.bet.save()

        self.save()
        return True

    def void_settlement(self, reason='', voided_by=None):
        """Void the settlement"""
        self.status = 'voided'
        self.settlement_reason = reason
        self.settled_by = voided_by
        self.settled_at = timezone.now()

        # Update bet status
        self.bet.status = 'void'
        self.bet.actual_win = self.bet.stake  # Return stake
        self.bet.settled_at = timezone.now()
        self.bet.save()

        self.save()

    def save(self, *args, **kwargs):
        """Auto-generate settlement ID"""
        if not self.settlement_id:
            import uuid
            self.settlement_id = f"ST{str(uuid.uuid4())[:8].upper()}"

        super().save(*args, **kwargs)


class SettlementHistory(models.Model):
    """
    Track settlement changes and corrections
    """
    settlement = models.ForeignKey(Settlement, on_delete=models.CASCADE, related_name='history')

    # Change details
    action = models.CharField(max_length=50)
    old_status = models.CharField(max_length=20, blank=True)
    new_status = models.CharField(max_length=20, blank=True)
    old_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    new_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)

    # Change metadata
    reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)

    # Staff information
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Timestamp
    changed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_settlement_history'
        ordering = ['-changed_at']
        verbose_name = 'Settlement History'
        verbose_name_plural = 'Settlement History'

    def __str__(self):
        return f"{self.settlement.settlement_id} - {self.action}"


class BetSettlementRule(models.Model):
    """
    Rules for automatic bet settlement
    """
    RULE_TYPE_CHOICES = [
        ('match_result', 'Match Result'),
        ('goal_total', 'Goal Total'),
        ('handicap', 'Handicap'),
        ('correct_score', 'Correct Score'),
        ('first_goal', 'First Goal Scorer'),
        ('custom', 'Custom Rule'),
    ]

    bet_type = models.ForeignKey(BetType, on_delete=models.CASCADE, related_name='settlement_rules')
    sport = models.ForeignKey('sports.Sport', on_delete=models.CASCADE, related_name='settlement_rules')

    # Rule details
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    rule_name = models.CharField(max_length=200)
    rule_description = models.TextField(blank=True)

    # Rule configuration
    rule_config = models.JSONField(default=dict, help_text="Rule configuration parameters")

    # Rule status
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0, help_text="Rule execution priority")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_settlement_rule'
        ordering = ['priority', 'rule_name']
        verbose_name = 'Settlement Rule'
        verbose_name_plural = 'Settlement Rules'

    def __str__(self):
        return f"{self.rule_name} - {self.bet_type.name}"

    def apply_rule(self, match, market):
        """Apply settlement rule to a match and market"""
        if not self.is_active:
            return None

        # Implementation would depend on rule type
        # This is a simplified example
        if self.rule_type == 'match_result' and match.is_finished:
            if match.winner == match.home_team:
                return market.selections.filter(name__icontains=match.home_team.name).first()
            elif match.winner == match.away_team:
                return market.selections.filter(name__icontains=match.away_team.name).first()
            elif match.winner == 'draw':
                return market.selections.filter(name__icontains='draw').first()

        return None


class BetCancellationRule(models.Model):
    """
    Rules for bet cancellation policies
    """
    RULE_TYPE_CHOICES = [
        ('time_based', 'Time-based Cancellation'),
        ('match_status', 'Match Status Based'),
        ('odds_change', 'Odds Change Based'),
        ('user_tier', 'User Tier Based'),
        ('bet_amount', 'Bet Amount Based'),
        ('custom', 'Custom Rule'),
    ]

    # Rule identification
    rule_name = models.CharField(max_length=200)
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    description = models.TextField(blank=True)

    # Rule scope
    sports = models.ManyToManyField('sports.Sport', blank=True, help_text="Applicable sports")
    bet_types = models.ManyToManyField(BetType, blank=True, help_text="Applicable bet types")

    # Rule configuration
    rule_config = models.JSONField(default=dict, help_text="Rule configuration parameters")

    # Rule settings
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0)

    # Cancellation fees
    cancellation_fee_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Percentage fee for cancellation"
    )
    minimum_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Minimum cancellation fee"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_cancellation_rule'
        ordering = ['priority', 'rule_name']
        verbose_name = 'Cancellation Rule'
        verbose_name_plural = 'Cancellation Rules'

    def __str__(self):
        return self.rule_name

    def can_cancel_bet(self, bet):
        """Check if bet can be cancelled according to this rule"""
        if not self.is_active:
            return False, "Rule is not active"

        # Check if bet is eligible for cancellation
        if bet.status != 'pending':
            return False, "Bet is not in pending status"

        # Apply rule based on type
        if self.rule_type == 'time_based':
            return self._check_time_based_rule(bet)
        elif self.rule_type == 'match_status':
            return self._check_match_status_rule(bet)
        elif self.rule_type == 'odds_change':
            return self._check_odds_change_rule(bet)
        elif self.rule_type == 'user_tier':
            return self._check_user_tier_rule(bet)
        elif self.rule_type == 'bet_amount':
            return self._check_bet_amount_rule(bet)

        return True, "Rule allows cancellation"

    def _check_time_based_rule(self, bet):
        """Check time-based cancellation rule"""
        config = self.rule_config
        cutoff_minutes = config.get('cutoff_minutes', 60)  # Default 1 hour before match

        # Get earliest match start time from bet selections
        earliest_match = None
        for bet_selection in bet.selections.all():
            match = bet_selection.selection.market.match
            if not earliest_match or match.start_time < earliest_match.start_time:
                earliest_match = match

        if earliest_match:
            time_until_match = (earliest_match.start_time - timezone.now()).total_seconds() / 60
            if time_until_match < cutoff_minutes:
                return False, f"Cannot cancel within {cutoff_minutes} minutes of match start"

        return True, "Time-based rule allows cancellation"

    def _check_match_status_rule(self, bet):
        """Check match status based rule"""
        for bet_selection in bet.selections.all():
            match = bet_selection.selection.market.match
            if match.status != 'scheduled':
                return False, f"Match {match} has already started or finished"

        return True, "Match status allows cancellation"

    def _check_odds_change_rule(self, bet):
        """Check odds change based rule"""
        config = self.rule_config
        max_odds_change = config.get('max_odds_change_percentage', 10)  # 10% change

        for bet_selection in bet.selections.all():
            current_odds = bet_selection.selection.decimal_odds
            taken_odds = bet_selection.odds_taken

            change_percentage = abs((current_odds - taken_odds) / taken_odds) * 100
            if change_percentage > max_odds_change:
                return False, f"Odds have changed by more than {max_odds_change}%"

        return True, "Odds change within acceptable limits"

    def _check_user_tier_rule(self, bet):
        """Check user tier based rule"""
        config = self.rule_config
        allowed_tiers = config.get('allowed_user_tiers', ['premium', 'vip'])

        # This would check user tier (simplified)
        user_tier = getattr(bet.user, 'tier', 'standard')
        if user_tier not in allowed_tiers:
            return False, f"User tier '{user_tier}' not allowed for cancellation"

        return True, "User tier allows cancellation"

    def _check_bet_amount_rule(self, bet):
        """Check bet amount based rule"""
        config = self.rule_config
        min_amount = Decimal(str(config.get('min_cancellable_amount', '0')))
        max_amount = Decimal(str(config.get('max_cancellable_amount', '999999')))

        if bet.stake < min_amount:
            return False, f"Bet amount below minimum cancellable amount of {min_amount}"

        if bet.stake > max_amount:
            return False, f"Bet amount above maximum cancellable amount of {max_amount}"

        return True, "Bet amount within cancellable range"

    def calculate_cancellation_fee(self, bet):
        """Calculate cancellation fee for a bet"""
        percentage_fee = bet.stake * (self.cancellation_fee_percentage / 100)
        return max(percentage_fee, self.minimum_fee)


class BetCancellation(models.Model):
    """
    Track bet cancellations
    """
    CANCELLATION_STATUS_CHOICES = [
        ('requested', 'Cancellation Requested'),
        ('approved', 'Cancellation Approved'),
        ('rejected', 'Cancellation Rejected'),
        ('processed', 'Cancellation Processed'),
    ]

    CANCELLATION_REASON_CHOICES = [
        ('user_request', 'User Request'),
        ('match_postponed', 'Match Postponed'),
        ('match_cancelled', 'Match Cancelled'),
        ('technical_issue', 'Technical Issue'),
        ('odds_error', 'Odds Error'),
        ('admin_action', 'Admin Action'),
    ]

    # Cancellation identification
    cancellation_id = models.CharField(max_length=20, unique=True)

    # Related objects
    bet = models.OneToOneField(Bet, on_delete=models.CASCADE, related_name='cancellation')

    # Cancellation details
    status = models.CharField(max_length=20, choices=CANCELLATION_STATUS_CHOICES, default='requested')
    reason = models.CharField(max_length=20, choices=CANCELLATION_REASON_CHOICES, default='user_request')
    reason_details = models.TextField(blank=True)

    # Financial details
    refund_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    cancellation_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    net_refund = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Processing information
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_cancellations'
    )
    processing_notes = models.TextField(blank=True)

    # Timestamps
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_cancellation'
        ordering = ['-requested_at']
        verbose_name = 'Bet Cancellation'
        verbose_name_plural = 'Bet Cancellations'

    def __str__(self):
        return f"Cancellation {self.cancellation_id} - {self.bet.bet_id}"

    def can_be_cancelled(self):
        """Check if bet can be cancelled based on active rules"""
        rules = BetCancellationRule.objects.filter(is_active=True).order_by('priority')

        for rule in rules:
            can_cancel, reason = rule.can_cancel_bet(self.bet)
            if not can_cancel:
                return False, reason

        return True, "Bet can be cancelled"

    def calculate_refund(self):
        """Calculate refund amount and fees"""
        self.refund_amount = self.bet.stake

        # Calculate cancellation fees from applicable rules
        total_fee = Decimal('0.00')
        rules = BetCancellationRule.objects.filter(is_active=True)

        for rule in rules:
            can_cancel, _ = rule.can_cancel_bet(self.bet)
            if can_cancel:
                fee = rule.calculate_cancellation_fee(self.bet)
                total_fee = max(total_fee, fee)  # Use highest applicable fee

        self.cancellation_fee = total_fee
        self.net_refund = self.refund_amount - self.cancellation_fee

        return self.net_refund

    def approve_cancellation(self, processed_by=None, notes=''):
        """Approve the cancellation"""
        if self.status != 'requested':
            return False

        # Calculate refund
        self.calculate_refund()

        # Update status
        self.status = 'approved'
        self.processed_by = processed_by
        self.processing_notes = notes
        self.processed_at = timezone.now()

        # Update bet status
        self.bet.status = 'cancelled'
        self.bet.settled_at = timezone.now()
        self.bet.save()

        self.save()
        return True

    def reject_cancellation(self, processed_by=None, notes=''):
        """Reject the cancellation"""
        if self.status != 'requested':
            return False

        self.status = 'rejected'
        self.processed_by = processed_by
        self.processing_notes = notes
        self.processed_at = timezone.now()

        self.save()
        return True

    def save(self, *args, **kwargs):
        """Auto-generate cancellation ID"""
        if not self.cancellation_id:
            import uuid
            self.cancellation_id = f"CN{str(uuid.uuid4())[:8].upper()}"

        super().save(*args, **kwargs)


class OddsComparison(models.Model):
    """
    Compare odds across different markets and bookmakers
    """
    match = models.ForeignKey('sports.Match', on_delete=models.CASCADE, related_name='odds_comparisons')
    bet_type = models.ForeignKey(BetType, on_delete=models.CASCADE, related_name='odds_comparisons')

    # Comparison details
    comparison_name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Best odds tracking
    best_home_odds = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    best_draw_odds = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    best_away_odds = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Best odds sources
    best_home_market = models.ForeignKey(
        Market,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='best_home_comparisons'
    )
    best_draw_market = models.ForeignKey(
        Market,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='best_draw_comparisons'
    )
    best_away_market = models.ForeignKey(
        Market,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='best_away_comparisons'
    )

    # Comparison metadata
    total_markets = models.PositiveIntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_odds_comparison'
        ordering = ['-last_updated']
        verbose_name = 'Odds Comparison'
        verbose_name_plural = 'Odds Comparisons'
        unique_together = ['match', 'bet_type']

    def __str__(self):
        return f"Odds Comparison - {self.match} - {self.bet_type.name}"

    def update_best_odds(self):
        """Update best odds from all available markets"""
        markets = Market.objects.filter(
            match=self.match,
            bet_type=self.bet_type,
            status='active'
        ).prefetch_related('selections')

        self.total_markets = markets.count()

        # Reset best odds
        self.best_home_odds = None
        self.best_draw_odds = None
        self.best_away_odds = None
        self.best_home_market = None
        self.best_draw_market = None
        self.best_away_market = None

        for market in markets:
            selections = market.selections.filter(status='active')

            for selection in selections:
                # Determine selection type based on name
                selection_name = selection.name.lower()

                if (self.match.home_team.name.lower() in selection_name or
                    selection_name in ['home', '1']):
                    # Home team selection
                    if (not self.best_home_odds or
                        selection.decimal_odds > self.best_home_odds):
                        self.best_home_odds = selection.decimal_odds
                        self.best_home_market = market

                elif selection_name in ['draw', 'x', 'tie']:
                    # Draw selection
                    if (not self.best_draw_odds or
                        selection.decimal_odds > self.best_draw_odds):
                        self.best_draw_odds = selection.decimal_odds
                        self.best_draw_market = market

                elif (self.match.away_team.name.lower() in selection_name or
                      selection_name in ['away', '2']):
                    # Away team selection
                    if (not self.best_away_odds or
                        selection.decimal_odds > self.best_away_odds):
                        self.best_away_odds = selection.decimal_odds
                        self.best_away_market = market

        self.save()

    @property
    def odds_spread(self):
        """Calculate odds spread (difference between best and worst odds)"""
        all_odds = [self.best_home_odds, self.best_draw_odds, self.best_away_odds]
        valid_odds = [odds for odds in all_odds if odds is not None]

        if len(valid_odds) < 2:
            return 0

        return max(valid_odds) - min(valid_odds)

    @property
    def arbitrage_opportunity(self):
        """Check if there's an arbitrage opportunity"""
        if not all([self.best_home_odds, self.best_away_odds]):
            return False, 0

        # Calculate implied probabilities
        home_prob = 1 / float(self.best_home_odds)
        away_prob = 1 / float(self.best_away_odds)
        draw_prob = 1 / float(self.best_draw_odds) if self.best_draw_odds else 0

        total_prob = home_prob + away_prob + draw_prob

        # Arbitrage exists if total probability < 1
        if total_prob < 1:
            profit_margin = (1 - total_prob) * 100
            return True, round(profit_margin, 2)

        return False, 0


class UserOddsAlert(models.Model):
    """
    User alerts for odds changes
    """
    ALERT_TYPE_CHOICES = [
        ('odds_increase', 'Odds Increase'),
        ('odds_decrease', 'Odds Decrease'),
        ('best_odds', 'Best Odds Available'),
        ('arbitrage', 'Arbitrage Opportunity'),
        ('value_bet', 'Value Bet'),
    ]

    ALERT_STATUS_CHOICES = [
        ('active', 'Active'),
        ('triggered', 'Triggered'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='odds_alerts')
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE, related_name='user_alerts')

    # Alert configuration
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPE_CHOICES)
    target_odds = models.DecimalField(max_digits=10, decimal_places=2)
    current_odds = models.DecimalField(max_digits=10, decimal_places=2)

    # Alert status
    status = models.CharField(max_length=20, choices=ALERT_STATUS_CHOICES, default='active')

    # Alert metadata
    alert_message = models.TextField(blank=True)
    notification_sent = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    triggered_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_user_odds_alert'
        ordering = ['-created_at']
        verbose_name = 'User Odds Alert'
        verbose_name_plural = 'User Odds Alerts'

    def __str__(self):
        return f"Alert - {self.user.email} - {self.selection.name} @ {self.target_odds}"

    def check_alert_condition(self):
        """Check if alert condition is met"""
        if self.status != 'active':
            return False

        current_odds = self.selection.decimal_odds

        if self.alert_type == 'odds_increase':
            return current_odds >= self.target_odds
        elif self.alert_type == 'odds_decrease':
            return current_odds <= self.target_odds
        elif self.alert_type == 'best_odds':
            # Check if this is the best odds available
            comparison = OddsComparison.objects.filter(
                match=self.selection.market.match,
                bet_type=self.selection.market.bet_type
            ).first()

            if comparison:
                best_odds = max([
                    comparison.best_home_odds or 0,
                    comparison.best_draw_odds or 0,
                    comparison.best_away_odds or 0
                ])
                return current_odds >= best_odds

        return False

    def trigger_alert(self):
        """Trigger the alert"""
        if self.status != 'active':
            return False

        self.status = 'triggered'
        self.triggered_at = timezone.now()
        self.current_odds = self.selection.decimal_odds

        # Generate alert message
        self.alert_message = f"Odds alert triggered for {self.selection.name}. "
        self.alert_message += f"Target: {self.target_odds}, Current: {self.current_odds}"

        self.save()

        # TODO: Send notification to user
        return True


class UserFavorite(models.Model):
    """
    User favorite events, teams, and leagues
    """
    FAVORITE_TYPE_CHOICES = [
        ('match', 'Match'),
        ('team', 'Team'),
        ('league', 'League'),
        ('sport', 'Sport'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorites')
    favorite_type = models.CharField(max_length=20, choices=FAVORITE_TYPE_CHOICES)

    # Generic foreign key fields
    match = models.ForeignKey('sports.Match', on_delete=models.CASCADE, null=True, blank=True)
    team = models.ForeignKey('sports.Team', on_delete=models.CASCADE, null=True, blank=True)
    league = models.ForeignKey('sports.League', on_delete=models.CASCADE, null=True, blank=True)
    sport = models.ForeignKey('sports.Sport', on_delete=models.CASCADE, null=True, blank=True)

    # Notification settings
    notify_on_odds_change = models.BooleanField(default=True)
    notify_on_match_start = models.BooleanField(default=True)
    notify_on_goals = models.BooleanField(default=False)
    notify_on_results = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_user_favorite'
        ordering = ['-created_at']
        verbose_name = 'User Favorite'
        verbose_name_plural = 'User Favorites'
        unique_together = [
            ['user', 'match'],
            ['user', 'team'],
            ['user', 'league'],
            ['user', 'sport'],
        ]

    def __str__(self):
        if self.favorite_type == 'match' and self.match:
            return f"{self.user.email} - {self.match}"
        elif self.favorite_type == 'team' and self.team:
            return f"{self.user.email} - {self.team.name}"
        elif self.favorite_type == 'league' and self.league:
            return f"{self.user.email} - {self.league.name}"
        elif self.favorite_type == 'sport' and self.sport:
            return f"{self.user.email} - {self.sport.name}"
        return f"{self.user.email} - {self.favorite_type}"

    @property
    def favorite_object(self):
        """Get the actual favorite object"""
        if self.favorite_type == 'match':
            return self.match
        elif self.favorite_type == 'team':
            return self.team
        elif self.favorite_type == 'league':
            return self.league
        elif self.favorite_type == 'sport':
            return self.sport
        return None

    @property
    def favorite_name(self):
        """Get the name of the favorite object"""
        obj = self.favorite_object
        if obj:
            if hasattr(obj, 'name'):
                return obj.name
            else:
                return str(obj)
        return "Unknown"

    def clean(self):
        """Validate that only one foreign key is set"""
        from django.core.exceptions import ValidationError

        fields = [self.match, self.team, self.league, self.sport]
        set_fields = [f for f in fields if f is not None]

        if len(set_fields) != 1:
            raise ValidationError("Exactly one favorite object must be set.")

        # Set favorite_type based on which field is set
        if self.match:
            self.favorite_type = 'match'
        elif self.team:
            self.favorite_type = 'team'
        elif self.league:
            self.favorite_type = 'league'
        elif self.sport:
            self.favorite_type = 'sport'


class FavoriteNotification(models.Model):
    """
    Notifications for favorite events
    """
    NOTIFICATION_TYPE_CHOICES = [
        ('odds_change', 'Odds Change'),
        ('match_start', 'Match Starting'),
        ('goal_scored', 'Goal Scored'),
        ('match_result', 'Match Result'),
        ('lineup_announced', 'Lineup Announced'),
        ('match_postponed', 'Match Postponed'),
        ('new_market', 'New Market Available'),
    ]

    NOTIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_notifications')
    favorite = models.ForeignKey(UserFavorite, on_delete=models.CASCADE, related_name='notifications')

    # Notification details
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Notification data
    data = models.JSONField(default=dict, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=NOTIFICATION_STATUS_CHOICES, default='pending')

    # Delivery details
    sent_at = models.DateTimeField(null=True, blank=True)
    delivery_method = models.CharField(max_length=50, blank=True)  # email, sms, push, etc.

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_favorite_notification'
        ordering = ['-created_at']
        verbose_name = 'Favorite Notification'
        verbose_name_plural = 'Favorite Notifications'

    def __str__(self):
        return f"{self.user.email} - {self.title}"

    def mark_as_sent(self, delivery_method=''):
        """Mark notification as sent"""
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.delivery_method = delivery_method
        self.save()

    def mark_as_failed(self, reason=''):
        """Mark notification as failed"""
        self.status = 'failed'
        if reason:
            self.data['failure_reason'] = reason
        self.save()


class FavoriteGroup(models.Model):
    """
    User-created groups of favorites
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_groups')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    # Group settings
    is_default = models.BooleanField(default=False)
    is_public = models.BooleanField(default=False)

    # Notification settings for the group
    group_notifications = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_favorite_group'
        ordering = ['name']
        verbose_name = 'Favorite Group'
        verbose_name_plural = 'Favorite Groups'
        unique_together = ['user', 'name']

    def __str__(self):
        return f"{self.user.email} - {self.name}"

    @property
    def favorites_count(self):
        """Get count of favorites in this group"""
        return self.favorites.count()


class FavoriteGroupMembership(models.Model):
    """
    Membership of favorites in groups
    """
    group = models.ForeignKey(FavoriteGroup, on_delete=models.CASCADE, related_name='memberships')
    favorite = models.ForeignKey(UserFavorite, on_delete=models.CASCADE, related_name='group_memberships')

    # Membership details
    added_at = models.DateTimeField(auto_now_add=True)
    added_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='added_group_memberships')

    class Meta:
        db_table = 'betting_favorite_group_membership'
        ordering = ['-added_at']
        verbose_name = 'Favorite Group Membership'
        verbose_name_plural = 'Favorite Group Memberships'
        unique_together = ['group', 'favorite']

    def __str__(self):
        return f"{self.group.name} - {self.favorite}"
