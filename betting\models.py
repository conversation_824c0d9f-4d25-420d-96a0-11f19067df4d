from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth import get_user_model
from decimal import Decimal
from sports.models import Match, Team, Sport

User = get_user_model()


class BetType(models.Model):
    """
    Types of bets available (Match Winner, Over/Under, etc.)
    """
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)

    # Sport compatibility
    sports = models.ManyToManyField(Sport, related_name='bet_types')

    # Bet configuration
    is_active = models.BooleanField(default=True)
    is_live_betting = models.BooleanField(default=False, help_text="Available for live betting")
    display_order = models.PositiveIntegerField(default=0)

    # Odds calculation
    margin_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('5.00'),
        help_text="House margin percentage"
    )

    class Meta:
        db_table = 'betting_bet_type'
        ordering = ['display_order', 'name']
        verbose_name = 'Bet Type'
        verbose_name_plural = 'Bet Types'

    def __str__(self):
        return self.name


class Market(models.Model):
    """
    Betting markets for specific matches
    """
    MARKET_STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('settled', 'Settled'),
        ('cancelled', 'Cancelled'),
    ]

    match = models.ForeignKey(Match, on_delete=models.CASCADE, related_name='markets')
    bet_type = models.ForeignKey(BetType, on_delete=models.CASCADE, related_name='markets')

    # Market details
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=MARKET_STATUS_CHOICES, default='active')

    # Market parameters (e.g., handicap value, over/under line)
    parameters = models.JSONField(default=dict, blank=True)

    # Settlement
    winning_selection = models.CharField(max_length=200, blank=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_market'
        ordering = ['match__start_time', 'bet_type__display_order']
        verbose_name = 'Market'
        verbose_name_plural = 'Markets'
        unique_together = ['match', 'bet_type', 'parameters']

    def __str__(self):
        return f"{self.match} - {self.name}"

    @property
    def is_active(self):
        """Check if market is active for betting"""
        return self.status == 'active' and self.match.betting_enabled

    @property
    def is_live(self):
        """Check if market is available for live betting"""
        return (self.is_active and
                self.match.is_live and
                self.match.live_betting_enabled and
                self.bet_type.is_live_betting)


class Selection(models.Model):
    """
    Individual betting selections within a market
    """
    SELECTION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
    ]

    market = models.ForeignKey(Market, on_delete=models.CASCADE, related_name='selections')

    # Selection details
    name = models.CharField(max_length=200)
    short_name = models.CharField(max_length=50, blank=True)
    status = models.CharField(max_length=20, choices=SELECTION_STATUS_CHOICES, default='active')

    # Odds
    decimal_odds = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('1.01'))]
    )
    fractional_odds = models.CharField(max_length=20, blank=True)
    american_odds = models.IntegerField(null=True, blank=True)

    # Betting limits
    min_bet = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('10.00'))
    max_bet = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('10000.00'))

    # Statistics
    total_bets = models.PositiveIntegerField(default=0)
    total_stake = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_selection'
        ordering = ['market', 'name']
        verbose_name = 'Selection'
        verbose_name_plural = 'Selections'

    def __str__(self):
        return f"{self.market.name} - {self.name} ({self.decimal_odds})"

    @property
    def is_active(self):
        """Check if selection is active for betting"""
        return self.status == 'active' and self.market.is_active

    def calculate_fractional_odds(self):
        """Calculate fractional odds from decimal odds"""
        if self.decimal_odds <= 1:
            return "0/1"

        decimal_part = float(self.decimal_odds) - 1

        # Convert to fraction
        from fractions import Fraction
        frac = Fraction(decimal_part).limit_denominator(100)
        return f"{frac.numerator}/{frac.denominator}"

    def calculate_american_odds(self):
        """Calculate American odds from decimal odds"""
        decimal = float(self.decimal_odds)

        if decimal >= 2.0:
            return int((decimal - 1) * 100)
        else:
            return int(-100 / (decimal - 1))

    def save(self, *args, **kwargs):
        """Auto-calculate other odds formats"""
        if not self.fractional_odds:
            self.fractional_odds = self.calculate_fractional_odds()

        if not self.american_odds:
            self.american_odds = self.calculate_american_odds()

        super().save(*args, **kwargs)


class OddsHistory(models.Model):
    """
    Track odds changes over time
    """
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE, related_name='odds_history')

    # Previous odds
    old_decimal_odds = models.DecimalField(max_digits=10, decimal_places=2)
    new_decimal_odds = models.DecimalField(max_digits=10, decimal_places=2)

    # Change details
    change_reason = models.CharField(max_length=200, blank=True)
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Timestamp
    changed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_odds_history'
        ordering = ['-changed_at']
        verbose_name = 'Odds History'
        verbose_name_plural = 'Odds History'

    def __str__(self):
        return f"{self.selection} - {self.old_decimal_odds} → {self.new_decimal_odds}"

    @property
    def change_percentage(self):
        """Calculate percentage change in odds"""
        if self.old_decimal_odds == 0:
            return 0

        change = ((self.new_decimal_odds - self.old_decimal_odds) / self.old_decimal_odds) * 100
        return round(change, 2)


class Bet(models.Model):
    """
    Individual bets placed by users
    """
    BET_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
        ('cashout', 'Cashed Out'),
    ]

    BET_TYPE_CHOICES = [
        ('single', 'Single Bet'),
        ('multiple', 'Multiple Bet'),
        ('system', 'System Bet'),
    ]

    # User and identification
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bets')
    bet_id = models.CharField(max_length=20, unique=True)

    # Bet details
    bet_type = models.CharField(max_length=20, choices=BET_TYPE_CHOICES, default='single')
    status = models.CharField(max_length=20, choices=BET_STATUS_CHOICES, default='pending')

    # Financial details
    stake = models.DecimalField(max_digits=10, decimal_places=2)
    total_odds = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    potential_win = models.DecimalField(max_digits=15, decimal_places=2)
    actual_win = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Cashout
    cashout_value = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    cashout_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    placed_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_bet'
        ordering = ['-placed_at']
        verbose_name = 'Bet'
        verbose_name_plural = 'Bets'

    def __str__(self):
        return f"Bet {self.bet_id} - {self.user.email} - {self.stake}"

    @property
    def is_active(self):
        """Check if bet is still active"""
        return self.status == 'pending'

    @property
    def profit_loss(self):
        """Calculate profit or loss"""
        if self.status == 'won':
            return self.actual_win - self.stake
        elif self.status == 'lost':
            return -self.stake
        elif self.status == 'cashout':
            return self.cashout_value - self.stake if self.cashout_value else Decimal('0.00')
        return Decimal('0.00')

    def calculate_potential_win(self):
        """Calculate potential winnings"""
        return self.stake * self.total_odds

    def save(self, *args, **kwargs):
        """Auto-calculate potential win"""
        if not self.potential_win:
            self.potential_win = self.calculate_potential_win()

        # Generate bet ID if not provided
        if not self.bet_id:
            import uuid
            self.bet_id = str(uuid.uuid4())[:8].upper()

        super().save(*args, **kwargs)


class BetSelection(models.Model):
    """
    Individual selections within a bet
    """
    SELECTION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
    ]

    bet = models.ForeignKey(Bet, on_delete=models.CASCADE, related_name='selections')
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE, related_name='bet_selections')

    # Odds at time of bet
    odds_taken = models.DecimalField(max_digits=10, decimal_places=2)

    # Status
    status = models.CharField(max_length=20, choices=SELECTION_STATUS_CHOICES, default='pending')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_bet_selection'
        ordering = ['created_at']
        verbose_name = 'Bet Selection'
        verbose_name_plural = 'Bet Selections'

    def __str__(self):
        return f"{self.bet.bet_id} - {self.selection.name} @ {self.odds_taken}"


class BetSlip(models.Model):
    """
    Temporary storage for bet slips before placement
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bet_slips')
    session_key = models.CharField(max_length=40, blank=True)

    # Bet slip details
    selections = models.ManyToManyField(Selection, through='BetSlipSelection')
    bet_type = models.CharField(max_length=20, default='single')
    stake = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Calculated values
    total_odds = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    potential_win = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_bet_slip'
        ordering = ['-updated_at']
        verbose_name = 'Bet Slip'
        verbose_name_plural = 'Bet Slips'

    def __str__(self):
        return f"Bet Slip - {self.user.email} - {self.selections.count()} selections"

    def calculate_total_odds(self):
        """Calculate total odds for multiple selections"""
        if self.bet_type == 'single':
            # For single bets, return the odds of the first selection
            first_selection = self.betslipselection_set.first()
            return first_selection.selection.decimal_odds if first_selection else Decimal('1.00')
        else:
            # For multiple bets, multiply all odds
            total = Decimal('1.00')
            for bet_slip_selection in self.betslipselection_set.all():
                total *= bet_slip_selection.selection.decimal_odds
            return total

    def calculate_potential_win(self):
        """Calculate potential winnings"""
        return self.stake * self.total_odds

    def update_calculations(self):
        """Update calculated values"""
        self.total_odds = self.calculate_total_odds()
        self.potential_win = self.calculate_potential_win()
        self.save()


class BetSlipSelection(models.Model):
    """
    Through model for bet slip selections
    """
    bet_slip = models.ForeignKey(BetSlip, on_delete=models.CASCADE)
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE)

    # Order in bet slip
    order = models.PositiveIntegerField(default=0)

    # Timestamps
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_bet_slip_selection'
        ordering = ['order', 'added_at']
        unique_together = ['bet_slip', 'selection']
        verbose_name = 'Bet Slip Selection'
        verbose_name_plural = 'Bet Slip Selections'

    def __str__(self):
        return f"{self.bet_slip} - {self.selection.name}"


class Settlement(models.Model):
    """
    Bet settlement tracking and management
    """
    SETTLEMENT_STATUS_CHOICES = [
        ('pending', 'Pending Settlement'),
        ('settled', 'Settled'),
        ('voided', 'Voided'),
        ('disputed', 'Disputed'),
        ('reviewed', 'Under Review'),
    ]

    SETTLEMENT_TYPE_CHOICES = [
        ('automatic', 'Automatic Settlement'),
        ('manual', 'Manual Settlement'),
        ('correction', 'Settlement Correction'),
        ('void', 'Void Settlement'),
    ]

    # Settlement identification
    settlement_id = models.CharField(max_length=20, unique=True)

    # Related objects
    bet = models.ForeignKey(Bet, on_delete=models.CASCADE, related_name='settlements')
    market = models.ForeignKey(Market, on_delete=models.CASCADE, related_name='settlements')

    # Settlement details
    settlement_type = models.CharField(max_length=20, choices=SETTLEMENT_TYPE_CHOICES, default='automatic')
    status = models.CharField(max_length=20, choices=SETTLEMENT_STATUS_CHOICES, default='pending')

    # Settlement results
    winning_selection = models.ForeignKey(
        Selection,
        on_delete=models.CASCADE,
        related_name='winning_settlements',
        null=True,
        blank=True
    )

    # Financial details
    settlement_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    net_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Settlement metadata
    settlement_reason = models.TextField(blank=True)
    settlement_notes = models.TextField(blank=True)
    settlement_data = models.JSONField(default=dict, blank=True, help_text="Additional settlement data")

    # Staff information
    settled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='settlements_made'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_settlement'
        ordering = ['-created_at']
        verbose_name = 'Settlement'
        verbose_name_plural = 'Settlements'

    def __str__(self):
        return f"Settlement {self.settlement_id} - {self.bet.bet_id}"

    @property
    def is_winning(self):
        """Check if this is a winning settlement"""
        return self.settlement_amount > 0

    @property
    def is_settled(self):
        """Check if settlement is completed"""
        return self.status == 'settled'

    def calculate_settlement_amount(self):
        """Calculate settlement amount based on bet selections"""
        if not self.winning_selection:
            return Decimal('0.00')

        # Check if user's bet selection matches winning selection
        user_selection = BetSelection.objects.filter(
            bet=self.bet,
            selection__market=self.market
        ).first()

        if user_selection and user_selection.selection == self.winning_selection:
            # Calculate winnings
            if self.bet.bet_type == 'single':
                return self.bet.stake * user_selection.odds_taken
            else:
                # For multiple bets, need to check all selections
                all_selections = self.bet.selections.all()
                all_won = True

                for bet_selection in all_selections:
                    # Check if this selection won (simplified logic)
                    selection_settlement = Settlement.objects.filter(
                        bet=self.bet,
                        market=bet_selection.selection.market,
                        winning_selection=bet_selection.selection,
                        status='settled'
                    ).exists()

                    if not selection_settlement:
                        all_won = False
                        break

                if all_won:
                    return self.bet.potential_win

        return Decimal('0.00')

    def settle(self, winning_selection=None, settlement_type='automatic', settled_by=None):
        """Settle the bet"""
        if self.status == 'settled':
            return False

        self.winning_selection = winning_selection
        self.settlement_type = settlement_type
        self.settled_by = settled_by

        # Calculate settlement amount
        self.settlement_amount = self.calculate_settlement_amount()

        # Calculate tax (if applicable)
        tax_rate = Decimal('0.20')  # 20% tax rate (configurable)
        if self.settlement_amount > 0:
            self.tax_amount = self.settlement_amount * tax_rate
            self.net_amount = self.settlement_amount - self.tax_amount
        else:
            self.tax_amount = Decimal('0.00')
            self.net_amount = Decimal('0.00')

        # Update status
        self.status = 'settled'
        self.settled_at = timezone.now()

        # Update bet status
        if self.settlement_amount > 0:
            self.bet.status = 'won'
            self.bet.actual_win = self.net_amount
        else:
            self.bet.status = 'lost'
            self.bet.actual_win = Decimal('0.00')

        self.bet.settled_at = timezone.now()
        self.bet.save()

        self.save()
        return True

    def void_settlement(self, reason='', voided_by=None):
        """Void the settlement"""
        self.status = 'voided'
        self.settlement_reason = reason
        self.settled_by = voided_by
        self.settled_at = timezone.now()

        # Update bet status
        self.bet.status = 'void'
        self.bet.actual_win = self.bet.stake  # Return stake
        self.bet.settled_at = timezone.now()
        self.bet.save()

        self.save()

    def save(self, *args, **kwargs):
        """Auto-generate settlement ID"""
        if not self.settlement_id:
            import uuid
            self.settlement_id = f"ST{str(uuid.uuid4())[:8].upper()}"

        super().save(*args, **kwargs)


class SettlementHistory(models.Model):
    """
    Track settlement changes and corrections
    """
    settlement = models.ForeignKey(Settlement, on_delete=models.CASCADE, related_name='history')

    # Change details
    action = models.CharField(max_length=50)
    old_status = models.CharField(max_length=20, blank=True)
    new_status = models.CharField(max_length=20, blank=True)
    old_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    new_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)

    # Change metadata
    reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)

    # Staff information
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Timestamp
    changed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_settlement_history'
        ordering = ['-changed_at']
        verbose_name = 'Settlement History'
        verbose_name_plural = 'Settlement History'

    def __str__(self):
        return f"{self.settlement.settlement_id} - {self.action}"


class BetSettlementRule(models.Model):
    """
    Rules for automatic bet settlement
    """
    RULE_TYPE_CHOICES = [
        ('match_result', 'Match Result'),
        ('goal_total', 'Goal Total'),
        ('handicap', 'Handicap'),
        ('correct_score', 'Correct Score'),
        ('first_goal', 'First Goal Scorer'),
        ('custom', 'Custom Rule'),
    ]

    bet_type = models.ForeignKey(BetType, on_delete=models.CASCADE, related_name='settlement_rules')
    sport = models.ForeignKey('sports.Sport', on_delete=models.CASCADE, related_name='settlement_rules')

    # Rule details
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    rule_name = models.CharField(max_length=200)
    rule_description = models.TextField(blank=True)

    # Rule configuration
    rule_config = models.JSONField(default=dict, help_text="Rule configuration parameters")

    # Rule status
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0, help_text="Rule execution priority")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_settlement_rule'
        ordering = ['priority', 'rule_name']
        verbose_name = 'Settlement Rule'
        verbose_name_plural = 'Settlement Rules'

    def __str__(self):
        return f"{self.rule_name} - {self.bet_type.name}"

    def apply_rule(self, match, market):
        """Apply settlement rule to a match and market"""
        if not self.is_active:
            return None

        # Implementation would depend on rule type
        # This is a simplified example
        if self.rule_type == 'match_result' and match.is_finished:
            if match.winner == match.home_team:
                return market.selections.filter(name__icontains=match.home_team.name).first()
            elif match.winner == match.away_team:
                return market.selections.filter(name__icontains=match.away_team.name).first()
            elif match.winner == 'draw':
                return market.selections.filter(name__icontains='draw').first()

        return None


class BetCancellationRule(models.Model):
    """
    Rules for bet cancellation policies
    """
    RULE_TYPE_CHOICES = [
        ('time_based', 'Time-based Cancellation'),
        ('match_status', 'Match Status Based'),
        ('odds_change', 'Odds Change Based'),
        ('user_tier', 'User Tier Based'),
        ('bet_amount', 'Bet Amount Based'),
        ('custom', 'Custom Rule'),
    ]

    # Rule identification
    rule_name = models.CharField(max_length=200)
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    description = models.TextField(blank=True)

    # Rule scope
    sports = models.ManyToManyField('sports.Sport', blank=True, help_text="Applicable sports")
    bet_types = models.ManyToManyField(BetType, blank=True, help_text="Applicable bet types")

    # Rule configuration
    rule_config = models.JSONField(default=dict, help_text="Rule configuration parameters")

    # Rule settings
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=0)

    # Cancellation fees
    cancellation_fee_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Percentage fee for cancellation"
    )
    minimum_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Minimum cancellation fee"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_cancellation_rule'
        ordering = ['priority', 'rule_name']
        verbose_name = 'Cancellation Rule'
        verbose_name_plural = 'Cancellation Rules'

    def __str__(self):
        return self.rule_name

    def can_cancel_bet(self, bet):
        """Check if bet can be cancelled according to this rule"""
        if not self.is_active:
            return False, "Rule is not active"

        # Check if bet is eligible for cancellation
        if bet.status != 'pending':
            return False, "Bet is not in pending status"

        # Apply rule based on type
        if self.rule_type == 'time_based':
            return self._check_time_based_rule(bet)
        elif self.rule_type == 'match_status':
            return self._check_match_status_rule(bet)
        elif self.rule_type == 'odds_change':
            return self._check_odds_change_rule(bet)
        elif self.rule_type == 'user_tier':
            return self._check_user_tier_rule(bet)
        elif self.rule_type == 'bet_amount':
            return self._check_bet_amount_rule(bet)

        return True, "Rule allows cancellation"

    def _check_time_based_rule(self, bet):
        """Check time-based cancellation rule"""
        config = self.rule_config
        cutoff_minutes = config.get('cutoff_minutes', 60)  # Default 1 hour before match

        # Get earliest match start time from bet selections
        earliest_match = None
        for bet_selection in bet.selections.all():
            match = bet_selection.selection.market.match
            if not earliest_match or match.start_time < earliest_match.start_time:
                earliest_match = match

        if earliest_match:
            time_until_match = (earliest_match.start_time - timezone.now()).total_seconds() / 60
            if time_until_match < cutoff_minutes:
                return False, f"Cannot cancel within {cutoff_minutes} minutes of match start"

        return True, "Time-based rule allows cancellation"

    def _check_match_status_rule(self, bet):
        """Check match status based rule"""
        for bet_selection in bet.selections.all():
            match = bet_selection.selection.market.match
            if match.status != 'scheduled':
                return False, f"Match {match} has already started or finished"

        return True, "Match status allows cancellation"

    def _check_odds_change_rule(self, bet):
        """Check odds change based rule"""
        config = self.rule_config
        max_odds_change = config.get('max_odds_change_percentage', 10)  # 10% change

        for bet_selection in bet.selections.all():
            current_odds = bet_selection.selection.decimal_odds
            taken_odds = bet_selection.odds_taken

            change_percentage = abs((current_odds - taken_odds) / taken_odds) * 100
            if change_percentage > max_odds_change:
                return False, f"Odds have changed by more than {max_odds_change}%"

        return True, "Odds change within acceptable limits"

    def _check_user_tier_rule(self, bet):
        """Check user tier based rule"""
        config = self.rule_config
        allowed_tiers = config.get('allowed_user_tiers', ['premium', 'vip'])

        # This would check user tier (simplified)
        user_tier = getattr(bet.user, 'tier', 'standard')
        if user_tier not in allowed_tiers:
            return False, f"User tier '{user_tier}' not allowed for cancellation"

        return True, "User tier allows cancellation"

    def _check_bet_amount_rule(self, bet):
        """Check bet amount based rule"""
        config = self.rule_config
        min_amount = Decimal(str(config.get('min_cancellable_amount', '0')))
        max_amount = Decimal(str(config.get('max_cancellable_amount', '999999')))

        if bet.stake < min_amount:
            return False, f"Bet amount below minimum cancellable amount of {min_amount}"

        if bet.stake > max_amount:
            return False, f"Bet amount above maximum cancellable amount of {max_amount}"

        return True, "Bet amount within cancellable range"

    def calculate_cancellation_fee(self, bet):
        """Calculate cancellation fee for a bet"""
        percentage_fee = bet.stake * (self.cancellation_fee_percentage / 100)
        return max(percentage_fee, self.minimum_fee)


class BetCancellation(models.Model):
    """
    Track bet cancellations
    """
    CANCELLATION_STATUS_CHOICES = [
        ('requested', 'Cancellation Requested'),
        ('approved', 'Cancellation Approved'),
        ('rejected', 'Cancellation Rejected'),
        ('processed', 'Cancellation Processed'),
    ]

    CANCELLATION_REASON_CHOICES = [
        ('user_request', 'User Request'),
        ('match_postponed', 'Match Postponed'),
        ('match_cancelled', 'Match Cancelled'),
        ('technical_issue', 'Technical Issue'),
        ('odds_error', 'Odds Error'),
        ('admin_action', 'Admin Action'),
    ]

    # Cancellation identification
    cancellation_id = models.CharField(max_length=20, unique=True)

    # Related objects
    bet = models.OneToOneField(Bet, on_delete=models.CASCADE, related_name='cancellation')

    # Cancellation details
    status = models.CharField(max_length=20, choices=CANCELLATION_STATUS_CHOICES, default='requested')
    reason = models.CharField(max_length=20, choices=CANCELLATION_REASON_CHOICES, default='user_request')
    reason_details = models.TextField(blank=True)

    # Financial details
    refund_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    cancellation_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    net_refund = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Processing information
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_cancellations'
    )
    processing_notes = models.TextField(blank=True)

    # Timestamps
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_cancellation'
        ordering = ['-requested_at']
        verbose_name = 'Bet Cancellation'
        verbose_name_plural = 'Bet Cancellations'

    def __str__(self):
        return f"Cancellation {self.cancellation_id} - {self.bet.bet_id}"

    def can_be_cancelled(self):
        """Check if bet can be cancelled based on active rules"""
        rules = BetCancellationRule.objects.filter(is_active=True).order_by('priority')

        for rule in rules:
            can_cancel, reason = rule.can_cancel_bet(self.bet)
            if not can_cancel:
                return False, reason

        return True, "Bet can be cancelled"

    def calculate_refund(self):
        """Calculate refund amount and fees"""
        self.refund_amount = self.bet.stake

        # Calculate cancellation fees from applicable rules
        total_fee = Decimal('0.00')
        rules = BetCancellationRule.objects.filter(is_active=True)

        for rule in rules:
            can_cancel, _ = rule.can_cancel_bet(self.bet)
            if can_cancel:
                fee = rule.calculate_cancellation_fee(self.bet)
                total_fee = max(total_fee, fee)  # Use highest applicable fee

        self.cancellation_fee = total_fee
        self.net_refund = self.refund_amount - self.cancellation_fee

        return self.net_refund

    def approve_cancellation(self, processed_by=None, notes=''):
        """Approve the cancellation"""
        if self.status != 'requested':
            return False

        # Calculate refund
        self.calculate_refund()

        # Update status
        self.status = 'approved'
        self.processed_by = processed_by
        self.processing_notes = notes
        self.processed_at = timezone.now()

        # Update bet status
        self.bet.status = 'cancelled'
        self.bet.settled_at = timezone.now()
        self.bet.save()

        self.save()
        return True

    def reject_cancellation(self, processed_by=None, notes=''):
        """Reject the cancellation"""
        if self.status != 'requested':
            return False

        self.status = 'rejected'
        self.processed_by = processed_by
        self.processing_notes = notes
        self.processed_at = timezone.now()

        self.save()
        return True

    def save(self, *args, **kwargs):
        """Auto-generate cancellation ID"""
        if not self.cancellation_id:
            import uuid
            self.cancellation_id = f"CN{str(uuid.uuid4())[:8].upper()}"

        super().save(*args, **kwargs)


class OddsComparison(models.Model):
    """
    Compare odds across different markets and bookmakers
    """
    match = models.ForeignKey('sports.Match', on_delete=models.CASCADE, related_name='odds_comparisons')
    bet_type = models.ForeignKey(BetType, on_delete=models.CASCADE, related_name='odds_comparisons')

    # Comparison details
    comparison_name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Best odds tracking
    best_home_odds = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    best_draw_odds = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    best_away_odds = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Best odds sources
    best_home_market = models.ForeignKey(
        Market,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='best_home_comparisons'
    )
    best_draw_market = models.ForeignKey(
        Market,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='best_draw_comparisons'
    )
    best_away_market = models.ForeignKey(
        Market,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='best_away_comparisons'
    )

    # Comparison metadata
    total_markets = models.PositiveIntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_odds_comparison'
        ordering = ['-last_updated']
        verbose_name = 'Odds Comparison'
        verbose_name_plural = 'Odds Comparisons'
        unique_together = ['match', 'bet_type']

    def __str__(self):
        return f"Odds Comparison - {self.match} - {self.bet_type.name}"

    def update_best_odds(self):
        """Update best odds from all available markets"""
        markets = Market.objects.filter(
            match=self.match,
            bet_type=self.bet_type,
            status='active'
        ).prefetch_related('selections')

        self.total_markets = markets.count()

        # Reset best odds
        self.best_home_odds = None
        self.best_draw_odds = None
        self.best_away_odds = None
        self.best_home_market = None
        self.best_draw_market = None
        self.best_away_market = None

        for market in markets:
            selections = market.selections.filter(status='active')

            for selection in selections:
                # Determine selection type based on name
                selection_name = selection.name.lower()

                if (self.match.home_team.name.lower() in selection_name or
                    selection_name in ['home', '1']):
                    # Home team selection
                    if (not self.best_home_odds or
                        selection.decimal_odds > self.best_home_odds):
                        self.best_home_odds = selection.decimal_odds
                        self.best_home_market = market

                elif selection_name in ['draw', 'x', 'tie']:
                    # Draw selection
                    if (not self.best_draw_odds or
                        selection.decimal_odds > self.best_draw_odds):
                        self.best_draw_odds = selection.decimal_odds
                        self.best_draw_market = market

                elif (self.match.away_team.name.lower() in selection_name or
                      selection_name in ['away', '2']):
                    # Away team selection
                    if (not self.best_away_odds or
                        selection.decimal_odds > self.best_away_odds):
                        self.best_away_odds = selection.decimal_odds
                        self.best_away_market = market

        self.save()

    @property
    def odds_spread(self):
        """Calculate odds spread (difference between best and worst odds)"""
        all_odds = [self.best_home_odds, self.best_draw_odds, self.best_away_odds]
        valid_odds = [odds for odds in all_odds if odds is not None]

        if len(valid_odds) < 2:
            return 0

        return max(valid_odds) - min(valid_odds)

    @property
    def arbitrage_opportunity(self):
        """Check if there's an arbitrage opportunity"""
        if not all([self.best_home_odds, self.best_away_odds]):
            return False, 0

        # Calculate implied probabilities
        home_prob = 1 / float(self.best_home_odds)
        away_prob = 1 / float(self.best_away_odds)
        draw_prob = 1 / float(self.best_draw_odds) if self.best_draw_odds else 0

        total_prob = home_prob + away_prob + draw_prob

        # Arbitrage exists if total probability < 1
        if total_prob < 1:
            profit_margin = (1 - total_prob) * 100
            return True, round(profit_margin, 2)

        return False, 0


class UserOddsAlert(models.Model):
    """
    User alerts for odds changes
    """
    ALERT_TYPE_CHOICES = [
        ('odds_increase', 'Odds Increase'),
        ('odds_decrease', 'Odds Decrease'),
        ('best_odds', 'Best Odds Available'),
        ('arbitrage', 'Arbitrage Opportunity'),
        ('value_bet', 'Value Bet'),
    ]

    ALERT_STATUS_CHOICES = [
        ('active', 'Active'),
        ('triggered', 'Triggered'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='odds_alerts')
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE, related_name='user_alerts')

    # Alert configuration
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPE_CHOICES)
    target_odds = models.DecimalField(max_digits=10, decimal_places=2)
    current_odds = models.DecimalField(max_digits=10, decimal_places=2)

    # Alert status
    status = models.CharField(max_length=20, choices=ALERT_STATUS_CHOICES, default='active')

    # Alert metadata
    alert_message = models.TextField(blank=True)
    notification_sent = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    triggered_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_user_odds_alert'
        ordering = ['-created_at']
        verbose_name = 'User Odds Alert'
        verbose_name_plural = 'User Odds Alerts'

    def __str__(self):
        return f"Alert - {self.user.email} - {self.selection.name} @ {self.target_odds}"

    def check_alert_condition(self):
        """Check if alert condition is met"""
        if self.status != 'active':
            return False

        current_odds = self.selection.decimal_odds

        if self.alert_type == 'odds_increase':
            return current_odds >= self.target_odds
        elif self.alert_type == 'odds_decrease':
            return current_odds <= self.target_odds
        elif self.alert_type == 'best_odds':
            # Check if this is the best odds available
            comparison = OddsComparison.objects.filter(
                match=self.selection.market.match,
                bet_type=self.selection.market.bet_type
            ).first()

            if comparison:
                best_odds = max([
                    comparison.best_home_odds or 0,
                    comparison.best_draw_odds or 0,
                    comparison.best_away_odds or 0
                ])
                return current_odds >= best_odds

        return False

    def trigger_alert(self):
        """Trigger the alert"""
        if self.status != 'active':
            return False

        self.status = 'triggered'
        self.triggered_at = timezone.now()
        self.current_odds = self.selection.decimal_odds

        # Generate alert message
        self.alert_message = f"Odds alert triggered for {self.selection.name}. "
        self.alert_message += f"Target: {self.target_odds}, Current: {self.current_odds}"

        self.save()

        # TODO: Send notification to user
        return True


class UserFavorite(models.Model):
    """
    User favorite events, teams, and leagues
    """
    FAVORITE_TYPE_CHOICES = [
        ('match', 'Match'),
        ('team', 'Team'),
        ('league', 'League'),
        ('sport', 'Sport'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorites')
    favorite_type = models.CharField(max_length=20, choices=FAVORITE_TYPE_CHOICES)

    # Generic foreign key fields
    match = models.ForeignKey('sports.Match', on_delete=models.CASCADE, null=True, blank=True)
    team = models.ForeignKey('sports.Team', on_delete=models.CASCADE, null=True, blank=True)
    league = models.ForeignKey('sports.League', on_delete=models.CASCADE, null=True, blank=True)
    sport = models.ForeignKey('sports.Sport', on_delete=models.CASCADE, null=True, blank=True)

    # Notification settings
    notify_on_odds_change = models.BooleanField(default=True)
    notify_on_match_start = models.BooleanField(default=True)
    notify_on_goals = models.BooleanField(default=False)
    notify_on_results = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_user_favorite'
        ordering = ['-created_at']
        verbose_name = 'User Favorite'
        verbose_name_plural = 'User Favorites'
        unique_together = [
            ['user', 'match'],
            ['user', 'team'],
            ['user', 'league'],
            ['user', 'sport'],
        ]

    def __str__(self):
        if self.favorite_type == 'match' and self.match:
            return f"{self.user.email} - {self.match}"
        elif self.favorite_type == 'team' and self.team:
            return f"{self.user.email} - {self.team.name}"
        elif self.favorite_type == 'league' and self.league:
            return f"{self.user.email} - {self.league.name}"
        elif self.favorite_type == 'sport' and self.sport:
            return f"{self.user.email} - {self.sport.name}"
        return f"{self.user.email} - {self.favorite_type}"

    @property
    def favorite_object(self):
        """Get the actual favorite object"""
        if self.favorite_type == 'match':
            return self.match
        elif self.favorite_type == 'team':
            return self.team
        elif self.favorite_type == 'league':
            return self.league
        elif self.favorite_type == 'sport':
            return self.sport
        return None

    @property
    def favorite_name(self):
        """Get the name of the favorite object"""
        obj = self.favorite_object
        if obj:
            if hasattr(obj, 'name'):
                return obj.name
            else:
                return str(obj)
        return "Unknown"

    def clean(self):
        """Validate that only one foreign key is set"""
        from django.core.exceptions import ValidationError

        fields = [self.match, self.team, self.league, self.sport]
        set_fields = [f for f in fields if f is not None]

        if len(set_fields) != 1:
            raise ValidationError("Exactly one favorite object must be set.")

        # Set favorite_type based on which field is set
        if self.match:
            self.favorite_type = 'match'
        elif self.team:
            self.favorite_type = 'team'
        elif self.league:
            self.favorite_type = 'league'
        elif self.sport:
            self.favorite_type = 'sport'


class FavoriteNotification(models.Model):
    """
    Notifications for favorite events
    """
    NOTIFICATION_TYPE_CHOICES = [
        ('odds_change', 'Odds Change'),
        ('match_start', 'Match Starting'),
        ('goal_scored', 'Goal Scored'),
        ('match_result', 'Match Result'),
        ('lineup_announced', 'Lineup Announced'),
        ('match_postponed', 'Match Postponed'),
        ('new_market', 'New Market Available'),
    ]

    NOTIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_notifications')
    favorite = models.ForeignKey(UserFavorite, on_delete=models.CASCADE, related_name='notifications')

    # Notification details
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Notification data
    data = models.JSONField(default=dict, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=NOTIFICATION_STATUS_CHOICES, default='pending')

    # Delivery details
    sent_at = models.DateTimeField(null=True, blank=True)
    delivery_method = models.CharField(max_length=50, blank=True)  # email, sms, push, etc.

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_favorite_notification'
        ordering = ['-created_at']
        verbose_name = 'Favorite Notification'
        verbose_name_plural = 'Favorite Notifications'

    def __str__(self):
        return f"{self.user.email} - {self.title}"

    def mark_as_sent(self, delivery_method=''):
        """Mark notification as sent"""
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.delivery_method = delivery_method
        self.save()

    def mark_as_failed(self, reason=''):
        """Mark notification as failed"""
        self.status = 'failed'
        if reason:
            self.data['failure_reason'] = reason
        self.save()


class FavoriteGroup(models.Model):
    """
    User-created groups of favorites
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_groups')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    # Group settings
    is_default = models.BooleanField(default=False)
    is_public = models.BooleanField(default=False)

    # Notification settings for the group
    group_notifications = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_favorite_group'
        ordering = ['name']
        verbose_name = 'Favorite Group'
        verbose_name_plural = 'Favorite Groups'
        unique_together = ['user', 'name']

    def __str__(self):
        return f"{self.user.email} - {self.name}"

    @property
    def favorites_count(self):
        """Get count of favorites in this group"""
        return self.favorites.count()


class FavoriteGroupMembership(models.Model):
    """
    Membership of favorites in groups
    """
    group = models.ForeignKey(FavoriteGroup, on_delete=models.CASCADE, related_name='memberships')
    favorite = models.ForeignKey(UserFavorite, on_delete=models.CASCADE, related_name='group_memberships')

    # Membership details
    added_at = models.DateTimeField(auto_now_add=True)
    added_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='added_group_memberships')

    class Meta:
        db_table = 'betting_favorite_group_membership'
        ordering = ['-added_at']
        verbose_name = 'Favorite Group Membership'
        verbose_name_plural = 'Favorite Group Memberships'
        unique_together = ['group', 'favorite']

    def __str__(self):
        return f"{self.group.name} - {self.favorite}"


class LiveBetNotification(models.Model):
    """
    Real-time notifications for live betting events
    """
    NOTIFICATION_TYPE_CHOICES = [
        ('odds_change', 'Odds Change'),
        ('new_market', 'New Market Available'),
        ('market_suspended', 'Market Suspended'),
        ('market_reopened', 'Market Reopened'),
        ('goal_scored', 'Goal Scored'),
        ('red_card', 'Red Card'),
        ('penalty_awarded', 'Penalty Awarded'),
        ('match_event', 'Match Event'),
        ('bet_won', 'Bet Won'),
        ('bet_lost', 'Bet Lost'),
        ('cashout_available', 'Cash Out Available'),
        ('cashout_unavailable', 'Cash Out Unavailable'),
        ('live_stream_available', 'Live Stream Available'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    DELIVERY_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
    ]

    # Target user or broadcast
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='live_notifications')
    is_broadcast = models.BooleanField(default=False)  # If True, send to all users

    # Related objects
    match = models.ForeignKey('sports.Match', on_delete=models.CASCADE, null=True, blank=True)
    market = models.ForeignKey(Market, on_delete=models.CASCADE, null=True, blank=True)
    selection = models.ForeignKey(Selection, on_delete=models.CASCADE, null=True, blank=True)
    bet = models.ForeignKey(Bet, on_delete=models.CASCADE, null=True, blank=True)

    # Notification details
    notification_type = models.CharField(max_length=30, choices=NOTIFICATION_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    message = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')

    # Notification data
    data = models.JSONField(default=dict, blank=True)

    # Delivery settings
    send_push = models.BooleanField(default=True)
    send_email = models.BooleanField(default=False)
    send_sms = models.BooleanField(default=False)

    # Status tracking
    delivery_status = models.CharField(max_length=20, choices=DELIVERY_STATUS_CHOICES, default='pending')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    scheduled_at = models.DateTimeField(null=True, blank=True)  # For delayed notifications
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_live_bet_notification'
        ordering = ['-created_at']
        verbose_name = 'Live Bet Notification'
        verbose_name_plural = 'Live Bet Notifications'
        indexes = [
            models.Index(fields=['user', 'delivery_status']),
            models.Index(fields=['notification_type', 'created_at']),
            models.Index(fields=['match', 'created_at']),
        ]

    def __str__(self):
        target = self.user.email if self.user else "Broadcast"
        return f"{target} - {self.title}"

    def mark_as_sent(self):
        """Mark notification as sent"""
        self.delivery_status = 'sent'
        self.sent_at = timezone.now()
        self.save()

    def mark_as_delivered(self):
        """Mark notification as delivered"""
        self.delivery_status = 'delivered'
        self.delivered_at = timezone.now()
        self.save()

    def mark_as_failed(self, reason=''):
        """Mark notification as failed"""
        self.delivery_status = 'failed'
        if reason:
            self.data['failure_reason'] = reason
        self.save()

    @property
    def is_expired(self):
        """Check if notification has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def should_send_now(self):
        """Check if notification should be sent now"""
        if self.delivery_status != 'pending':
            return False

        if self.is_expired:
            return False

        if self.scheduled_at:
            return timezone.now() >= self.scheduled_at

        return True


class UserNotificationPreference(models.Model):
    """
    User preferences for live bet notifications
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_preferences')

    # General preferences
    enable_live_notifications = models.BooleanField(default=True)
    enable_push_notifications = models.BooleanField(default=True)
    enable_email_notifications = models.BooleanField(default=False)
    enable_sms_notifications = models.BooleanField(default=False)

    # Notification type preferences
    notify_odds_changes = models.BooleanField(default=True)
    notify_new_markets = models.BooleanField(default=True)
    notify_market_suspensions = models.BooleanField(default=True)
    notify_match_events = models.BooleanField(default=True)
    notify_bet_results = models.BooleanField(default=True)
    notify_cashout_opportunities = models.BooleanField(default=True)
    notify_live_streams = models.BooleanField(default=False)

    # Frequency settings
    max_notifications_per_hour = models.PositiveIntegerField(default=10)
    quiet_hours_start = models.TimeField(null=True, blank=True)  # e.g., 22:00
    quiet_hours_end = models.TimeField(null=True, blank=True)    # e.g., 08:00

    # Match-specific preferences
    only_favorite_matches = models.BooleanField(default=False)
    only_active_bets = models.BooleanField(default=False)
    minimum_odds_change = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.10'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_user_notification_preference'
        verbose_name = 'User Notification Preference'
        verbose_name_plural = 'User Notification Preferences'

    def __str__(self):
        return f"{self.user.email} - Notification Preferences"

    def is_in_quiet_hours(self):
        """Check if current time is in user's quiet hours"""
        if not self.quiet_hours_start or not self.quiet_hours_end:
            return False

        now = timezone.now().time()

        # Handle quiet hours that span midnight
        if self.quiet_hours_start > self.quiet_hours_end:
            return now >= self.quiet_hours_start or now <= self.quiet_hours_end
        else:
            return self.quiet_hours_start <= now <= self.quiet_hours_end

    def should_receive_notification(self, notification_type):
        """Check if user should receive a specific type of notification"""
        if not self.enable_live_notifications:
            return False

        if self.is_in_quiet_hours():
            return False

        # Check type-specific preferences
        type_mapping = {
            'odds_change': self.notify_odds_changes,
            'new_market': self.notify_new_markets,
            'market_suspended': self.notify_market_suspensions,
            'market_reopened': self.notify_market_suspensions,
            'goal_scored': self.notify_match_events,
            'red_card': self.notify_match_events,
            'penalty_awarded': self.notify_match_events,
            'match_event': self.notify_match_events,
            'bet_won': self.notify_bet_results,
            'bet_lost': self.notify_bet_results,
            'cashout_available': self.notify_cashout_opportunities,
            'cashout_unavailable': self.notify_cashout_opportunities,
            'live_stream_available': self.notify_live_streams,
        }

        return type_mapping.get(notification_type, True)


class NotificationDeliveryLog(models.Model):
    """
    Log of notification delivery attempts
    """
    DELIVERY_METHOD_CHOICES = [
        ('push', 'Push Notification'),
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('websocket', 'WebSocket'),
        ('in_app', 'In-App Notification'),
    ]

    DELIVERY_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('bounced', 'Bounced'),
        ('clicked', 'Clicked'),
    ]

    notification = models.ForeignKey(LiveBetNotification, on_delete=models.CASCADE, related_name='delivery_logs')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notification_delivery_logs')

    # Delivery details
    delivery_method = models.CharField(max_length=20, choices=DELIVERY_METHOD_CHOICES)
    delivery_status = models.CharField(max_length=20, choices=DELIVERY_STATUS_CHOICES, default='pending')

    # Delivery metadata
    recipient_address = models.CharField(max_length=255, blank=True)  # email, phone, device_token
    provider = models.CharField(max_length=100, blank=True)  # FCM, APNS, SendGrid, Twilio, etc.
    provider_message_id = models.CharField(max_length=255, blank=True)

    # Response data
    response_data = models.JSONField(default=dict, blank=True)
    error_message = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    clicked_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'betting_notification_delivery_log'
        ordering = ['-created_at']
        verbose_name = 'Notification Delivery Log'
        verbose_name_plural = 'Notification Delivery Logs'
        indexes = [
            models.Index(fields=['user', 'delivery_status']),
            models.Index(fields=['notification', 'delivery_method']),
            models.Index(fields=['delivery_status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.delivery_method} - {self.delivery_status}"

    def mark_as_sent(self, provider_message_id=''):
        """Mark delivery as sent"""
        self.delivery_status = 'sent'
        self.sent_at = timezone.now()
        if provider_message_id:
            self.provider_message_id = provider_message_id
        self.save()

    def mark_as_delivered(self):
        """Mark delivery as delivered"""
        self.delivery_status = 'delivered'
        self.delivered_at = timezone.now()
        self.save()

    def mark_as_failed(self, error_message=''):
        """Mark delivery as failed"""
        self.delivery_status = 'failed'
        if error_message:
            self.error_message = error_message
        self.save()

    def mark_as_clicked(self):
        """Mark notification as clicked"""
        self.delivery_status = 'clicked'
        self.clicked_at = timezone.now()
        self.save()


class LiveBetNotificationTemplate(models.Model):
    """
    Templates for live bet notifications
    """
    TEMPLATE_TYPE_CHOICES = [
        ('odds_change', 'Odds Change'),
        ('new_market', 'New Market'),
        ('market_suspended', 'Market Suspended'),
        ('goal_scored', 'Goal Scored'),
        ('bet_result', 'Bet Result'),
        ('cashout', 'Cash Out'),
        ('general', 'General'),
    ]

    name = models.CharField(max_length=100, unique=True)
    template_type = models.CharField(max_length=30, choices=TEMPLATE_TYPE_CHOICES)

    # Template content
    title_template = models.CharField(max_length=200)
    message_template = models.TextField()

    # Template variables (JSON list of available variables)
    available_variables = models.JSONField(default=list, blank=True)

    # Settings
    is_active = models.BooleanField(default=True)
    priority = models.CharField(max_length=10, choices=LiveBetNotification.PRIORITY_CHOICES, default='medium')

    # Delivery settings
    default_push = models.BooleanField(default=True)
    default_email = models.BooleanField(default=False)
    default_sms = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_live_bet_notification_template'
        ordering = ['name']
        verbose_name = 'Live Bet Notification Template'
        verbose_name_plural = 'Live Bet Notification Templates'

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def render_title(self, context):
        """Render title with context variables"""
        title = self.title_template
        for key, value in context.items():
            title = title.replace(f"{{{key}}}", str(value))
        return title

    def render_message(self, context):
        """Render message with context variables"""
        message = self.message_template
        for key, value in context.items():
            message = message.replace(f"{{{key}}}", str(value))
        return message


class VirtualSport(models.Model):
    """
    Virtual sports available for betting
    """
    SPORT_TYPE_CHOICES = [
        ('football', 'Virtual Football'),
        ('horse_racing', 'Virtual Horse Racing'),
        ('greyhound_racing', 'Virtual Greyhound Racing'),
        ('basketball', 'Virtual Basketball'),
        ('tennis', 'Virtual Tennis'),
        ('motor_racing', 'Virtual Motor Racing'),
        ('cycling', 'Virtual Cycling'),
    ]

    name = models.CharField(max_length=100)
    sport_type = models.CharField(max_length=20, choices=SPORT_TYPE_CHOICES, unique=True)
    description = models.TextField(blank=True)

    # Visual settings
    logo = models.ImageField(upload_to='virtual_sports/logos/', null=True, blank=True)
    background_image = models.ImageField(upload_to='virtual_sports/backgrounds/', null=True, blank=True)

    # Game settings
    match_duration_minutes = models.PositiveIntegerField(default=90)  # Duration of each virtual match
    matches_per_day = models.PositiveIntegerField(default=48)  # How many matches per day
    interval_minutes = models.PositiveIntegerField(default=30)  # Interval between matches

    # Betting settings
    is_active = models.BooleanField(default=True)
    min_bet_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    max_bet_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('10000.00'))

    # Algorithm settings
    randomness_factor = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.15'))  # 0-1 scale
    home_advantage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.05'))  # 0-1 scale

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_virtual_sport'
        ordering = ['name']
        verbose_name = 'Virtual Sport'
        verbose_name_plural = 'Virtual Sports'

    def __str__(self):
        return self.name

    def generate_next_match_time(self):
        """Generate the next match start time"""
        from datetime import datetime, timedelta

        now = timezone.now()
        # Round to next interval
        minutes_to_add = self.interval_minutes - (now.minute % self.interval_minutes)
        next_match_time = now.replace(second=0, microsecond=0) + timedelta(minutes=minutes_to_add)

        return next_match_time


class VirtualTeam(models.Model):
    """
    Virtual teams for virtual sports
    """
    virtual_sport = models.ForeignKey(VirtualSport, on_delete=models.CASCADE, related_name='teams')
    name = models.CharField(max_length=100)
    short_name = models.CharField(max_length=10)

    # Team attributes (affect match outcomes)
    attack_rating = models.PositiveIntegerField(default=75)  # 1-100
    defense_rating = models.PositiveIntegerField(default=75)  # 1-100
    midfield_rating = models.PositiveIntegerField(default=75)  # 1-100
    overall_rating = models.PositiveIntegerField(default=75)  # 1-100

    # Visual elements
    logo = models.ImageField(upload_to='virtual_teams/logos/', null=True, blank=True)
    primary_color = models.CharField(max_length=7, default='#000000')  # Hex color
    secondary_color = models.CharField(max_length=7, default='#FFFFFF')  # Hex color

    # Statistics
    matches_played = models.PositiveIntegerField(default=0)
    wins = models.PositiveIntegerField(default=0)
    draws = models.PositiveIntegerField(default=0)
    losses = models.PositiveIntegerField(default=0)
    goals_for = models.PositiveIntegerField(default=0)
    goals_against = models.PositiveIntegerField(default=0)

    # Status
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_virtual_team'
        ordering = ['name']
        verbose_name = 'Virtual Team'
        verbose_name_plural = 'Virtual Teams'
        unique_together = ['virtual_sport', 'name']

    def __str__(self):
        return f"{self.name} ({self.virtual_sport.name})"

    @property
    def win_percentage(self):
        """Calculate win percentage"""
        if self.matches_played == 0:
            return 0
        return round((self.wins / self.matches_played) * 100, 1)

    @property
    def goal_difference(self):
        """Calculate goal difference"""
        return self.goals_for - self.goals_against

    def update_stats(self, goals_for, goals_against, result):
        """Update team statistics after a match"""
        self.matches_played += 1
        self.goals_for += goals_for
        self.goals_against += goals_against

        if result == 'win':
            self.wins += 1
        elif result == 'draw':
            self.draws += 1
        elif result == 'loss':
            self.losses += 1

        self.save()


class VirtualMatch(models.Model):
    """
    Virtual matches/events
    """
    MATCH_STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('live', 'Live'),
        ('finished', 'Finished'),
        ('cancelled', 'Cancelled'),
    ]

    virtual_sport = models.ForeignKey(VirtualSport, on_delete=models.CASCADE, related_name='matches')
    home_team = models.ForeignKey(VirtualTeam, on_delete=models.CASCADE, related_name='home_matches')
    away_team = models.ForeignKey(VirtualTeam, on_delete=models.CASCADE, related_name='away_matches')

    # Match details
    match_id = models.CharField(max_length=20, unique=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=MATCH_STATUS_CHOICES, default='scheduled')

    # Results
    home_score = models.PositiveIntegerField(null=True, blank=True)
    away_score = models.PositiveIntegerField(null=True, blank=True)

    # Match events (JSON field for storing events like goals, cards, etc.)
    events = models.JSONField(default=list, blank=True)

    # Algorithm data
    algorithm_seed = models.CharField(max_length=50, blank=True)  # For reproducible results
    home_win_probability = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    draw_probability = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    away_win_probability = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    # Betting
    betting_enabled = models.BooleanField(default=True)
    total_bets = models.PositiveIntegerField(default=0)
    total_stake = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_virtual_match'
        ordering = ['-start_time']
        verbose_name = 'Virtual Match'
        verbose_name_plural = 'Virtual Matches'
        indexes = [
            models.Index(fields=['virtual_sport', 'status']),
            models.Index(fields=['start_time', 'status']),
        ]

    def __str__(self):
        return f"{self.home_team.name} vs {self.away_team.name} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"

    def save(self, *args, **kwargs):
        if not self.match_id:
            # Generate unique match ID
            import uuid
            self.match_id = f"VM{str(uuid.uuid4())[:8].upper()}"

        super().save(*args, **kwargs)

    def calculate_probabilities(self):
        """Calculate match outcome probabilities based on team ratings"""
        import random

        # Base probabilities from team ratings
        home_strength = (
            self.home_team.overall_rating +
            (self.home_team.attack_rating + self.home_team.defense_rating) / 2
        ) / 2

        away_strength = (
            self.away_team.overall_rating +
            (self.away_team.attack_rating + self.away_team.defense_rating) / 2
        ) / 2

        # Apply home advantage
        home_strength += float(self.virtual_sport.home_advantage) * 10

        # Calculate strength difference
        strength_diff = home_strength - away_strength

        # Convert to probabilities (simplified model)
        if strength_diff > 0:
            home_prob = 0.45 + (strength_diff / 200)
            away_prob = 0.35 - (strength_diff / 300)
        else:
            home_prob = 0.45 + (strength_diff / 300)
            away_prob = 0.35 - (strength_diff / 200)

        draw_prob = 1.0 - home_prob - away_prob

        # Ensure probabilities are within valid range
        home_prob = max(0.1, min(0.8, home_prob))
        away_prob = max(0.1, min(0.8, away_prob))
        draw_prob = max(0.1, min(0.8, draw_prob))

        # Normalize to sum to 1
        total = home_prob + draw_prob + away_prob
        home_prob /= total
        draw_prob /= total
        away_prob /= total

        # Apply randomness factor
        randomness = float(self.virtual_sport.randomness_factor)
        if randomness > 0:
            random_factor = random.uniform(-randomness, randomness)
            home_prob += random_factor
            away_prob -= random_factor / 2
            draw_prob -= random_factor / 2

            # Ensure probabilities remain valid
            home_prob = max(0.05, min(0.9, home_prob))
            away_prob = max(0.05, min(0.9, away_prob))
            draw_prob = max(0.05, min(0.9, draw_prob))

            # Normalize again
            total = home_prob + draw_prob + away_prob
            home_prob /= total
            draw_prob /= total
            away_prob /= total

        self.home_win_probability = Decimal(str(round(home_prob, 4)))
        self.draw_probability = Decimal(str(round(draw_prob, 4)))
        self.away_win_probability = Decimal(str(round(away_prob, 4)))

        self.save()

    def simulate_match(self):
        """Simulate the match and generate result"""
        import random
        import uuid

        if self.status != 'scheduled':
            return False

        # Set algorithm seed for reproducibility
        if not self.algorithm_seed:
            self.algorithm_seed = str(uuid.uuid4())
            self.save()

        # Use seed for consistent results
        random.seed(self.algorithm_seed)

        # Calculate probabilities if not set
        if not self.home_win_probability:
            self.calculate_probabilities()

        # Determine match outcome
        outcome_rand = random.random()

        if outcome_rand < float(self.home_win_probability):
            # Home win
            self.home_score = random.randint(1, 4)
            self.away_score = random.randint(0, self.home_score - 1)
            result = 'home_win'
        elif outcome_rand < float(self.home_win_probability) + float(self.draw_probability):
            # Draw
            score = random.randint(0, 3)
            self.home_score = score
            self.away_score = score
            result = 'draw'
        else:
            # Away win
            self.away_score = random.randint(1, 4)
            self.home_score = random.randint(0, self.away_score - 1)
            result = 'away_win'

        # Generate match events
        self.generate_match_events()

        # Update match status
        self.status = 'finished'
        self.end_time = timezone.now()
        self.save()

        # Update team statistics
        if result == 'home_win':
            self.home_team.update_stats(self.home_score, self.away_score, 'win')
            self.away_team.update_stats(self.away_score, self.home_score, 'loss')
        elif result == 'away_win':
            self.home_team.update_stats(self.home_score, self.away_score, 'loss')
            self.away_team.update_stats(self.away_score, self.home_score, 'win')
        else:  # draw
            self.home_team.update_stats(self.home_score, self.away_score, 'draw')
            self.away_team.update_stats(self.away_score, self.home_score, 'draw')

        return True

    def generate_match_events(self):
        """Generate realistic match events"""
        import random

        events = []
        total_goals = self.home_score + self.away_score

        # Generate goal events
        for i in range(total_goals):
            minute = random.randint(1, 90)

            # Determine which team scored
            if i < self.home_score:
                team = 'home'
                team_name = self.home_team.name
            else:
                team = 'away'
                team_name = self.away_team.name

            events.append({
                'type': 'goal',
                'minute': minute,
                'team': team,
                'team_name': team_name,
                'description': f"Goal scored by {team_name}"
            })

        # Generate other events (cards, substitutions, etc.)
        num_cards = random.randint(0, 4)
        for i in range(num_cards):
            minute = random.randint(10, 90)
            team = random.choice(['home', 'away'])
            team_name = self.home_team.name if team == 'home' else self.away_team.name
            card_type = random.choice(['yellow', 'yellow', 'yellow', 'red'])  # More yellows than reds

            events.append({
                'type': 'card',
                'minute': minute,
                'team': team,
                'team_name': team_name,
                'card_type': card_type,
                'description': f"{card_type.title()} card for {team_name}"
            })

        # Sort events by minute
        events.sort(key=lambda x: x['minute'])

        self.events = events

    @property
    def is_finished(self):
        """Check if match is finished"""
        return self.status == 'finished'

    @property
    def winner(self):
        """Get match winner"""
        if not self.is_finished:
            return None

        if self.home_score > self.away_score:
            return 'home'
        elif self.away_score > self.home_score:
            return 'away'
        else:
            return 'draw'


class VirtualBetType(models.Model):
    """
    Bet types available for virtual sports
    """
    virtual_sport = models.ForeignKey(VirtualSport, on_delete=models.CASCADE, related_name='bet_types')
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100)
    description = models.TextField(blank=True)

    # Betting rules
    is_active = models.BooleanField(default=True)
    min_odds = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.01'))
    max_odds = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1000.00'))

    # Display settings
    display_order = models.PositiveIntegerField(default=0)

    class Meta:
        db_table = 'betting_virtual_bet_type'
        ordering = ['display_order', 'name']
        verbose_name = 'Virtual Bet Type'
        verbose_name_plural = 'Virtual Bet Types'
        unique_together = ['virtual_sport', 'slug']

    def __str__(self):
        return f"{self.name} ({self.virtual_sport.name})"


class VirtualMarket(models.Model):
    """
    Betting markets for virtual matches
    """
    MARKET_STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('settled', 'Settled'),
        ('cancelled', 'Cancelled'),
    ]

    virtual_match = models.ForeignKey(VirtualMatch, on_delete=models.CASCADE, related_name='markets')
    bet_type = models.ForeignKey(VirtualBetType, on_delete=models.CASCADE, related_name='markets')

    # Market details
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=MARKET_STATUS_CHOICES, default='active')

    # Market parameters (for handicap, totals, etc.)
    parameters = models.JSONField(default=dict, blank=True)

    # Settlement
    winning_selection = models.CharField(max_length=100, blank=True)
    settled_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_virtual_market'
        ordering = ['-created_at']
        verbose_name = 'Virtual Market'
        verbose_name_plural = 'Virtual Markets'

    def __str__(self):
        return f"{self.name} - {self.virtual_match}"

    def settle_market(self):
        """Settle the market based on match result"""
        if self.status != 'active' or not self.virtual_match.is_finished:
            return False

        # Determine winning selection based on bet type
        match = self.virtual_match

        if self.bet_type.slug == 'match-winner':
            if match.winner == 'home':
                self.winning_selection = 'home'
            elif match.winner == 'away':
                self.winning_selection = 'away'
            else:
                self.winning_selection = 'draw'

        elif self.bet_type.slug == 'total-goals':
            total_goals = match.home_score + match.away_score
            threshold = self.parameters.get('threshold', 2.5)

            if total_goals > threshold:
                self.winning_selection = 'over'
            else:
                self.winning_selection = 'under'

        elif self.bet_type.slug == 'both-teams-score':
            if match.home_score > 0 and match.away_score > 0:
                self.winning_selection = 'yes'
            else:
                self.winning_selection = 'no'

        # Update status
        self.status = 'settled'
        self.settled_at = timezone.now()
        self.save()

        # Settle all selections in this market
        for selection in self.selections.all():
            selection.settle_selection()

        return True


class VirtualSelection(models.Model):
    """
    Betting selections for virtual markets
    """
    SELECTION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
    ]

    market = models.ForeignKey(VirtualMarket, on_delete=models.CASCADE, related_name='selections')

    # Selection details
    name = models.CharField(max_length=200)
    selection_key = models.CharField(max_length=100)  # home, away, draw, over, under, etc.

    # Odds
    decimal_odds = models.DecimalField(max_digits=10, decimal_places=2)
    fractional_odds = models.CharField(max_length=20, blank=True)

    # Status
    status = models.CharField(max_length=20, choices=SELECTION_STATUS_CHOICES, default='active')

    # Betting statistics
    total_bets = models.PositiveIntegerField(default=0)
    total_stake = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_virtual_selection'
        ordering = ['decimal_odds']
        verbose_name = 'Virtual Selection'
        verbose_name_plural = 'Virtual Selections'

    def __str__(self):
        return f"{self.name} @ {self.decimal_odds}"

    def save(self, *args, **kwargs):
        # Auto-generate fractional odds
        if not self.fractional_odds:
            self.fractional_odds = self.decimal_to_fractional(self.decimal_odds)

        super().save(*args, **kwargs)

    def decimal_to_fractional(self, decimal_odds):
        """Convert decimal odds to fractional odds"""
        decimal_odds = float(decimal_odds)

        if decimal_odds < 1:
            return "0/1"

        # Convert to fraction
        numerator = decimal_odds - 1
        denominator = 1

        # Simplify fraction
        from fractions import Fraction
        frac = Fraction(numerator).limit_denominator(100)

        return f"{frac.numerator}/{frac.denominator}"

    def settle_selection(self):
        """Settle the selection based on market result"""
        if self.market.status != 'settled':
            return False

        if self.selection_key == self.market.winning_selection:
            self.status = 'won'
        else:
            self.status = 'lost'

        self.save()
        return True


class VirtualBet(models.Model):
    """
    Bets placed on virtual sports
    """
    BET_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('active', 'Active'),
        ('won', 'Won'),
        ('lost', 'Lost'),
        ('void', 'Void'),
        ('cancelled', 'Cancelled'),
    ]

    BET_TYPE_CHOICES = [
        ('single', 'Single'),
        ('multiple', 'Multiple'),
        ('system', 'System'),
    ]

    # User and identification
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='virtual_bets')
    bet_id = models.CharField(max_length=20, unique=True)

    # Bet details
    bet_type = models.CharField(max_length=20, choices=BET_TYPE_CHOICES, default='single')
    stake = models.DecimalField(max_digits=10, decimal_places=2)
    total_odds = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    potential_winnings = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))

    # Status
    status = models.CharField(max_length=20, choices=BET_STATUS_CHOICES, default='pending')

    # Settlement
    actual_winnings = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    settled_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_virtual_bet'
        ordering = ['-created_at']
        verbose_name = 'Virtual Bet'
        verbose_name_plural = 'Virtual Bets'
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"Virtual Bet {self.bet_id} - {self.user.email}"

    def save(self, *args, **kwargs):
        if not self.bet_id:
            # Generate unique bet ID
            import uuid
            self.bet_id = f"VB{str(uuid.uuid4())[:8].upper()}"

        # Calculate potential winnings
        self.potential_winnings = self.stake * self.total_odds

        super().save(*args, **kwargs)

    def settle_bet(self):
        """Settle the bet based on selection results"""
        if self.status not in ['pending', 'active']:
            return False

        # Check if all selections are settled
        selections = self.selections.all()
        if not all(sel.selection.status in ['won', 'lost', 'void'] for sel in selections):
            return False

        # Determine bet result
        if self.bet_type == 'single':
            # Single bet - one selection
            selection = selections.first()
            if selection.selection.status == 'won':
                self.status = 'won'
                self.actual_winnings = self.potential_winnings
            elif selection.selection.status == 'void':
                self.status = 'void'
                self.actual_winnings = self.stake  # Return stake
            else:
                self.status = 'lost'
                self.actual_winnings = Decimal('0.00')

        else:
            # Multiple bet - all selections must win
            won_selections = [sel for sel in selections if sel.selection.status == 'won']
            void_selections = [sel for sel in selections if sel.selection.status == 'void']

            if len(won_selections) == len(selections):
                self.status = 'won'
                self.actual_winnings = self.potential_winnings
            elif len(won_selections) + len(void_selections) == len(selections):
                # Some void selections - recalculate odds
                valid_odds = Decimal('1.00')
                for sel in won_selections:
                    valid_odds *= sel.selection.decimal_odds

                self.actual_winnings = self.stake * valid_odds
                self.status = 'won' if len(won_selections) > 0 else 'void'
            else:
                self.status = 'lost'
                self.actual_winnings = Decimal('0.00')

        self.settled_at = timezone.now()
        self.save()

        return True


class VirtualBetSelection(models.Model):
    """
    Selections within a virtual bet
    """
    bet = models.ForeignKey(VirtualBet, on_delete=models.CASCADE, related_name='selections')
    selection = models.ForeignKey(VirtualSelection, on_delete=models.CASCADE, related_name='bet_selections')

    # Odds at time of bet placement
    odds_at_placement = models.DecimalField(max_digits=10, decimal_places=2)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'betting_virtual_bet_selection'
        verbose_name = 'Virtual Bet Selection'
        verbose_name_plural = 'Virtual Bet Selections'
        unique_together = ['bet', 'selection']

    def __str__(self):
        return f"{self.bet.bet_id} - {self.selection.name}"


class VirtualSportsManager(models.Model):
    """
    Manager for virtual sports system configuration and automation
    """
    GENERATION_STATUS_CHOICES = [
        ('idle', 'Idle'),
        ('generating', 'Generating'),
        ('error', 'Error'),
    ]

    virtual_sport = models.OneToOneField(VirtualSport, on_delete=models.CASCADE, related_name='manager')

    # Auto-generation settings
    auto_generate_matches = models.BooleanField(default=True)
    auto_generate_odds = models.BooleanField(default=True)
    auto_settle_matches = models.BooleanField(default=True)

    # Generation parameters
    matches_ahead_hours = models.PositiveIntegerField(default=24)  # Generate matches X hours ahead
    odds_margin = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.05'))  # House edge

    # Status tracking
    generation_status = models.CharField(max_length=20, choices=GENERATION_STATUS_CHOICES, default='idle')
    last_generation_time = models.DateTimeField(null=True, blank=True)
    last_settlement_time = models.DateTimeField(null=True, blank=True)
    last_error_message = models.TextField(blank=True)

    # Statistics
    total_matches_generated = models.PositiveIntegerField(default=0)
    total_matches_settled = models.PositiveIntegerField(default=0)
    total_bets_processed = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'betting_virtual_sports_manager'
        verbose_name = 'Virtual Sports Manager'
        verbose_name_plural = 'Virtual Sports Managers'

    def __str__(self):
        return f"Manager for {self.virtual_sport.name}"

    def generate_upcoming_matches(self):
        """Generate matches for the next period"""
        try:
            self.generation_status = 'generating'
            self.save()

            from datetime import timedelta

            now = timezone.now()
            end_time = now + timedelta(hours=self.matches_ahead_hours)

            # Get existing matches in this period
            existing_matches = VirtualMatch.objects.filter(
                virtual_sport=self.virtual_sport,
                start_time__gte=now,
                start_time__lte=end_time
            ).count()

            # Calculate how many matches we need
            total_needed = int(self.matches_ahead_hours * (self.virtual_sport.matches_per_day / 24))
            matches_to_generate = max(0, total_needed - existing_matches)

            # Generate matches
            teams = list(self.virtual_sport.teams.filter(is_active=True))
            if len(teams) < 2:
                raise ValueError("Need at least 2 active teams to generate matches")

            import random
            generated_count = 0

            current_time = self.virtual_sport.generate_next_match_time()

            for i in range(matches_to_generate):
                # Select random teams
                home_team = random.choice(teams)
                away_team = random.choice([t for t in teams if t != home_team])

                # Create match
                match = VirtualMatch.objects.create(
                    virtual_sport=self.virtual_sport,
                    home_team=home_team,
                    away_team=away_team,
                    start_time=current_time
                )

                # Calculate probabilities
                match.calculate_probabilities()

                # Generate markets and odds if enabled
                if self.auto_generate_odds:
                    self.generate_match_markets(match)

                generated_count += 1
                current_time += timedelta(minutes=self.virtual_sport.interval_minutes)

            # Update statistics
            self.total_matches_generated += generated_count
            self.last_generation_time = timezone.now()
            self.generation_status = 'idle'
            self.last_error_message = ''
            self.save()

            return generated_count

        except Exception as e:
            self.generation_status = 'error'
            self.last_error_message = str(e)
            self.save()
            raise e

    def generate_match_markets(self, match):
        """Generate betting markets for a match"""
        bet_types = self.virtual_sport.bet_types.filter(is_active=True)

        for bet_type in bet_types:
            # Create market
            market = VirtualMarket.objects.create(
                virtual_match=match,
                bet_type=bet_type,
                name=f"{bet_type.name}",
                status='active'
            )

            # Generate selections based on bet type
            if bet_type.slug == 'match-winner':
                self.create_match_winner_selections(market, match)
            elif bet_type.slug == 'total-goals':
                self.create_total_goals_selections(market, match)
            elif bet_type.slug == 'both-teams-score':
                self.create_both_teams_score_selections(market, match)

    def create_match_winner_selections(self, market, match):
        """Create match winner selections"""
        # Calculate base odds from probabilities
        home_odds = 1 / float(match.home_win_probability) if match.home_win_probability > 0 else 10.0
        draw_odds = 1 / float(match.draw_probability) if match.draw_probability > 0 else 10.0
        away_odds = 1 / float(match.away_win_probability) if match.away_win_probability > 0 else 10.0

        # Apply margin
        margin = float(self.odds_margin)
        home_odds *= (1 + margin)
        draw_odds *= (1 + margin)
        away_odds *= (1 + margin)

        # Create selections
        VirtualSelection.objects.create(
            market=market,
            name=match.home_team.name,
            selection_key='home',
            decimal_odds=Decimal(str(round(home_odds, 2)))
        )

        VirtualSelection.objects.create(
            market=market,
            name='Draw',
            selection_key='draw',
            decimal_odds=Decimal(str(round(draw_odds, 2)))
        )

        VirtualSelection.objects.create(
            market=market,
            name=match.away_team.name,
            selection_key='away',
            decimal_odds=Decimal(str(round(away_odds, 2)))
        )

    def create_total_goals_selections(self, market, match):
        """Create total goals selections"""
        # Set threshold (usually 2.5)
        threshold = 2.5
        market.parameters = {'threshold': threshold}
        market.save()

        # Estimate probabilities based on team ratings
        avg_goals = (match.home_team.overall_rating + match.away_team.overall_rating) / 50

        # Simple probability model
        over_prob = min(0.8, max(0.2, avg_goals / 4))
        under_prob = 1 - over_prob

        # Calculate odds
        over_odds = (1 / over_prob) * (1 + float(self.odds_margin))
        under_odds = (1 / under_prob) * (1 + float(self.odds_margin))

        VirtualSelection.objects.create(
            market=market,
            name=f'Over {threshold}',
            selection_key='over',
            decimal_odds=Decimal(str(round(over_odds, 2)))
        )

        VirtualSelection.objects.create(
            market=market,
            name=f'Under {threshold}',
            selection_key='under',
            decimal_odds=Decimal(str(round(under_odds, 2)))
        )

    def create_both_teams_score_selections(self, market, match):
        """Create both teams score selections"""
        # Estimate probability based on team attack ratings
        home_attack = match.home_team.attack_rating / 100
        away_attack = match.away_team.attack_rating / 100

        # Simple model for both teams scoring
        btts_prob = min(0.8, max(0.2, home_attack * away_attack * 0.8))
        no_btts_prob = 1 - btts_prob

        # Calculate odds
        yes_odds = (1 / btts_prob) * (1 + float(self.odds_margin))
        no_odds = (1 / no_btts_prob) * (1 + float(self.odds_margin))

        VirtualSelection.objects.create(
            market=market,
            name='Yes',
            selection_key='yes',
            decimal_odds=Decimal(str(round(yes_odds, 2)))
        )

        VirtualSelection.objects.create(
            market=market,
            name='No',
            selection_key='no',
            decimal_odds=Decimal(str(round(no_odds, 2)))
        )

    def settle_finished_matches(self):
        """Settle all finished matches that haven't been settled"""
        try:
            finished_matches = VirtualMatch.objects.filter(
                virtual_sport=self.virtual_sport,
                status='finished'
            ).exclude(
                markets__status='settled'
            ).distinct()

            settled_count = 0

            for match in finished_matches:
                # Settle all markets for this match
                for market in match.markets.filter(status='active'):
                    if market.settle_market():
                        settled_count += 1

                # Settle all bets for this match
                for market in match.markets.all():
                    for selection in market.selections.all():
                        for bet_selection in selection.bet_selections.all():
                            bet_selection.bet.settle_bet()

            # Update statistics
            self.total_matches_settled += len(finished_matches)
            self.last_settlement_time = timezone.now()
            self.save()

            return settled_count

        except Exception as e:
            self.last_error_message = str(e)
            self.save()
            raise e
