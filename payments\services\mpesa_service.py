"""
M-Pesa Daraja API Integration Service
Handles all M-Pesa payment operations including STK Push, callbacks, and transaction verification
"""

import requests
import base64
import json
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


class MPesaService:
    """M-Pesa Daraja API service for handling payments"""
    
    def __init__(self):
        self.consumer_key = getattr(settings, 'MPESA_CONSUMER_KEY', '')
        self.consumer_secret = getattr(settings, 'MPESA_CONSUMER_SECRET', '')
        self.business_shortcode = getattr(settings, 'MPESA_SHORTCODE', '')
        self.passkey = getattr(settings, 'MPESA_PASSKEY', '')
        self.callback_url = getattr(settings, 'MPESA_CALLBACK_URL', '')
        self.environment = getattr(settings, 'MPESA_ENVIRONMENT', 'sandbox')
        
        # Set API URLs based on environment
        if self.environment == 'production':
            self.base_url = 'https://api.safaricom.co.ke'
        else:
            self.base_url = 'https://sandbox.safaricom.co.ke'
    
    def get_access_token(self):
        """Get M-Pesa access token with caching"""
        cache_key = 'mpesa_access_token'
        token = cache.get(cache_key)
        
        if token:
            return token
        
        try:
            # Create credentials
            credentials = base64.b64encode(
                f"{self.consumer_key}:{self.consumer_secret}".encode()
            ).decode('utf-8')
            
            headers = {
                'Authorization': f'Basic {credentials}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.base_url}/oauth/v1/generate?grant_type=client_credentials",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get('access_token')
                expires_in = int(token_data.get('expires_in', 3600))
                
                # Cache token for slightly less than expiry time
                cache.set(cache_key, access_token, expires_in - 60)
                return access_token
            else:
                logger.error(f"Failed to get M-Pesa access token: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting M-Pesa access token: {str(e)}")
            return None
    
    def generate_password(self):
        """Generate M-Pesa password for STK Push"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        password_string = f"{self.business_shortcode}{self.passkey}{timestamp}"
        password = base64.b64encode(password_string.encode()).decode('utf-8')
        return password, timestamp
    
    def format_phone_number(self, phone_number):
        """Format phone number to M-Pesa format (254XXXXXXXXX)"""
        # Remove any spaces, dashes, or plus signs
        phone = phone_number.replace(' ', '').replace('-', '').replace('+', '')
        
        # Handle different formats
        if phone.startswith('0'):
            phone = '254' + phone[1:]
        elif phone.startswith('7') or phone.startswith('1'):
            phone = '254' + phone
        elif not phone.startswith('254'):
            phone = '254' + phone
        
        return phone
    
    def initiate_stk_push(self, phone_number, amount, account_reference, transaction_desc):
        """Initiate M-Pesa STK Push payment"""
        access_token = self.get_access_token()
        if not access_token:
            return {'success': False, 'message': 'Failed to get access token'}
        
        try:
            password, timestamp = self.generate_password()
            formatted_phone = self.format_phone_number(phone_number)
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'BusinessShortCode': self.business_shortcode,
                'Password': password,
                'Timestamp': timestamp,
                'TransactionType': 'CustomerPayBillOnline',
                'Amount': int(float(amount)),
                'PartyA': formatted_phone,
                'PartyB': self.business_shortcode,
                'PhoneNumber': formatted_phone,
                'CallBackURL': self.callback_url,
                'AccountReference': account_reference,
                'TransactionDesc': transaction_desc
            }
            
            response = requests.post(
                f"{self.base_url}/mpesa/stkpush/v1/processrequest",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('ResponseCode') == '0':
                    return {
                        'success': True,
                        'checkout_request_id': response_data.get('CheckoutRequestID'),
                        'merchant_request_id': response_data.get('MerchantRequestID'),
                        'customer_message': response_data.get('CustomerMessage'),
                        'response_code': response_data.get('ResponseCode'),
                        'response_description': response_data.get('ResponseDescription')
                    }
                else:
                    return {
                        'success': False,
                        'message': response_data.get('ResponseDescription', 'STK Push failed'),
                        'error_code': response_data.get('ResponseCode')
                    }
            else:
                logger.error(f"STK Push request failed: {response.text}")
                return {'success': False, 'message': 'Payment request failed'}
                
        except Exception as e:
            logger.error(f"Error initiating STK Push: {str(e)}")
            return {'success': False, 'message': 'Payment service error'}
    
    def query_stk_status(self, checkout_request_id):
        """Query STK Push transaction status"""
        access_token = self.get_access_token()
        if not access_token:
            return {'success': False, 'message': 'Failed to get access token'}
        
        try:
            password, timestamp = self.generate_password()
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'BusinessShortCode': self.business_shortcode,
                'Password': password,
                'Timestamp': timestamp,
                'CheckoutRequestID': checkout_request_id
            }
            
            response = requests.post(
                f"{self.base_url}/mpesa/stkpushquery/v1/query",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return {'success': True, 'data': response.json()}
            else:
                logger.error(f"STK status query failed: {response.text}")
                return {'success': False, 'message': 'Status query failed'}
                
        except Exception as e:
            logger.error(f"Error querying STK status: {str(e)}")
            return {'success': False, 'message': 'Status query error'}
    
    def process_callback(self, callback_data):
        """Process M-Pesa callback data"""
        try:
            stk_callback = callback_data.get('Body', {}).get('stkCallback', {})
            
            result_code = stk_callback.get('ResultCode')
            result_desc = stk_callback.get('ResultDesc')
            checkout_request_id = stk_callback.get('CheckoutRequestID')
            merchant_request_id = stk_callback.get('MerchantRequestID')
            
            callback_metadata = stk_callback.get('CallbackMetadata', {})
            items = callback_metadata.get('Item', [])
            
            # Extract transaction details from callback metadata
            transaction_data = {}
            for item in items:
                name = item.get('Name')
                value = item.get('Value')
                if name == 'Amount':
                    transaction_data['amount'] = value
                elif name == 'MpesaReceiptNumber':
                    transaction_data['mpesa_receipt_number'] = value
                elif name == 'TransactionDate':
                    transaction_data['transaction_date'] = value
                elif name == 'PhoneNumber':
                    transaction_data['phone_number'] = value
            
            return {
                'success': True,
                'result_code': result_code,
                'result_desc': result_desc,
                'checkout_request_id': checkout_request_id,
                'merchant_request_id': merchant_request_id,
                'transaction_data': transaction_data
            }
            
        except Exception as e:
            logger.error(f"Error processing M-Pesa callback: {str(e)}")
            return {'success': False, 'message': 'Callback processing error'}
    
    def validate_transaction(self, mpesa_receipt_number):
        """Validate M-Pesa transaction (optional additional verification)"""
        # This would typically involve calling M-Pesa's transaction status API
        # For now, we'll implement basic validation
        if not mpesa_receipt_number or len(mpesa_receipt_number) < 10:
            return False
        return True


# Create a singleton instance
mpesa_service = MPesaService()
