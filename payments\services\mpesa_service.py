"""
M-Pesa Daraja API Integration Service
Handles all M-Pesa payment operations including STK Push, callbacks, and transaction verification
"""

import requests
import base64
import json
import time
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class MPesaService:
    """M-Pesa Daraja API service for handling payments"""
    
    def __init__(self):
        self.consumer_key = getattr(settings, 'MPESA_CONSUMER_KEY', '')
        self.consumer_secret = getattr(settings, 'MPESA_CONSUMER_SECRET', '')
        self.business_shortcode = getattr(settings, 'MPESA_SHORTCODE', '')
        self.passkey = getattr(settings, 'MPESA_PASSKEY', '')
        self.callback_url = getattr(settings, 'MPESA_CALLBACK_URL', '')
        self.environment = getattr(settings, 'MPESA_ENVIRONMENT', 'sandbox')
        
        # Set API URLs based on environment
        if self.environment == 'production':
            self.base_url = 'https://api.safaricom.co.ke'
        else:
            self.base_url = 'https://sandbox.safaricom.co.ke'
    
    def get_access_token(self):
        """Get M-Pesa access token with caching"""
        cache_key = 'mpesa_access_token'
        token = cache.get(cache_key)
        
        if token:
            return token
        
        try:
            # Create credentials
            credentials = base64.b64encode(
                f"{self.consumer_key}:{self.consumer_secret}".encode()
            ).decode('utf-8')
            
            headers = {
                'Authorization': f'Basic {credentials}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.base_url}/oauth/v1/generate?grant_type=client_credentials",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get('access_token')
                expires_in = int(token_data.get('expires_in', 3600))
                
                # Cache token for slightly less than expiry time
                cache.set(cache_key, access_token, expires_in - 60)
                return access_token
            else:
                logger.error(f"Failed to get M-Pesa access token: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting M-Pesa access token: {str(e)}")
            return None
    
    def generate_password(self):
        """Generate M-Pesa password for STK Push"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        password_string = f"{self.business_shortcode}{self.passkey}{timestamp}"
        password = base64.b64encode(password_string.encode()).decode('utf-8')
        return password, timestamp
    
    def format_phone_number(self, phone_number):
        """Format phone number to M-Pesa format (254XXXXXXXXX)"""
        # Remove any spaces, dashes, or plus signs
        phone = phone_number.replace(' ', '').replace('-', '').replace('+', '')
        
        # Handle different formats
        if phone.startswith('0'):
            phone = '254' + phone[1:]
        elif phone.startswith('7') or phone.startswith('1'):
            phone = '254' + phone
        elif not phone.startswith('254'):
            phone = '254' + phone
        
        return phone
    
    def initiate_stk_push(self, phone_number, amount, account_reference, transaction_desc):
        """Initiate M-Pesa STK Push payment"""
        access_token = self.get_access_token()
        if not access_token:
            return {'success': False, 'message': 'Failed to get access token'}
        
        try:
            password, timestamp = self.generate_password()
            formatted_phone = self.format_phone_number(phone_number)
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'BusinessShortCode': self.business_shortcode,
                'Password': password,
                'Timestamp': timestamp,
                'TransactionType': 'CustomerPayBillOnline',
                'Amount': int(float(amount)),
                'PartyA': formatted_phone,
                'PartyB': self.business_shortcode,
                'PhoneNumber': formatted_phone,
                'CallBackURL': self.callback_url,
                'AccountReference': account_reference,
                'TransactionDesc': transaction_desc
            }
            
            response = requests.post(
                f"{self.base_url}/mpesa/stkpush/v1/processrequest",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('ResponseCode') == '0':
                    return {
                        'success': True,
                        'checkout_request_id': response_data.get('CheckoutRequestID'),
                        'merchant_request_id': response_data.get('MerchantRequestID'),
                        'customer_message': response_data.get('CustomerMessage'),
                        'response_code': response_data.get('ResponseCode'),
                        'response_description': response_data.get('ResponseDescription')
                    }
                else:
                    return {
                        'success': False,
                        'message': response_data.get('ResponseDescription', 'STK Push failed'),
                        'error_code': response_data.get('ResponseCode')
                    }
            else:
                logger.error(f"STK Push request failed: {response.text}")
                return {'success': False, 'message': 'Payment request failed'}
                
        except Exception as e:
            logger.error(f"Error initiating STK Push: {str(e)}")
            return {'success': False, 'message': 'Payment service error'}
    
    def query_stk_status(self, checkout_request_id):
        """Query STK Push transaction status"""
        access_token = self.get_access_token()
        if not access_token:
            return {'success': False, 'message': 'Failed to get access token'}
        
        try:
            password, timestamp = self.generate_password()
            
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'BusinessShortCode': self.business_shortcode,
                'Password': password,
                'Timestamp': timestamp,
                'CheckoutRequestID': checkout_request_id
            }
            
            response = requests.post(
                f"{self.base_url}/mpesa/stkpushquery/v1/query",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return {'success': True, 'data': response.json()}
            else:
                logger.error(f"STK status query failed: {response.text}")
                return {'success': False, 'message': 'Status query failed'}
                
        except Exception as e:
            logger.error(f"Error querying STK status: {str(e)}")
            return {'success': False, 'message': 'Status query error'}
    
    def process_callback(self, callback_data):
        """Process M-Pesa callback data"""
        try:
            stk_callback = callback_data.get('Body', {}).get('stkCallback', {})
            
            result_code = stk_callback.get('ResultCode')
            result_desc = stk_callback.get('ResultDesc')
            checkout_request_id = stk_callback.get('CheckoutRequestID')
            merchant_request_id = stk_callback.get('MerchantRequestID')
            
            callback_metadata = stk_callback.get('CallbackMetadata', {})
            items = callback_metadata.get('Item', [])
            
            # Extract transaction details from callback metadata
            transaction_data = {}
            for item in items:
                name = item.get('Name')
                value = item.get('Value')
                if name == 'Amount':
                    transaction_data['amount'] = value
                elif name == 'MpesaReceiptNumber':
                    transaction_data['mpesa_receipt_number'] = value
                elif name == 'TransactionDate':
                    transaction_data['transaction_date'] = value
                elif name == 'PhoneNumber':
                    transaction_data['phone_number'] = value
            
            return {
                'success': True,
                'result_code': result_code,
                'result_desc': result_desc,
                'checkout_request_id': checkout_request_id,
                'merchant_request_id': merchant_request_id,
                'transaction_data': transaction_data
            }
            
        except Exception as e:
            logger.error(f"Error processing M-Pesa callback: {str(e)}")
            return {'success': False, 'message': 'Callback processing error'}
    
    def validate_transaction(self, mpesa_receipt_number):
        """Validate M-Pesa transaction (optional additional verification)"""
        # This would typically involve calling M-Pesa's transaction status API
        # For now, we'll implement basic validation
        if not mpesa_receipt_number or len(mpesa_receipt_number) < 10:
            return False
        return True

    def validate_customer(self, phone_number):
        """Validate M-Pesa customer before transaction"""
        try:
            formatted_phone = self.format_phone_number(phone_number)

            # Basic phone number validation
            if not formatted_phone.startswith('254'):
                return {'success': False, 'valid': False, 'error': 'Invalid phone number format'}

            if len(formatted_phone) != 12:
                return {'success': False, 'valid': False, 'error': 'Phone number must be 12 digits'}

            # Check if it's a valid Kenyan mobile number
            valid_prefixes = ['2547', '2541', '2570', '2571', '2572', '2573', '2574', '2575', '2576', '2577', '2578', '2579']
            if not any(formatted_phone.startswith(prefix) for prefix in valid_prefixes):
                return {'success': False, 'valid': False, 'error': 'Invalid Kenyan mobile number'}

            return {
                'success': True,
                'valid': True,
                'phone_number': formatted_phone
            }

        except Exception as e:
            logger.error(f"Error validating M-Pesa customer: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def initiate_b2c_withdrawal(self, phone_number, amount, reference, description="Withdrawal"):
        """Initiate M-Pesa B2C withdrawal (Business to Customer)"""
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}

            formatted_phone = self.format_phone_number(phone_number)

            url = f"{self.base_url}/mpesa/b2c/v1/paymentrequest"

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            payload = {
                "InitiatorName": settings.MPESA_INITIATOR_NAME,
                "SecurityCredential": self.get_security_credential(),
                "CommandID": "BusinessPayment",
                "Amount": str(amount),
                "PartyA": settings.MPESA_SHORTCODE,
                "PartyB": formatted_phone,
                "Remarks": description,
                "QueueTimeOutURL": f"{settings.SITE_URL}/payments/mpesa/b2c-timeout/",
                "ResultURL": f"{settings.SITE_URL}/payments/mpesa/b2c-result/",
                "Occasion": reference
            }

            response = requests.post(url, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('ResponseCode') == '0':
                    return {
                        'success': True,
                        'conversation_id': result.get('ConversationID'),
                        'originator_conversation_id': result.get('OriginatorConversationID'),
                        'response_description': result.get('ResponseDescription')
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('ResponseDescription', 'B2C withdrawal failed')
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: {response.text}'
                }

        except Exception as e:
            logger.error(f"Error initiating B2C withdrawal: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_security_credential(self):
        """Generate security credential for B2C transactions"""
        # This would typically involve encrypting the initiator password with M-Pesa's public key
        # For sandbox/testing, you can use the provided security credential
        return getattr(settings, 'MPESA_SECURITY_CREDENTIAL', '')

    def reconcile_transactions(self, start_date=None, end_date=None):
        """Reconcile M-Pesa transactions for a given date range"""
        try:
            if not start_date:
                start_date = timezone.now().date() - timedelta(days=1)
            if not end_date:
                end_date = timezone.now().date()

            access_token = self.get_access_token()
            if not access_token:
                return {'success': False, 'error': 'Failed to get access token'}

            url = f"{self.base_url}/mpesa/transactionstatus/v1/query"

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            # This is a simplified reconciliation - in production you'd query specific transactions
            payload = {
                "Initiator": settings.MPESA_INITIATOR_NAME,
                "SecurityCredential": self.get_security_credential(),
                "CommandID": "TransactionStatusQuery",
                "TransactionID": "SAMPLE_TRANSACTION_ID",  # You'd iterate through actual transaction IDs
                "PartyA": settings.MPESA_SHORTCODE,
                "IdentifierType": "4",
                "ResultURL": f"{settings.SITE_URL}/payments/mpesa/reconciliation-result/",
                "QueueTimeOutURL": f"{settings.SITE_URL}/payments/mpesa/reconciliation-timeout/",
                "Remarks": f"Reconciliation for {start_date} to {end_date}",
                "Occasion": "Transaction Reconciliation"
            }

            # In a real implementation, you'd make multiple API calls for different transactions
            # and compare with your local database records

            return {
                'success': True,
                'message': 'Reconciliation initiated',
                'start_date': str(start_date),
                'end_date': str(end_date)
            }

        except Exception as e:
            logger.error(f"Error during M-Pesa reconciliation: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def retry_failed_transaction(self, transaction_reference, max_retries=3):
        """Retry failed M-Pesa transaction with exponential backoff"""
        try:
            from ..models import MPesaTransaction

            # Get the failed transaction
            try:
                mpesa_transaction = MPesaTransaction.objects.get(checkout_request_id=transaction_reference)
            except MPesaTransaction.DoesNotExist:
                return {'success': False, 'error': 'Transaction not found'}

            if mpesa_transaction.result_code == '0':
                return {'success': True, 'message': 'Transaction already successful'}

            retry_count = 0
            while retry_count < max_retries:
                # Query transaction status
                status_result = self.query_stk_status(mpesa_transaction.checkout_request_id)

                if status_result.get('success'):
                    data = status_result.get('data', {})
                    result_code = data.get('ResultCode')

                    if result_code == '0':
                        # Transaction successful
                        mpesa_transaction.result_code = result_code
                        mpesa_transaction.result_description = data.get('ResultDesc')
                        mpesa_transaction.save()

                        return {
                            'success': True,
                            'message': 'Transaction retry successful',
                            'retry_count': retry_count + 1
                        }
                    elif result_code in ['1032', '1037']:  # Still processing
                        retry_count += 1
                        if retry_count < max_retries:
                            # Exponential backoff: wait 2^retry_count seconds
                            wait_time = 2 ** retry_count
                            time.sleep(wait_time)
                        continue
                    else:
                        # Transaction failed permanently
                        return {
                            'success': False,
                            'error': f'Transaction failed permanently: {data.get("ResultDesc")}',
                            'retry_count': retry_count + 1
                        }
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        time.sleep(2 ** retry_count)
                    continue

            return {
                'success': False,
                'error': 'Maximum retries exceeded',
                'retry_count': retry_count
            }

        except Exception as e:
            logger.error(f"Error retrying M-Pesa transaction: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


# Create a singleton instance
mpesa_service = MPesaService()
