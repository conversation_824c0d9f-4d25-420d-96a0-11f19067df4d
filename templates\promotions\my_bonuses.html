{% extends 'base.html' %}
{% load static %}

{% block title %}My Bonuses - ZBet{% endblock %}

{% block extra_css %}
<style>
.bonuses-container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.bonuses-header {
    background: #1e1e2e;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
    border: 1px solid #333;
}

.bonuses-title {
    font-size: 2.5rem;
    color: #ffd700;
    margin-bottom: 15px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: #2a2a3e;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #444;
}

.stat-value {
    color: #ffd700;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #aaa;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.bonuses-section {
    background: #1e1e2e;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.section-title {
    color: #ffd700;
    font-size: 1.5rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bonus-card {
    background: #2a2a3e;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    border: 1px solid #444;
    transition: all 0.3s ease;
}

.bonus-card:hover {
    border-color: #ffd700;
    transform: translateY(-2px);
}

.bonus-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.bonus-info {
    flex: 1;
}

.bonus-title {
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.bonus-source {
    color: #aaa;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.bonus-reference {
    color: #ffd700;
    font-size: 0.8rem;
    font-family: monospace;
}

.bonus-amounts {
    text-align: right;
    flex-shrink: 0;
}

.bonus-amount {
    color: #00ff88;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.original-amount {
    color: #aaa;
    font-size: 0.9rem;
}

.bonus-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-top: 10px;
}

.status-active {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border: 1px solid #00ff88;
}

.status-wagering {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    border: 1px solid #ffd700;
}

.status-completed {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
    border: 1px solid #00ff00;
}

.status-expired {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 1px solid #ff6b6b;
}

.status-forfeited {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
    border: 1px solid #ff0000;
}

.wagering-section {
    margin-top: 20px;
    padding: 15px;
    background: #1e1e2e;
    border-radius: 8px;
    border: 1px solid #333;
}

.wagering-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.wagering-title {
    color: white;
    font-weight: bold;
}

.wagering-percentage {
    color: #ffd700;
    font-weight: bold;
}

.progress-bar {
    background: #333;
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    height: 100%;
    transition: width 0.3s ease;
}

.wagering-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    font-size: 0.9rem;
}

.wagering-item {
    text-align: center;
}

.wagering-label {
    color: #aaa;
    margin-bottom: 5px;
}

.wagering-value {
    color: white;
    font-weight: bold;
}

.bonus-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-forfeit {
    background: transparent;
    color: #ff6b6b;
    border: 2px solid #ff6b6b;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-forfeit:hover {
    background: #ff6b6b;
    color: white;
}

.bonus-terms {
    margin-top: 15px;
    padding: 10px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ffd700;
}

.terms-title {
    color: #ffd700;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.terms-text {
    color: #ccc;
    font-size: 0.8rem;
    line-height: 1.4;
}

.no-bonuses {
    text-align: center;
    padding: 60px 20px;
    color: #aaa;
}

.no-bonuses-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #555;
}

.breadcrumb {
    background: rgba(30, 30, 46, 0.8);
    padding: 15px 0;
    margin-bottom: 20px;
}

.breadcrumb-nav {
    color: #aaa;
    font-size: 0.9rem;
}

.breadcrumb-nav a {
    color: #ffd700;
    text-decoration: none;
}

@media (max-width: 768px) {
    .bonuses-title {
        font-size: 2rem;
    }
    
    .bonus-header {
        flex-direction: column;
        text-align: center;
    }
    
    .bonus-amounts {
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wagering-details {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="bonuses-container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <div class="container">
            <nav class="breadcrumb-nav">
                <a href="{% url 'promotions:home' %}">Promotions</a> 
                <span class="mx-2">›</span> 
                <span>My Bonuses</span>
            </nav>
        </div>
    </div>

    <div class="container">
        <!-- Bonuses Header -->
        <div class="bonuses-header">
            <h1 class="bonuses-title">🎁 My Bonuses</h1>
            <p>Track your active bonuses and wagering progress</p>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ stats.total_bonuses }}</div>
                    <div class="stat-label">Total Bonuses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.active_bonuses }}</div>
                    <div class="stat-label">Active Bonuses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">KES {{ stats.total_awarded|floatformat:2 }}</div>
                    <div class="stat-label">Total Awarded</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">KES {{ stats.total_wagered|floatformat:2 }}</div>
                    <div class="stat-label">Total Wagered</div>
                </div>
            </div>
        </div>

        <!-- Active Bonuses -->
        {% if active_bonuses %}
        <div class="bonuses-section">
            <h2 class="section-title">🎯 Active Bonuses</h2>
            {% for bonus in active_bonuses %}
            <div class="bonus-card">
                <div class="bonus-header">
                    <div class="bonus-info">
                        <div class="bonus-title">{{ bonus.bonus_type.name }}</div>
                        <div class="bonus-source">{{ bonus.get_source_display }}</div>
                        <div class="bonus-reference">Ref: {{ bonus.reference }}</div>
                        <div class="bonus-status status-{{ bonus.status|lower }}">{{ bonus.get_status_display }}</div>
                    </div>
                    <div class="bonus-amounts">
                        <div class="bonus-amount">KES {{ bonus.remaining_amount|floatformat:2 }}</div>
                        <div class="original-amount">of KES {{ bonus.bonus_amount|floatformat:2 }}</div>
                    </div>
                </div>

                {% if bonus.wagering_requirement > 0 %}
                <div class="wagering-section">
                    <div class="wagering-header">
                        <div class="wagering-title">Wagering Progress</div>
                        <div class="wagering-percentage">{{ bonus.wagering_progress|floatformat:1 }}%</div>
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ bonus.wagering_progress }}%"></div>
                    </div>
                    
                    <div class="wagering-details">
                        <div class="wagering-item">
                            <div class="wagering-label">Required</div>
                            <div class="wagering-value">KES {{ bonus.wagering_requirement|floatformat:2 }}</div>
                        </div>
                        <div class="wagering-item">
                            <div class="wagering-label">Completed</div>
                            <div class="wagering-value">KES {{ bonus.wagered_amount|floatformat:2 }}</div>
                        </div>
                        <div class="wagering-item">
                            <div class="wagering-label">Remaining</div>
                            <div class="wagering-value">KES {{ bonus.remaining_wagering|floatformat:2 }}</div>
                        </div>
                        {% if bonus.expires_at %}
                        <div class="wagering-item">
                            <div class="wagering-label">Expires</div>
                            <div class="wagering-value">{{ bonus.expires_at|date:"M j, Y" }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                {% if bonus.promotion %}
                <div class="bonus-terms">
                    <div class="terms-title">Terms & Conditions</div>
                    <div class="terms-text">{{ bonus.promotion.terms_and_conditions|default:"Standard terms and conditions apply." }}</div>
                </div>
                {% endif %}

                {% if bonus.status in 'ACTIVE,WAGERING' %}
                <div class="bonus-actions">
                    <button class="btn-forfeit" onclick="forfeitBonus('{{ bonus.id }}')">
                        Forfeit Bonus
                    </button>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- All Bonuses History -->
        <div class="bonuses-section">
            <h2 class="section-title">📋 Bonus History</h2>
            {% if bonuses %}
            {% for bonus in bonuses %}
            <div class="bonus-card">
                <div class="bonus-header">
                    <div class="bonus-info">
                        <div class="bonus-title">{{ bonus.bonus_type.name }}</div>
                        <div class="bonus-source">{{ bonus.get_source_display }}</div>
                        <div class="bonus-reference">Ref: {{ bonus.reference }}</div>
                        <div class="bonus-status status-{{ bonus.status|lower }}">{{ bonus.get_status_display }}</div>
                    </div>
                    <div class="bonus-amounts">
                        <div class="bonus-amount">KES {{ bonus.bonus_amount|floatformat:2 }}</div>
                        <div class="original-amount">{{ bonus.awarded_at|date:"M j, Y" }}</div>
                    </div>
                </div>

                {% if bonus.wagering_requirement > 0 %}
                <div class="wagering-details" style="margin-top: 15px;">
                    <div class="wagering-item">
                        <div class="wagering-label">Wagering Progress</div>
                        <div class="wagering-value">{{ bonus.wagering_progress|floatformat:1 }}%</div>
                    </div>
                    <div class="wagering-item">
                        <div class="wagering-label">Wagered</div>
                        <div class="wagering-value">KES {{ bonus.wagered_amount|floatformat:2 }}</div>
                    </div>
                    {% if bonus.completed_at %}
                    <div class="wagering-item">
                        <div class="wagering-label">Completed</div>
                        <div class="wagering-value">{{ bonus.completed_at|date:"M j, Y" }}</div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
            {% else %}
            <div class="no-bonuses">
                <div class="no-bonuses-icon">🎁</div>
                <h3>No Bonuses Yet</h3>
                <p>You haven't claimed any bonuses yet. Check out our amazing promotions!</p>
                <a href="{% url 'promotions:home' %}" style="display: inline-block; margin-top: 20px; padding: 15px 30px; background: linear-gradient(45deg, #ffd700, #ffed4e); color: #1a1a2e; text-decoration: none; border-radius: 10px; font-weight: bold;">
                    Browse Promotions
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function forfeitBonus(bonusId) {
    if (confirm('Are you sure you want to forfeit this bonus? This action cannot be undone.')) {
        fetch(`/promotions/forfeit-bonus/${bonusId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Bonus forfeited successfully');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while forfeiting the bonus');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
