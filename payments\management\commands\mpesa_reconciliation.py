from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from payments.services.mpesa_service import mpesa_service
from payments.models import Transaction, MPesaTransaction
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Run M-Pesa transaction reconciliation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='Date to reconcile (YYYY-MM-DD format). Defaults to yesterday.',
        )
        parser.add_argument(
            '--days',
            type=int,
            default=1,
            help='Number of days to reconcile (default: 1)',
        )
        parser.add_argument(
            '--retry-failed',
            action='store_true',
            help='Retry failed transactions',
        )

    def handle(self, *args, **options):
        self.stdout.write('Starting M-Pesa reconciliation...')
        
        # Determine date range
        if options['date']:
            try:
                start_date = datetime.strptime(options['date'], '%Y-%m-%d').date()
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('Invalid date format. Use YYYY-MM-DD.')
                )
                return
        else:
            start_date = timezone.now().date() - timedelta(days=1)
        
        end_date = start_date + timedelta(days=options['days'] - 1)
        
        self.stdout.write(f'Reconciling transactions from {start_date} to {end_date}')
        
        # Get M-Pesa transactions in the date range
        mpesa_transactions = MPesaTransaction.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).select_related('transaction')
        
        total_transactions = mpesa_transactions.count()
        processed_count = 0
        success_count = 0
        failed_count = 0
        discrepancy_count = 0
        
        self.stdout.write(f'Found {total_transactions} M-Pesa transactions to reconcile')
        
        for mpesa_tx in mpesa_transactions:
            processed_count += 1
            
            try:
                # Check transaction status with M-Pesa
                status_result = mpesa_service.query_stk_status(mpesa_tx.checkout_request_id)
                
                if status_result['success']:
                    status_data = status_result['data']
                    mpesa_result_code = status_data.get('ResultCode')
                    
                    # Compare with local record
                    local_result_code = mpesa_tx.result_code
                    
                    if str(mpesa_result_code) != str(local_result_code):
                        # Discrepancy found
                        discrepancy_count += 1
                        self.stdout.write(
                            self.style.WARNING(
                                f'Discrepancy found for transaction {mpesa_tx.checkout_request_id}: '
                                f'Local={local_result_code}, M-Pesa={mpesa_result_code}'
                            )
                        )
                        
                        # Update local record with M-Pesa data
                        mpesa_tx.result_code = mpesa_result_code
                        mpesa_tx.result_description = status_data.get('ResultDesc')
                        mpesa_tx.save()
                        
                        # Update main transaction status if needed
                        transaction = mpesa_tx.transaction
                        if mpesa_result_code == '0' and transaction.status != 'COMPLETED':
                            transaction.mark_completed()
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f'Updated transaction {transaction.reference} to COMPLETED'
                                )
                            )
                        elif mpesa_result_code != '0' and transaction.status == 'COMPLETED':
                            transaction.status = 'FAILED'
                            transaction.save()
                            self.stdout.write(
                                self.style.WARNING(
                                    f'Updated transaction {transaction.reference} to FAILED'
                                )
                            )
                    
                    success_count += 1
                    
                else:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f'Failed to query status for {mpesa_tx.checkout_request_id}: '
                            f'{status_result.get("message")}'
                        )
                    )
                
                # Progress indicator
                if processed_count % 10 == 0:
                    self.stdout.write(f'Processed {processed_count}/{total_transactions} transactions...')
                    
            except Exception as e:
                failed_count += 1
                logger.error(f'Error reconciling transaction {mpesa_tx.checkout_request_id}: {str(e)}')
                self.stdout.write(
                    self.style.ERROR(
                        f'Error processing {mpesa_tx.checkout_request_id}: {str(e)}'
                    )
                )
        
        # Retry failed transactions if requested
        if options['retry_failed']:
            self.stdout.write('Retrying failed transactions...')
            
            failed_transactions = MPesaTransaction.objects.filter(
                created_at__date__range=[start_date, end_date],
                result_code__in=['1032', '1037', '1001']  # Retryable error codes
            ).select_related('transaction')
            
            retry_count = 0
            retry_success_count = 0
            
            for mpesa_tx in failed_transactions:
                try:
                    retry_result = mpesa_service.retry_failed_transaction(
                        mpesa_tx.checkout_request_id,
                        max_retries=2
                    )
                    
                    if retry_result['success']:
                        retry_success_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'Successfully retried transaction {mpesa_tx.checkout_request_id}'
                            )
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f'Retry failed for {mpesa_tx.checkout_request_id}: '
                                f'{retry_result.get("error")}'
                            )
                        )
                    
                    retry_count += 1
                    
                except Exception as e:
                    logger.error(f'Error retrying transaction {mpesa_tx.checkout_request_id}: {str(e)}')
            
            self.stdout.write(f'Retried {retry_count} transactions, {retry_success_count} successful')
        
        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nReconciliation completed!\n'
                f'Total transactions: {total_transactions}\n'
                f'Successfully processed: {success_count}\n'
                f'Failed to process: {failed_count}\n'
                f'Discrepancies found: {discrepancy_count}\n'
                f'Date range: {start_date} to {end_date}'
            )
        )
        
        # Run general reconciliation with M-Pesa API
        self.stdout.write('Running general reconciliation with M-Pesa API...')
        reconciliation_result = mpesa_service.reconcile_transactions(start_date, end_date)
        
        if reconciliation_result['success']:
            self.stdout.write(
                self.style.SUCCESS(
                    f'General reconciliation initiated: {reconciliation_result["message"]}'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR(
                    f'General reconciliation failed: {reconciliation_result.get("error")}'
                )
            )
