{% extends 'base.html' %}
{% load static %}

{% block title %}{{ game.name }} - ZBet Casino{% endblock %}

{% block extra_css %}
<style>
.game-container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.slot-machine {
    max-width: 800px;
    margin: 0 auto;
    background: #2a2a3e;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.game-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.game-title {
    font-size: 2.5rem;
    color: #ffd700;
    margin-bottom: 10px;
}

.game-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    color: #aaa;
}

.reels-container {
    background: #1a1a2e;
    border: 3px solid #ffd700;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.reels {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.reel {
    background: #333;
    border: 2px solid #555;
    border-radius: 10px;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    font-size: 2rem;
    transition: all 0.5s ease;
}

.reel.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotateY(0deg); }
    100% { transform: rotateY(360deg); }
}

.symbol {
    padding: 10px;
    border-radius: 5px;
    background: rgba(255, 215, 0, 0.1);
}

.winning-line {
    border: 3px solid #00ff00 !important;
    box-shadow: 0 0 20px #00ff00;
}

.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1e1e2e;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.bet-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bet-amount {
    background: #333;
    color: white;
    border: 1px solid #555;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 1.1rem;
    width: 120px;
    text-align: center;
}

.btn-bet {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    background: #ffd700;
    color: #1a1a2e;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-bet:hover {
    background: #ffed4e;
}

.btn-bet:disabled {
    background: #666;
    cursor: not-allowed;
}

.spin-button {
    padding: 15px 40px;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.spin-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.spin-button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.game-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: #1e1e2e;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #333;
}

.stat-label {
    color: #aaa;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-value {
    color: #ffd700;
    font-size: 1.2rem;
    font-weight: bold;
}

.result-display {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-win {
    background: rgba(0, 255, 0, 0.1);
    border: 2px solid #00ff00;
    color: #00ff00;
}

.result-loss {
    background: rgba(255, 0, 0, 0.1);
    border: 2px solid #ff0000;
    color: #ff0000;
}

.result-jackpot {
    background: rgba(255, 215, 0, 0.2);
    border: 2px solid #ffd700;
    color: #ffd700;
    animation: jackpot-glow 1s ease-in-out infinite alternate;
}

@keyframes jackpot-glow {
    0% { box-shadow: 0 0 20px #ffd700; }
    100% { box-shadow: 0 0 40px #ffd700; }
}

.balance-display {
    background: #1e1e2e;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #333;
}

.balance-label {
    color: #aaa;
    margin-bottom: 5px;
}

.balance-amount {
    color: #ffd700;
    font-size: 1.5rem;
    font-weight: bold;
}

.demo-badge {
    background: #ff6b6b;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}
</style>
{% endblock %}

{% block content %}
<div class="game-container">
    <div class="container">
        <div class="slot-machine">
            <!-- Game Header -->
            <div class="game-header">
                <h1 class="game-title">{{ game.name }}</h1>
                <div class="game-info">
                    <span>Provider: {{ game.provider.name }}</span>
                    <span>RTP: {{ game.rtp_percentage }}%</span>
                    <span>Volatility: {{ game.volatility }}</span>
                    {% if is_demo %}
                    <span class="demo-badge">DEMO MODE</span>
                    {% endif %}
                </div>
            </div>

            <!-- Game Stats -->
            <div class="game-stats">
                <div class="stat-card">
                    <div class="stat-label">Total Bet</div>
                    <div class="stat-value" id="total-bet">KES 0.00</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Total Win</div>
                    <div class="stat-value" id="total-win">KES 0.00</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Net Result</div>
                    <div class="stat-value" id="net-result">KES 0.00</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Rounds</div>
                    <div class="stat-value" id="rounds-played">0</div>
                </div>
            </div>

            <!-- Balance Display -->
            <div class="balance-display">
                <div class="balance-label">{% if is_demo %}Demo {% endif %}Balance</div>
                <div class="balance-amount" id="current-balance">KES {{ wallet_balance|floatformat:2 }}</div>
            </div>

            <!-- Result Display -->
            <div id="result-display" class="result-display" style="display: none;">
                <div id="result-message"></div>
            </div>

            <!-- Slot Reels -->
            <div class="reels-container">
                <div class="reels" id="reels">
                    <!-- Reels will be populated by JavaScript -->
                </div>
            </div>

            <!-- Game Controls -->
            <div class="controls">
                <div class="bet-controls">
                    <label style="color: white;">Bet Amount:</label>
                    <button class="btn-bet" onclick="adjustBet(-1)">-</button>
                    <input type="number" id="bet-amount" class="bet-amount" value="{{ game.min_bet }}" 
                           min="{{ game.min_bet }}" max="{{ game.max_bet }}" step="1">
                    <button class="btn-bet" onclick="adjustBet(1)">+</button>
                    <button class="btn-bet" onclick="setBet({{ game.max_bet }})">MAX</button>
                </div>
                
                <button id="spin-button" class="spin-button" onclick="spin()">
                    SPIN
                </button>
                
                <div>
                    <button class="btn-bet" onclick="endSession()" style="background: #666;">
                        Exit Game
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const gameData = {
    sessionToken: '{{ session.session_token }}',
    minBet: {{ game.min_bet }},
    maxBet: {{ game.max_bet }},
    isDemo: {{ is_demo|yesno:"true,false" }},
    currentBalance: {{ wallet_balance }},
    symbols: ['🍒', '🍋', '🍊', '🍇', '🔔', '⭐', '💎']
};

let isSpinning = false;

// Initialize reels
function initializeReels() {
    const reelsContainer = document.getElementById('reels');
    for (let i = 0; i < 5; i++) {
        const reel = document.createElement('div');
        reel.className = 'reel';
        reel.id = `reel-${i}`;
        
        for (let j = 0; j < 3; j++) {
            const symbol = document.createElement('div');
            symbol.className = 'symbol';
            symbol.textContent = gameData.symbols[Math.floor(Math.random() * gameData.symbols.length)];
            reel.appendChild(symbol);
        }
        
        reelsContainer.appendChild(reel);
    }
}

function adjustBet(direction) {
    const betInput = document.getElementById('bet-amount');
    let currentBet = parseFloat(betInput.value);
    let newBet = currentBet + direction;
    
    if (newBet >= gameData.minBet && newBet <= gameData.maxBet) {
        betInput.value = newBet;
    }
}

function setBet(amount) {
    document.getElementById('bet-amount').value = amount;
}

function spin() {
    if (isSpinning) return;
    
    const betAmount = parseFloat(document.getElementById('bet-amount').value);
    
    if (betAmount < gameData.minBet || betAmount > gameData.maxBet) {
        alert(`Bet amount must be between KES ${gameData.minBet} and KES ${gameData.maxBet}`);
        return;
    }
    
    if (!gameData.isDemo && betAmount > gameData.currentBalance) {
        alert('Insufficient balance');
        return;
    }
    
    isSpinning = true;
    document.getElementById('spin-button').disabled = true;
    document.getElementById('result-display').style.display = 'none';
    
    // Add spinning animation
    document.querySelectorAll('.reel').forEach(reel => {
        reel.classList.add('spinning');
    });
    
    // Place bet via API
    fetch('/casino/api/place-bet/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            session_token: gameData.sessionToken,
            bet_amount: betAmount,
            game_data: {}
        })
    })
    .then(response => response.json())
    .then(data => {
        setTimeout(() => {
            // Remove spinning animation
            document.querySelectorAll('.reel').forEach(reel => {
                reel.classList.remove('spinning');
            });
            
            if (data.success) {
                displayResult(data.result, data.session, data.balance);
            } else {
                alert('Error: ' + data.error);
            }
            
            isSpinning = false;
            document.getElementById('spin-button').disabled = false;
        }, 2000); // 2 second spin animation
    })
    .catch(error => {
        console.error('Error:', error);
        isSpinning = false;
        document.getElementById('spin-button').disabled = false;
        document.querySelectorAll('.reel').forEach(reel => {
            reel.classList.remove('spinning');
        });
    });
}

function displayResult(result, session, balance) {
    // Update reels with result
    if (result.game_data && result.game_data.reels) {
        result.game_data.reels.forEach((reel, reelIndex) => {
            const reelElement = document.getElementById(`reel-${reelIndex}`);
            const symbols = reelElement.querySelectorAll('.symbol');
            
            reel.forEach((symbol, symbolIndex) => {
                if (symbols[symbolIndex]) {
                    symbols[symbolIndex].textContent = symbol;
                }
            });
            
            // Highlight winning line
            if (result.game_data.winning_line && result.type === 'WIN') {
                reelElement.classList.add('winning-line');
                setTimeout(() => {
                    reelElement.classList.remove('winning-line');
                }, 3000);
            }
        });
    }
    
    // Update statistics
    document.getElementById('total-bet').textContent = `KES ${session.total_bet.toFixed(2)}`;
    document.getElementById('total-win').textContent = `KES ${session.total_win.toFixed(2)}`;
    document.getElementById('net-result').textContent = `KES ${session.net_result.toFixed(2)}`;
    document.getElementById('rounds-played').textContent = session.rounds_played;
    
    // Update balance
    if (balance !== null) {
        gameData.currentBalance = balance;
        document.getElementById('current-balance').textContent = `KES ${balance.toFixed(2)}`;
    }
    
    // Show result
    const resultDisplay = document.getElementById('result-display');
    const resultMessage = document.getElementById('result-message');
    
    resultDisplay.className = 'result-display';
    
    if (result.type === 'WIN') {
        resultDisplay.classList.add('result-win');
        resultMessage.innerHTML = `🎉 WIN! <br>KES ${result.win_amount.toFixed(2)} (${result.multiplier.toFixed(1)}x)`;
    } else if (result.type === 'JACKPOT') {
        resultDisplay.classList.add('result-jackpot');
        resultMessage.innerHTML = `🏆 JACKPOT! <br>KES ${result.win_amount.toFixed(2)}`;
    } else {
        resultDisplay.classList.add('result-loss');
        resultMessage.innerHTML = `😔 No win this time`;
    }
    
    if (result.jackpot_win) {
        resultMessage.innerHTML += `<br>🏆 JACKPOT: ${result.jackpot_win.jackpot_name} - KES ${result.jackpot_win.amount.toFixed(2)}`;
    }
    
    resultDisplay.style.display = 'flex';
}

function endSession() {
    if (confirm('Are you sure you want to exit the game?')) {
        fetch(`/casino/api/end-session/${gameData.sessionToken}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Session ended!\nTotal Bet: KES ${data.session_summary.total_bet}\nTotal Win: KES ${data.session_summary.total_win}\nNet Result: KES ${data.session_summary.net_result}`);
                window.location.href = '/casino/';
            }
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize the game
initializeReels();
</script>
{% endblock %}
