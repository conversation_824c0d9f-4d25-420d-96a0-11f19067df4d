from django.core.management.base import BaseCommand
from payments.models import PaymentMethod, TransactionFee
from decimal import Decimal


class Command(BaseCommand):
    help = 'Set up transaction fees for payment methods'

    def handle(self, *args, **options):
        self.stdout.write('Setting up transaction fees...')
        
        # Get payment methods
        try:
            mpesa = PaymentMethod.objects.get(name='M-Pesa')
            airtel = PaymentMethod.objects.get(name='Airtel Money')
        except PaymentMethod.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Payment methods not found. Run setup_payment_methods first.')
            )
            return
        
        # M-Pesa Deposit Fees (Based on real M-Pesa rates)
        mpesa_deposit_fees = [
            # Amount range: 1-49 KES, Fee: 0 KES
            {
                'min_amount': Decimal('1'),
                'max_amount': Decimal('49'),
                'fee_amount': Decimal('0'),
                'fee_type': 'FIXED'
            },
            # Amount range: 50-100 KES, Fee: 0 KES
            {
                'min_amount': Decimal('50'),
                'max_amount': Decimal('100'),
                'fee_amount': Decimal('0'),
                'fee_type': 'FIXED'
            },
            # Amount range: 101-500 KES, Fee: 7 KES
            {
                'min_amount': Decimal('101'),
                'max_amount': Decimal('500'),
                'fee_amount': Decimal('7'),
                'fee_type': 'FIXED'
            },
            # Amount range: 501-1000 KES, Fee: 13 KES
            {
                'min_amount': Decimal('501'),
                'max_amount': Decimal('1000'),
                'fee_amount': Decimal('13'),
                'fee_type': 'FIXED'
            },
            # Amount range: 1001-1500 KES, Fee: 23 KES
            {
                'min_amount': Decimal('1001'),
                'max_amount': Decimal('1500'),
                'fee_amount': Decimal('23'),
                'fee_type': 'FIXED'
            },
            # Amount range: 1501-2500 KES, Fee: 33 KES
            {
                'min_amount': Decimal('1501'),
                'max_amount': Decimal('2500'),
                'fee_amount': Decimal('33'),
                'fee_type': 'FIXED'
            },
            # Amount range: 2501-3500 KES, Fee: 53 KES
            {
                'min_amount': Decimal('2501'),
                'max_amount': Decimal('3500'),
                'fee_amount': Decimal('53'),
                'fee_type': 'FIXED'
            },
            # Amount range: 3501-5000 KES, Fee: 57 KES
            {
                'min_amount': Decimal('3501'),
                'max_amount': Decimal('5000'),
                'fee_amount': Decimal('57'),
                'fee_type': 'FIXED'
            },
            # Amount range: 5001-7500 KES, Fee: 78 KES
            {
                'min_amount': Decimal('5001'),
                'max_amount': Decimal('7500'),
                'fee_amount': Decimal('78'),
                'fee_type': 'FIXED'
            },
            # Amount range: 7501-10000 KES, Fee: 90 KES
            {
                'min_amount': Decimal('7501'),
                'max_amount': Decimal('10000'),
                'fee_amount': Decimal('90'),
                'fee_type': 'FIXED'
            },
            # Amount range: 10001+ KES, Fee: 1% (min 100, max 300)
            {
                'min_amount': Decimal('10001'),
                'max_amount': None,
                'fee_amount': Decimal('1'),  # 1%
                'fee_type': 'PERCENTAGE',
                'min_fee': Decimal('100'),
                'max_fee': Decimal('300')
            },
        ]
        
        # Create M-Pesa deposit fees
        for fee_data in mpesa_deposit_fees:
            fee, created = TransactionFee.objects.get_or_create(
                payment_method=mpesa,
                transaction_type='DEPOSIT',
                min_amount=fee_data['min_amount'],
                max_amount=fee_data['max_amount'],
                defaults={
                    'fee_type': fee_data['fee_type'],
                    'fee_amount': fee_data['fee_amount'],
                    'min_fee': fee_data.get('min_fee', Decimal('0')),
                    'max_fee': fee_data.get('max_fee'),
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created M-Pesa deposit fee: {fee_data["min_amount"]}-{fee_data["max_amount"]} = {fee_data["fee_amount"]}')
        
        # M-Pesa Withdrawal Fees (Higher than deposits)
        mpesa_withdrawal_fees = [
            # Amount range: 50-100 KES, Fee: 11 KES
            {
                'min_amount': Decimal('50'),
                'max_amount': Decimal('100'),
                'fee_amount': Decimal('11'),
                'fee_type': 'FIXED'
            },
            # Amount range: 101-500 KES, Fee: 15 KES
            {
                'min_amount': Decimal('101'),
                'max_amount': Decimal('500'),
                'fee_amount': Decimal('15'),
                'fee_type': 'FIXED'
            },
            # Amount range: 501-1000 KES, Fee: 25 KES
            {
                'min_amount': Decimal('501'),
                'max_amount': Decimal('1000'),
                'fee_amount': Decimal('25'),
                'fee_type': 'FIXED'
            },
            # Amount range: 1001-1500 KES, Fee: 35 KES
            {
                'min_amount': Decimal('1001'),
                'max_amount': Decimal('1500'),
                'fee_amount': Decimal('35'),
                'fee_type': 'FIXED'
            },
            # Amount range: 1501-2500 KES, Fee: 50 KES
            {
                'min_amount': Decimal('1501'),
                'max_amount': Decimal('2500'),
                'fee_amount': Decimal('50'),
                'fee_type': 'FIXED'
            },
            # Amount range: 2501-3500 KES, Fee: 69 KES
            {
                'min_amount': Decimal('2501'),
                'max_amount': Decimal('3500'),
                'fee_amount': Decimal('69'),
                'fee_type': 'FIXED'
            },
            # Amount range: 3501-5000 KES, Fee: 87 KES
            {
                'min_amount': Decimal('3501'),
                'max_amount': Decimal('5000'),
                'fee_amount': Decimal('87'),
                'fee_type': 'FIXED'
            },
            # Amount range: 5001-7500 KES, Fee: 115 KES
            {
                'min_amount': Decimal('5001'),
                'max_amount': Decimal('7500'),
                'fee_amount': Decimal('115'),
                'fee_type': 'FIXED'
            },
            # Amount range: 7501-10000 KES, Fee: 167 KES
            {
                'min_amount': Decimal('7501'),
                'max_amount': Decimal('10000'),
                'fee_amount': Decimal('167'),
                'fee_type': 'FIXED'
            },
            # Amount range: 10001+ KES, Fee: 2% (min 200, max 500)
            {
                'min_amount': Decimal('10001'),
                'max_amount': None,
                'fee_amount': Decimal('2'),  # 2%
                'fee_type': 'PERCENTAGE',
                'min_fee': Decimal('200'),
                'max_fee': Decimal('500')
            },
        ]
        
        # Create M-Pesa withdrawal fees
        for fee_data in mpesa_withdrawal_fees:
            fee, created = TransactionFee.objects.get_or_create(
                payment_method=mpesa,
                transaction_type='WITHDRAWAL',
                min_amount=fee_data['min_amount'],
                max_amount=fee_data['max_amount'],
                defaults={
                    'fee_type': fee_data['fee_type'],
                    'fee_amount': fee_data['fee_amount'],
                    'min_fee': fee_data.get('min_fee', Decimal('0')),
                    'max_fee': fee_data.get('max_fee'),
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created M-Pesa withdrawal fee: {fee_data["min_amount"]}-{fee_data["max_amount"]} = {fee_data["fee_amount"]}')
        
        # Airtel Money Fees (Similar to M-Pesa but slightly lower)
        airtel_deposit_fees = [
            {
                'min_amount': Decimal('1'),
                'max_amount': Decimal('100'),
                'fee_amount': Decimal('0'),
                'fee_type': 'FIXED'
            },
            {
                'min_amount': Decimal('101'),
                'max_amount': Decimal('500'),
                'fee_amount': Decimal('5'),
                'fee_type': 'FIXED'
            },
            {
                'min_amount': Decimal('501'),
                'max_amount': Decimal('1000'),
                'fee_amount': Decimal('10'),
                'fee_type': 'FIXED'
            },
            {
                'min_amount': Decimal('1001'),
                'max_amount': None,
                'fee_amount': Decimal('1'),  # 1%
                'fee_type': 'PERCENTAGE',
                'min_fee': Decimal('15'),
                'max_fee': Decimal('200')
            },
        ]
        
        # Create Airtel Money deposit fees
        for fee_data in airtel_deposit_fees:
            fee, created = TransactionFee.objects.get_or_create(
                payment_method=airtel,
                transaction_type='DEPOSIT',
                min_amount=fee_data['min_amount'],
                max_amount=fee_data['max_amount'],
                defaults={
                    'fee_type': fee_data['fee_type'],
                    'fee_amount': fee_data['fee_amount'],
                    'min_fee': fee_data.get('min_fee', Decimal('0')),
                    'max_fee': fee_data.get('max_fee'),
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created Airtel Money deposit fee: {fee_data["min_amount"]}-{fee_data["max_amount"]} = {fee_data["fee_amount"]}')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up transaction fees!')
        )
