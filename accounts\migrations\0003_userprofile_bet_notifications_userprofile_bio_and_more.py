# Generated by Django 5.2.4 on 2025-07-05 18:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0002_twofactorauth"),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="bet_notifications",
            field=models.Bo<PERSON>anField(
                default=True, help_text="Receive betting-related notifications"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="bio",
            field=models.TextField(
                blank=True, help_text="Brief description about yourself", max_length=500
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="email_notifications",
            field=models.BooleanField(
                default=True, help_text="Receive notifications via email"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="marketing_emails",
            field=models.BooleanField(
                default=False, help_text="Receive marketing emails"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="promotion_notifications",
            field=models.<PERSON><PERSON>an<PERSON>ield(
                default=True, help_text="Receive promotion notifications"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="push_notifications",
            field=models.BooleanField(
                default=True, help_text="Receive push notifications"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="sms_notifications",
            field=models.BooleanField(
                default=True, help_text="Receive notifications via SMS"
            ),
        ),
    ]
