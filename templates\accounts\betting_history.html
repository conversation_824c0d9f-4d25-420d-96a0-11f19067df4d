{% extends 'base.html' %}

{% block title %}Betting History - ZBet{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card">
                <div class="card-header zbet-primary text-white">
                    <h6 class="mb-0">Account Menu</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'accounts:dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'accounts:profile_update' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user"></i> Profile
                    </a>
                    <a href="{% url 'accounts:betting_history' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-history"></i> Betting History
                    </a>
                    <a href="{% url 'accounts:transaction_history' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-exchange-alt"></i> Transactions
                    </a>
                    <a href="{% url 'accounts:account_balance' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-wallet"></i> Wallet
                    </a>
                    <a href="{% url 'accounts:notification_preferences' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Betting History</h2>
                    <p class="text-muted">Track your betting performance and history</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center bg-primary text-white">
                        <div class="card-body">
                            <h4>{{ stats.total_bets }}</h4>
                            <p class="mb-0">Total Bets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-info text-white">
                        <div class="card-body">
                            <h4>KSh {{ stats.total_stakes|floatformat:0 }}</h4>
                            <p class="mb-0">Total Stakes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-success text-white">
                        <div class="card-body">
                            <h4>KSh {{ stats.total_winnings|floatformat:0 }}</h4>
                            <p class="mb-0">Total Winnings</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-{% if stats.net_profit >= 0 %}success{% else %}danger{% endif %} text-white">
                        <div class="card-body">
                            <h4>KSh {{ stats.net_profit|floatformat:0 }}</h4>
                            <p class="mb-0">Net Profit</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-filter"></i> Filter Bets</h6>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="">All Statuses</option>
                                {% for value, label in bet_status_choices %}
                                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Bet Type</label>
                            <select name="bet_type" class="form-control">
                                <option value="">All Types</option>
                                {% for value, label in bet_type_choices %}
                                    <option value="{{ value }}" {% if bet_type_filter == value %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">From Date</label>
                            <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">To Date</label>
                            <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Filter</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Betting History Table -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-list"></i> Your Bets</h6>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Bet ID</th>
                                        <th>Type</th>
                                        <th>Stake</th>
                                        <th>Odds</th>
                                        <th>Potential Win</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for bet in page_obj %}
                                    <tr>
                                        <td>
                                            <strong>{{ bet.bet_id }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ bet.get_bet_type_display }}</span>
                                        </td>
                                        <td>KSh {{ bet.stake_amount|floatformat:0 }}</td>
                                        <td>{{ bet.total_odds|floatformat:2 }}</td>
                                        <td>KSh {{ bet.potential_win|floatformat:0 }}</td>
                                        <td>
                                            {% if bet.status == 'pending' %}
                                                <span class="badge bg-warning">{{ bet.get_status_display }}</span>
                                            {% elif bet.status == 'won' %}
                                                <span class="badge bg-success">{{ bet.get_status_display }}</span>
                                            {% elif bet.status == 'lost' %}
                                                <span class="badge bg-danger">{{ bet.get_status_display }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ bet.get_status_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ bet.placed_at|date:"M d, Y H:i" }}</td>
                                        <td>
                                            <a href="{% url 'accounts:bet_detail' bet.bet_id %}" class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <nav aria-label="Betting history pagination">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}{% if bet_type_filter %}&bet_type={{ bet_type_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if bet_type_filter %}&bet_type={{ bet_type_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if bet_type_filter %}&bet_type={{ bet_type_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if bet_type_filter %}&bet_type={{ bet_type_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5>No Betting History</h5>
                            <p class="text-muted">You haven't placed any bets yet. Start betting to see your history here!</p>
                            <a href="#" class="btn btn-zbet">Place Your First Bet</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
