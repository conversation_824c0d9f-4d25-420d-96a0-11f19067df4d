<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZBet API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .section {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #1e40af;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.8rem;
            margin-right: 10px;
        }
        .get { background: #10b981; color: white; }
        .post { background: #f59e0b; color: white; }
        .put { background: #3b82f6; color: white; }
        .delete { background: #ef4444; color: white; }
        .url {
            font-family: 'Courier New', monospace;
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9rem;
        }
        .description {
            margin-top: 8px;
            color: #6b7280;
        }
        .nav {
            background: white;
            padding: 20px 0;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }
        .nav li {
            margin: 0 20px;
        }
        .nav a {
            color: #1e40af;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        .nav a:hover {
            background-color: #f3f4f6;
        }
        .base-url {
            background: #1e40af;
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
        }
        .auth-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .auth-info h3 {
            margin-top: 0;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ZBet API Documentation</h1>
            <p>Comprehensive REST API for Sports Betting and Casino Platform</p>
        </div>
    </div>

    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#sports">Sports</a></li>
                <li><a href="#betting">Betting</a></li>
                <li><a href="#payments">Payments</a></li>
                <li><a href="#promotions">Promotions</a></li>
                <li><a href="#casino">Casino</a></li>
                <li><a href="#utilities">Utilities</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <div class="base-url">
            Base URL: http://127.0.0.1:8000/api/v1/
        </div>

        <div class="auth-info">
            <h3>🔐 Authentication</h3>
            <p>This API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:</p>
            <code>Authorization: Bearer &lt;your-jwt-token&gt;</code>
        </div>

        <section id="authentication" class="section">
            <h2>Authentication Endpoints</h2>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/auth/register/</span>
                <div class="description">Register a new user account</div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/auth/login/</span>
                <div class="description">Login and get JWT tokens</div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/auth/token/refresh/</span>
                <div class="description">Refresh JWT access token</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/auth/profile/</span>
                <div class="description">Get user profile information</div>
            </div>
        </section>

        <section id="sports" class="section">
            <h2>Sports Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/sports/</span>
                <div class="description">List all available sports</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/leagues/</span>
                <div class="description">List leagues with filtering by sport</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/teams/</span>
                <div class="description">List teams with filtering</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/matches/</span>
                <div class="description">List matches with comprehensive filtering</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/matches/{id}/</span>
                <div class="description">Get detailed match information</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/matches/{id}/selections/</span>
                <div class="description">Get betting selections for a match</div>
            </div>
        </section>

        <section id="betting" class="section">
            <h2>Betting Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/bets/</span>
                <div class="description">List user's betting history</div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/bets/place/</span>
                <div class="description">Place a new bet (single or multiple)</div>
            </div>
        </section>

        <section id="payments" class="section">
            <h2>Payment & Wallet Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/wallet/balance/</span>
                <div class="description">Get current wallet balance</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/wallet/transactions/</span>
                <div class="description">Get transaction history</div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/deposits/create/</span>
                <div class="description">Create M-Pesa deposit request</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/deposits/</span>
                <div class="description">Get deposit history</div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/withdrawals/create/</span>
                <div class="description">Create withdrawal request</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/withdrawals/</span>
                <div class="description">Get withdrawal history</div>
            </div>
        </section>

        <section id="promotions" class="section">
            <h2>Promotion Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/promotions/</span>
                <div class="description">List active promotions</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/bonuses/</span>
                <div class="description">Get user's bonuses</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/loyalty/</span>
                <div class="description">Get user loyalty status</div>
            </div>
        </section>

        <section id="casino" class="section">
            <h2>Casino Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/games/</span>
                <div class="description">List available casino games</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/game-sessions/</span>
                <div class="description">Get user's game session history</div>
            </div>
        </section>

        <section id="utilities" class="section">
            <h2>Utility Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/dashboard/</span>
                <div class="description">Get comprehensive user dashboard data</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/config/</span>
                <div class="description">Get app configuration and settings</div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/schema/</span>
                <div class="description">Get OpenAPI schema</div>
            </div>
        </section>

        <div class="section">
            <h2>📚 Additional Resources</h2>
            <p><strong>Interactive API Documentation:</strong></p>
            <ul>
                <li><a href="/api/v1/docs/" target="_blank">Swagger UI</a> - Interactive API explorer</li>
                <li><a href="/api/v1/redoc/" target="_blank">ReDoc</a> - Clean API documentation</li>
                <li><a href="/api/v1/schema/" target="_blank">OpenAPI Schema</a> - Raw schema file</li>
            </ul>
            
            <p><strong>Rate Limits:</strong></p>
            <ul>
                <li>Anonymous users: 100 requests/hour</li>
                <li>Authenticated users: 1000 requests/hour</li>
                <li>Betting operations: 500 requests/hour</li>
                <li>Payment operations: 100 requests/hour</li>
            </ul>
        </div>
    </div>
</body>
</html>
