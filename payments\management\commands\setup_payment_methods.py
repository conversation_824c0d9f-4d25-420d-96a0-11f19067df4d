from django.core.management.base import BaseCommand
from payments.models import PaymentMethod, PaymentLimit
from decimal import Decimal


class Command(BaseCommand):
    help = 'Setup initial payment methods and limits'

    def handle(self, *args, **options):
        # Create M-Pesa payment method
        mpesa, created = PaymentMethod.objects.get_or_create(
            code='MPESA',
            defaults={
                'name': 'M-Pesa',
                'description': 'Mobile money payment via Safaricom M-Pesa',
                'is_active': True,
                'priority': 1
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created payment method: {mpesa.name}')
            )
            
            # Create M-Pesa payment limits
            limits = [
                ('MIN_DEPOSIT', Decimal('10.00')),
                ('MAX_DEPOSIT', Decimal('100000.00')),
                ('MIN_WITHDRAWAL', Decimal('50.00')),
                ('MAX_WITHDRAWAL', Decimal('50000.00')),
                ('DAILY_DEPOSIT', Decimal('300000.00')),
                ('DAILY_WITHDRAWAL', Decimal('150000.00')),
            ]
            
            for limit_type, amount in limits:
                limit, limit_created = PaymentLimit.objects.get_or_create(
                    payment_method=mpesa,
                    limit_type=limit_type,
                    defaults={
                        'amount': amount,
                        'is_active': True
                    }
                )
                
                if limit_created:
                    self.stdout.write(
                        self.style.SUCCESS(f'Created {limit_type} limit: KES {amount}')
                    )
        else:
            self.stdout.write(
                self.style.WARNING(f'Payment method {mpesa.name} already exists')
            )
        
        # Create Airtel Money payment method (as backup)
        airtel, created = PaymentMethod.objects.get_or_create(
            code='AIRTEL',
            defaults={
                'name': 'Airtel Money',
                'description': 'Mobile money payment via Airtel Money',
                'is_active': False,  # Disabled by default until implemented
                'priority': 2
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created payment method: {airtel.name} (disabled)')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Payment method {airtel.name} already exists')
            )
        
        self.stdout.write(
            self.style.SUCCESS('Payment methods setup completed!')
        )
