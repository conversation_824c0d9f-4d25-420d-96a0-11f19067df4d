# Generated by Django 5.2.4 on 2025-07-05 19:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0005_responsiblegambling"),
    ]

    operations = [
        migrations.CreateModel(
            name="SuspiciousActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "activity_type",
                    models.CharField(
                        choices=[
                            ("multiple_failed_logins", "Multiple Failed Logins"),
                            ("login_from_new_location", "Login from New Location"),
                            ("unusual_betting_pattern", "Unusual Betting Pattern"),
                            ("rapid_transactions", "Rapid Transactions"),
                            ("account_takeover_attempt", "Account Takeover Attempt"),
                            ("bot_activity", "Bot Activity"),
                            ("vpn_usage", "VPN Usage"),
                            ("multiple_accounts", "Multiple Accounts"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "risk_level",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("open", "Open"),
                            ("investigating", "Investigating"),
                            ("resolved", "Resolved"),
                            ("false_positive", "False Positive"),
                        ],
                        default="open",
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("metadata", models.JSONField(default=dict)),
                ("investigation_notes", models.TextField(blank=True)),
                ("detected_at", models.DateTimeField(auto_now_add=True)),
                ("investigated_at", models.DateTimeField(blank=True, null=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "investigated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="investigated_activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="suspicious_activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Suspicious Activity",
                "verbose_name_plural": "Suspicious Activities",
                "db_table": "accounts_suspicious_activity",
                "ordering": ["-detected_at"],
            },
        ),
        migrations.CreateModel(
            name="UserDevice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("device_id", models.CharField(max_length=100, unique=True)),
                ("device_name", models.CharField(blank=True, max_length=200)),
                (
                    "device_type",
                    models.CharField(
                        choices=[
                            ("desktop", "Desktop"),
                            ("mobile", "Mobile"),
                            ("tablet", "Tablet"),
                            ("unknown", "Unknown"),
                        ],
                        default="unknown",
                        max_length=20,
                    ),
                ),
                ("user_agent", models.TextField()),
                ("browser_name", models.CharField(blank=True, max_length=100)),
                ("browser_version", models.CharField(blank=True, max_length=50)),
                ("os_name", models.CharField(blank=True, max_length=100)),
                ("os_version", models.CharField(blank=True, max_length=50)),
                ("ip_address", models.GenericIPAddressField()),
                ("country", models.CharField(blank=True, max_length=100)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("is_trusted", models.BooleanField(default=False)),
                ("is_blocked", models.BooleanField(default=False)),
                ("first_seen", models.DateTimeField(auto_now_add=True)),
                ("last_seen", models.DateTimeField(auto_now=True)),
                ("last_login", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="devices",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Device",
                "verbose_name_plural": "User Devices",
                "db_table": "accounts_user_device",
                "ordering": ["-last_seen"],
            },
        ),
        migrations.CreateModel(
            name="LoginAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username_attempted", models.CharField(max_length=150)),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("success", "Success"),
                            ("failed", "Failed"),
                            ("blocked", "Blocked"),
                        ],
                        max_length=20,
                    ),
                ),
                ("failure_reason", models.CharField(blank=True, max_length=200)),
                ("attempted_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="login_attempts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Login Attempt",
                "verbose_name_plural": "Login Attempts",
                "db_table": "accounts_login_attempt",
                "ordering": ["-attempted_at"],
                "indexes": [
                    models.Index(
                        fields=["ip_address", "attempted_at"],
                        name="accounts_lo_ip_addr_8e6499_idx",
                    ),
                    models.Index(
                        fields=["username_attempted", "attempted_at"],
                        name="accounts_lo_usernam_bc2076_idx",
                    ),
                ],
            },
        ),
    ]
