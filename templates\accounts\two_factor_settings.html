{% extends 'base.html' %}

{% block title %}Two-Factor Authentication Settings - ZBet{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header zbet-primary text-white text-center">
                <h3 class="mb-0">Two-Factor Authentication</h3>
                <p class="mb-0">Manage your 2FA settings</p>
            </div>
            
            <div class="card-body">
                <!-- 2FA Status -->
                <div class="text-center mb-4">
                    {% if status.enabled %}
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h5 class="text-success">2FA Enabled</h5>
                        <p class="text-muted">
                            Your account is protected with two-factor authentication.
                        </p>
                    {% else %}
                        <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                        <h5 class="text-warning">2FA Disabled</h5>
                        <p class="text-muted">
                            Your account is not protected with two-factor authentication.
                        </p>
                    {% endif %}
                </div>
                
                {% if status.enabled %}
                    <!-- Current Settings -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6><i class="fas fa-cog"></i> Current Settings</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Method:</strong> 
                                        {% if status.method == 'sms' %}
                                            <span class="badge bg-primary">SMS</span>
                                        {% elif status.method == 'totp' %}
                                            <span class="badge bg-success">Authenticator App</span>
                                        {% else %}
                                            <span class="badge bg-info">Both</span>
                                        {% endif %}
                                    </p>
                                    <p><strong>TOTP Status:</strong> 
                                        {% if status.totp_verified %}
                                            <span class="text-success"><i class="fas fa-check"></i> Verified</span>
                                        {% else %}
                                            <span class="text-warning"><i class="fas fa-times"></i> Not Verified</span>
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Enabled:</strong> {{ status.enabled_at|date:"M d, Y" }}</p>
                                    {% if status.last_used %}
                                        <p><strong>Last Used:</strong> {{ status.last_used|date:"M d, Y H:i" }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Backup Codes -->
                    {% if backup_info %}
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6><i class="fas fa-key"></i> Backup Codes</h6>
                            <p class="text-muted">
                                Backup codes can be used if you lose access to your primary 2FA method.
                            </p>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Total Codes:</strong> {{ backup_info.total }}</p>
                                    <p><strong>Used:</strong> {{ backup_info.used }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Remaining:</strong> 
                                        <span class="{% if backup_info.remaining < 3 %}text-danger{% elif backup_info.remaining < 5 %}text-warning{% else %}text-success{% endif %}">
                                            {{ backup_info.remaining }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                            
                            {% if backup_info.remaining < 3 %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    You're running low on backup codes. Consider regenerating them.
                                </div>
                            {% endif %}
                            
                            <div class="d-grid gap-2 d-md-flex">
                                <a href="{% url 'accounts:view_backup_codes' %}" class="btn btn-outline-primary">
                                    View Codes
                                </a>
                                <a href="{% url 'accounts:regenerate_backup_codes' %}" class="btn btn-outline-warning">
                                    Regenerate Codes
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Actions -->
                    <div class="d-grid gap-2">
                        {% if not status.totp_verified and status.method in 'totp,both' %}
                            <a href="{% url 'accounts:setup_totp' %}" class="btn btn-warning">
                                <i class="fas fa-mobile-alt"></i> Complete TOTP Setup
                            </a>
                        {% endif %}
                        
                        <a href="{% url 'accounts:disable_2fa' %}" class="btn btn-outline-danger">
                            <i class="fas fa-times"></i> Disable 2FA
                        </a>
                    </div>
                    
                {% else %}
                    <!-- Enable 2FA -->
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Security Recommendation</h6>
                        <p class="mb-0">
                            Enable two-factor authentication to protect your account and funds from unauthorized access.
                        </p>
                    </div>
                    
                    <div class="d-grid">
                        <a href="{% url 'accounts:enable_2fa' %}" class="btn btn-zbet btn-lg">
                            <i class="fas fa-shield-alt"></i> Enable Two-Factor Authentication
                        </a>
                    </div>
                {% endif %}
                
                <hr>
                
                <div class="text-center">
                    <a href="{% url 'accounts:dashboard' %}" class="btn btn-outline-secondary">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Security Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb"></i> Security Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success"></i> Keep your backup codes in a safe place</li>
                    <li><i class="fas fa-check text-success"></i> Don't share your 2FA codes with anyone</li>
                    <li><i class="fas fa-check text-success"></i> Use a reputable authenticator app</li>
                    <li><i class="fas fa-check text-success"></i> Regenerate backup codes if you suspect they're compromised</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
