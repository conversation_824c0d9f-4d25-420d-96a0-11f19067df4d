from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg
from django.contrib import messages
from decimal import Decimal
import json
import uuid
import random

from .models import (
    Game, GameCategory, GameProvider, GameSession, GameResult,
    ProgressiveJackpot, JackpotWin, GameStatistics
)
from payments.models import Wallet


def casino_home(request):
    """Casino homepage with featured games and categories"""
    # Get featured games
    featured_games = Game.objects.filter(
        is_active=True, is_featured=True, status='ACTIVE'
    ).select_related('provider', 'category')[:12]

    # Get popular games
    popular_games = Game.objects.filter(
        is_active=True, is_popular=True, status='ACTIVE'
    ).select_related('provider', 'category')[:12]

    # Get new games
    new_games = Game.objects.filter(
        is_active=True, is_new=True, status='ACTIVE'
    ).select_related('provider', 'category')[:12]

    # Get game categories
    categories = GameCategory.objects.filter(is_active=True)

    # Get active jackpots
    jackpots = ProgressiveJackpot.objects.filter(
        status='ACTIVE', is_featured=True
    ).order_by('-current_amount')[:5]

    # Get providers
    providers = GameProvider.objects.filter(status='ACTIVE')

    context = {
        'featured_games': featured_games,
        'popular_games': popular_games,
        'new_games': new_games,
        'categories': categories,
        'jackpots': jackpots,
        'providers': providers,
    }
    return render(request, 'casino/home.html', context)


def games_by_category(request, category_slug):
    """Display games by category"""
    category = get_object_or_404(GameCategory, slug=category_slug, is_active=True)

    games = Game.objects.filter(
        category=category, is_active=True, status='ACTIVE'
    ).select_related('provider').order_by('-is_featured', '-play_count')

    # Filter by provider if specified
    provider_id = request.GET.get('provider')
    if provider_id:
        games = games.filter(provider_id=provider_id)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        games = games.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    providers = GameProvider.objects.filter(
        status='ACTIVE', games__category=category
    ).distinct()

    context = {
        'category': category,
        'games': games,
        'providers': providers,
        'current_provider': provider_id,
        'search_query': search_query,
    }
    return render(request, 'casino/games_by_category.html', context)


def game_detail(request, game_slug):
    """Game detail page"""
    game = get_object_or_404(
        Game, slug=game_slug, is_active=True, status='ACTIVE'
    )

    # Get related games
    related_games = Game.objects.filter(
        category=game.category, is_active=True, status='ACTIVE'
    ).exclude(id=game.id).select_related('provider')[:6]

    # Get game jackpots
    jackpots = game.jackpots.filter(status='ACTIVE')

    context = {
        'game': game,
        'related_games': related_games,
        'jackpots': jackpots,
    }
    return render(request, 'casino/game_detail.html', context)


@login_required
def launch_game(request, game_slug):
    """Launch a casino game"""
    game = get_object_or_404(
        Game, slug=game_slug, is_active=True, status='ACTIVE'
    )

    # Check if demo mode
    is_demo = request.GET.get('demo', 'false').lower() == 'true'

    if not is_demo:
        # Check user wallet balance
        try:
            wallet = Wallet.objects.get(user=request.user)
            if wallet.balance < game.min_bet:
                messages.error(request, f'Insufficient balance. Minimum bet is KES {game.min_bet}')
                return redirect('casino:game_detail', game_slug=game.slug)
        except Wallet.DoesNotExist:
            messages.error(request, 'Wallet not found. Please contact support.')
            return redirect('casino:game_detail', game_slug=game.slug)

    # Create game session
    session_token = str(uuid.uuid4())
    initial_balance = wallet.balance if not is_demo else Decimal('1000.00')

    game_session = GameSession.objects.create(
        user=request.user,
        game=game,
        session_token=session_token,
        is_demo=is_demo,
        initial_balance=initial_balance,
        final_balance=initial_balance,
        ip_address=request.META.get('REMOTE_ADDR'),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        device_type=get_device_type(request)
    )

    # Increment play count
    game.increment_play_count()

    # Redirect to game launcher
    context = {
        'game': game,
        'session': game_session,
        'is_demo': is_demo,
        'wallet_balance': wallet.balance if not is_demo else Decimal('1000.00'),
    }

    # Route to specific game type
    if game.game_type == 'SLOT':
        return render(request, 'casino/games/slot_game.html', context)
    elif game.game_type == 'BLACKJACK':
        return render(request, 'casino/games/blackjack.html', context)
    elif game.game_type == 'ROULETTE':
        return render(request, 'casino/games/roulette.html', context)
    elif game.game_type == 'AVIATOR':
        return render(request, 'casino/games/aviator.html', context)
    elif game.game_type == 'SPIN_WIN':
        return render(request, 'casino/games/spin_win.html', context)
    else:
        return render(request, 'casino/games/generic_game.html', context)


def get_device_type(request):
    """Determine device type from user agent"""
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
        return 'mobile'
    elif 'tablet' in user_agent or 'ipad' in user_agent:
        return 'tablet'
    else:
        return 'desktop'


# Game API Endpoints
@csrf_exempt
@require_http_methods(["POST"])
@login_required
def place_bet(request):
    """API endpoint for placing bets in casino games"""
    try:
        data = json.loads(request.body)
        session_token = data.get('session_token')
        bet_amount = Decimal(str(data.get('bet_amount', 0)))
        game_data = data.get('game_data', {})

        # Get game session
        session = get_object_or_404(
            GameSession,
            session_token=session_token,
            user=request.user,
            status='ACTIVE'
        )

        # Validate bet amount
        if bet_amount < session.game.min_bet or bet_amount > session.game.max_bet:
            return JsonResponse({
                'success': False,
                'error': f'Bet amount must be between KES {session.game.min_bet} and KES {session.game.max_bet}'
            })

        # Check balance (for real money games)
        if not session.is_demo:
            wallet = Wallet.objects.get(user=request.user)
            if wallet.balance < bet_amount:
                return JsonResponse({
                    'success': False,
                    'error': 'Insufficient balance'
                })

        # Generate game result
        result = generate_game_result(session.game, bet_amount, game_data)

        # Create game result record
        game_result = GameResult.objects.create(
            session=session,
            user=request.user,
            game=session.game,
            round_id=str(uuid.uuid4()),
            result_type=result['type'],
            bet_amount=bet_amount,
            win_amount=result['win_amount'],
            net_result=result['win_amount'] - bet_amount,
            game_data=result['game_data'],
            multiplier=result.get('multiplier', 1),
            triggered_bonus=result.get('triggered_bonus', False),
            free_spins_awarded=result.get('free_spins', 0)
        )

        # Update session statistics
        session.total_bet_amount += bet_amount
        session.total_win_amount += result['win_amount']
        session.net_result = session.total_win_amount - session.total_bet_amount
        session.rounds_played += 1
        session.max_bet = max(session.max_bet, bet_amount)
        session.max_win = max(session.max_win, result['win_amount'])
        session.save()

        # Update game statistics
        session.game.total_bets += bet_amount
        session.game.total_wins += result['win_amount']
        session.game.save(update_fields=['total_bets', 'total_wins'])

        # Handle wallet transactions (for real money games)
        new_balance = None
        if not session.is_demo:
            # Debit bet amount
            wallet.debit(bet_amount, f'Casino bet - {session.game.name}')

            # Credit win amount
            if result['win_amount'] > 0:
                wallet.credit(result['win_amount'], f'Casino win - {session.game.name}')

            new_balance = wallet.balance

            # Check for jackpot contributions
            handle_jackpot_contributions(session.game, bet_amount)

            # Check for jackpot triggers
            jackpot_win = check_jackpot_trigger(session.game, request.user, bet_amount)
            if jackpot_win:
                result['jackpot_win'] = {
                    'amount': float(jackpot_win.amount),
                    'jackpot_name': jackpot_win.jackpot.name
                }
                # Credit jackpot win to wallet
                wallet.credit(jackpot_win.amount, f'Jackpot win - {jackpot_win.jackpot.name}')
                new_balance = wallet.balance

        return JsonResponse({
            'success': True,
            'result': {
                'type': result['type'],
                'win_amount': float(result['win_amount']),
                'net_result': float(result['win_amount'] - bet_amount),
                'game_data': result['game_data'],
                'multiplier': float(result.get('multiplier', 1)),
                'triggered_bonus': result.get('triggered_bonus', False),
                'free_spins': result.get('free_spins', 0),
                'jackpot_win': result.get('jackpot_win'),
            },
            'session': {
                'total_bet': float(session.total_bet_amount),
                'total_win': float(session.total_win_amount),
                'net_result': float(session.net_result),
                'rounds_played': session.rounds_played,
            },
            'balance': float(new_balance) if new_balance is not None else None
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def generate_game_result(game, bet_amount, game_data):
    """Generate game result based on game type and RTP"""
    # Simple RTP-based result generation
    # In production, this would integrate with actual game providers

    rtp = game.rtp_percentage / 100
    random_value = random.random()

    # Determine if it's a win based on RTP
    if random_value < rtp:
        # It's a win - calculate win amount
        if game.game_type == 'SLOT':
            return generate_slot_result(bet_amount, game_data)
        elif game.game_type == 'BLACKJACK':
            return generate_blackjack_result(bet_amount, game_data)
        elif game.game_type == 'ROULETTE':
            return generate_roulette_result(bet_amount, game_data)
        elif game.game_type == 'AVIATOR':
            return generate_aviator_result(bet_amount, game_data)
        else:
            # Generic win
            multiplier = random.uniform(1.1, 5.0)
            return {
                'type': 'WIN',
                'win_amount': bet_amount * Decimal(str(multiplier)),
                'multiplier': multiplier,
                'game_data': {'result': 'win', 'multiplier': multiplier}
            }
    else:
        # It's a loss
        return {
            'type': 'LOSS',
            'win_amount': Decimal('0'),
            'multiplier': 0,
            'game_data': {'result': 'loss'}
        }


def generate_slot_result(bet_amount, game_data):
    """Generate slot game result"""
    symbols = ['🍒', '🍋', '🍊', '🍇', '🔔', '⭐', '💎']
    reels = []

    # Generate 5 reels with 3 symbols each
    for _ in range(5):
        reel = [random.choice(symbols) for _ in range(3)]
        reels.append(reel)

    # Check for winning combinations
    win_amount = Decimal('0')
    multiplier = 1

    # Simple payline check (middle row)
    middle_line = [reel[1] for reel in reels]

    # Count consecutive matching symbols from left
    if len(set(middle_line)) == 1:  # All 5 match
        multiplier = 50
    elif len(set(middle_line[:4])) == 1:  # First 4 match
        multiplier = 20
    elif len(set(middle_line[:3])) == 1:  # First 3 match
        multiplier = 5

    if multiplier > 1:
        win_amount = bet_amount * multiplier
        result_type = 'WIN'
    else:
        result_type = 'LOSS'

    return {
        'type': result_type,
        'win_amount': win_amount,
        'multiplier': multiplier,
        'game_data': {
            'reels': reels,
            'winning_line': middle_line if multiplier > 1 else None,
            'payline': 'middle'
        }
    }


def generate_blackjack_result(bet_amount, game_data):
    """Generate blackjack result"""
    # Simplified blackjack simulation
    player_cards = [random.randint(1, 11), random.randint(1, 11)]
    dealer_cards = [random.randint(1, 11), random.randint(1, 11)]

    player_total = sum(player_cards)
    dealer_total = sum(dealer_cards)

    # Adjust for aces
    if player_total > 21 and 11 in player_cards:
        player_total -= 10
    if dealer_total > 21 and 11 in dealer_cards:
        dealer_total -= 10

    # Determine winner
    if player_total > 21:
        result_type = 'LOSS'
        multiplier = 0
    elif dealer_total > 21 or player_total > dealer_total:
        result_type = 'WIN'
        multiplier = 2 if player_total == 21 else 1.5
    elif player_total == dealer_total:
        result_type = 'WIN'
        multiplier = 1  # Push - return bet
    else:
        result_type = 'LOSS'
        multiplier = 0

    win_amount = bet_amount * Decimal(str(multiplier))

    return {
        'type': result_type,
        'win_amount': win_amount,
        'multiplier': multiplier,
        'game_data': {
            'player_cards': player_cards,
            'dealer_cards': dealer_cards,
            'player_total': player_total,
            'dealer_total': dealer_total
        }
    }


def generate_roulette_result(bet_amount, game_data):
    """Generate roulette result"""
    # European roulette (0-36)
    winning_number = random.randint(0, 36)
    bet_type = game_data.get('bet_type', 'number')
    bet_value = game_data.get('bet_value')

    win_amount = Decimal('0')
    multiplier = 0

    if bet_type == 'number' and bet_value == winning_number:
        multiplier = 35  # Straight up bet
    elif bet_type == 'red' and winning_number in [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36]:
        multiplier = 1
    elif bet_type == 'black' and winning_number in [2,4,6,8,10,11,13,15,17,20,22,24,26,28,29,31,33,35]:
        multiplier = 1
    elif bet_type == 'even' and winning_number % 2 == 0 and winning_number != 0:
        multiplier = 1
    elif bet_type == 'odd' and winning_number % 2 == 1:
        multiplier = 1

    if multiplier > 0:
        win_amount = bet_amount * multiplier
        result_type = 'WIN'
    else:
        result_type = 'LOSS'

    return {
        'type': result_type,
        'win_amount': win_amount,
        'multiplier': multiplier,
        'game_data': {
            'winning_number': winning_number,
            'bet_type': bet_type,
            'bet_value': bet_value
        }
    }


def generate_aviator_result(bet_amount, game_data):
    """Generate Aviator game result"""
    # Aviator multiplier simulation
    crash_point = random.uniform(1.0, 10.0)
    cash_out_point = game_data.get('cash_out', None)

    if cash_out_point and cash_out_point < crash_point:
        # Player cashed out before crash
        multiplier = cash_out_point
        win_amount = bet_amount * Decimal(str(multiplier))
        result_type = 'WIN'
    else:
        # Plane crashed before cash out
        multiplier = 0
        win_amount = Decimal('0')
        result_type = 'LOSS'

    return {
        'type': result_type,
        'win_amount': win_amount,
        'multiplier': multiplier,
        'game_data': {
            'crash_point': crash_point,
            'cash_out_point': cash_out_point,
            'crashed': cash_out_point is None or cash_out_point >= crash_point
        }
    }


# Jackpot Functions
def handle_jackpot_contributions(game, bet_amount):
    """Handle jackpot contributions from bets"""
    jackpots = game.jackpots.filter(status='ACTIVE')

    for jackpot in jackpots:
        contribution = jackpot.add_contribution(bet_amount)

        # Update daily statistics
        today = timezone.now().date()
        stats, created = GameStatistics.objects.get_or_create(
            game=game,
            date=today,
            defaults={'jackpot_contributions': contribution}
        )
        if not created:
            stats.jackpot_contributions += contribution
            stats.save(update_fields=['jackpot_contributions'])


def check_jackpot_trigger(game, user, bet_amount):
    """Check if any jackpots should be triggered"""
    jackpots = game.jackpots.filter(
        status='ACTIVE',
        min_bet_to_qualify__lte=bet_amount
    )

    for jackpot in jackpots:
        # Check trigger probability
        if random.random() < float(jackpot.trigger_probability):
            # Jackpot triggered!
            winning_amount = jackpot.trigger_jackpot(user)

            # Create jackpot win record
            jackpot_win = JackpotWin.objects.create(
                jackpot=jackpot,
                winner=user,
                game=game,
                amount=winning_amount,
                bet_amount=bet_amount,
                is_verified=True  # Auto-verify for now
            )

            # Reset jackpot
            jackpot.reset_jackpot()

            # Update daily statistics
            today = timezone.now().date()
            stats, created = GameStatistics.objects.get_or_create(
                game=game,
                date=today,
                defaults={'jackpots_won': 1, 'jackpot_payouts': winning_amount}
            )
            if not created:
                stats.jackpots_won += 1
                stats.jackpot_payouts += winning_amount
                stats.save(update_fields=['jackpots_won', 'jackpot_payouts'])

            return jackpot_win

    return None


@login_required
def end_game_session(request, session_token):
    """End a game session"""
    session = get_object_or_404(
        GameSession,
        session_token=session_token,
        user=request.user,
        status='ACTIVE'
    )

    session.end_session()

    if not session.is_demo:
        # Update final balance
        wallet = Wallet.objects.get(user=request.user)
        session.final_balance = wallet.balance
        session.save(update_fields=['final_balance'])

    return JsonResponse({
        'success': True,
        'session_summary': {
            'total_bet': float(session.total_bet_amount),
            'total_win': float(session.total_win_amount),
            'net_result': float(session.net_result),
            'rounds_played': session.rounds_played,
            'duration': str(session.duration),
        }
    })


def jackpot_display(request):
    """API endpoint for jackpot display widget"""
    jackpots = ProgressiveJackpot.objects.filter(
        status='ACTIVE', is_featured=True
    ).order_by('-current_amount')[:5]

    jackpot_data = []
    for jackpot in jackpots:
        jackpot_data.append({
            'id': str(jackpot.id),
            'name': jackpot.name,
            'current_amount': float(jackpot.current_amount),
            'type': jackpot.jackpot_type,
            'games_count': jackpot.games.count(),
        })

    return JsonResponse({
        'success': True,
        'jackpots': jackpot_data
    })


def jackpot_winners(request):
    """Display recent jackpot winners"""
    recent_wins = JackpotWin.objects.filter(
        is_verified=True
    ).select_related('winner', 'jackpot', 'game').order_by('-won_at')[:10]

    context = {
        'recent_wins': recent_wins,
    }
    return render(request, 'casino/jackpot_winners.html', context)
