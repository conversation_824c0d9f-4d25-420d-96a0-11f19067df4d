"""
URL configuration for zbet_platform project.


"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path("admin/", admin.site.urls),
    path("accounts/", include('accounts.urls')),
    path("accounts/", include('allauth.urls')),  # Django Allauth URLs
    path("sports/", include('sports.urls')),  # Sports management URLs
    path("payments/", include('payments.urls')),  # Payment system URLs
    path("casino/", include('casino.urls')),  # Casino games URLs
    path("promotions/", include('promotions.urls')),  # Promotions and bonuses URLs
    path("", include('betting.urls')),  # Betting URLs (including homepage)
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
